<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\BusinessSetting;

class UserChatTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;
    protected $seller;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->customer = User::factory()->create([
            'user_type' => 'customer'
        ]);

        $this->seller = User::factory()->create([
            'user_type' => 'seller'
        ]);

        // 创建测试产品
        $this->product = Product::factory()->create([
            'user_id' => $this->seller->id,
            'published' => 1,
            'approved' => 1
        ]);

        // 启用对话系统
        BusinessSetting::updateOrCreate(
            ['type' => 'conversation_system'],
            ['value' => '1']
        );
    }

    /** @test */
    public function user_can_view_chat_index()
    {
        $response = $this->actingAs($this->customer)
            ->get(route('user.chat.index'));

        $response->assertStatus(200);
        $response->assertViewIs('frontend.user.chat.index');
    }

    /** @test */
    public function user_can_create_conversation_from_product()
    {
        $response = $this->actingAs($this->customer)
            ->post(route('user.chat.create-from-product'), [
                'product_id' => $this->product->id,
                'message' => 'Hello, I am interested in this product.'
            ]);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('conversations', [
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'product_id' => $this->product->id
        ]);

        $this->assertDatabaseHas('messages', [
            'user_id' => $this->customer->id,
            'message' => 'Hello, I am interested in this product.',
            'message_type' => 'text'
        ]);
    }

    /** @test */
    public function user_can_send_message_in_conversation()
    {
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => $this->product->name,
            'product_id' => $this->product->id,
            'last_message_at' => now()
        ]);

        $response = $this->actingAs($this->customer)
            ->postJson(route('user.chat.send'), [
                'conversation_id' => $conversation->id,
                'message' => 'What is the shipping cost?'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('messages', [
            'conversation_id' => $conversation->id,
            'user_id' => $this->customer->id,
            'message' => 'What is the shipping cost?',
            'message_type' => 'text'
        ]);
    }

    /** @test */
    public function user_can_share_product_in_conversation()
    {
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => 'Test Conversation',
            'last_message_at' => now()
        ]);

        $response = $this->actingAs($this->customer)
            ->postJson(route('user.chat.share-product'), [
                'conversation_id' => $conversation->id,
                'product_id' => $this->product->id
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('messages', [
            'conversation_id' => $conversation->id,
            'user_id' => $this->customer->id,
            'message_type' => 'product'
        ]);
    }

    /** @test */
    public function user_can_get_unread_message_count()
    {
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => 'Test Conversation',
            'last_message_at' => now()
        ]);

        // 创建未读消息
        Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => $this->seller->id,
            'message' => 'Hello from seller',
            'message_type' => 'text',
            'is_read' => false
        ]);

        $response = $this->actingAs($this->customer)
            ->get(route('notifications.chat.unread-count'));

        $response->assertStatus(200);
        $response->assertJson(['count' => 1]);
    }

    /** @test */
    public function conversation_marks_messages_as_read_when_viewed()
    {
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => 'Test Conversation',
            'last_message_at' => now()
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => $this->seller->id,
            'message' => 'Hello from seller',
            'message_type' => 'text',
            'is_read' => false
        ]);

        $response = $this->actingAs($this->customer)
            ->get(route('user.chat.show', encrypt($conversation->id)));

        $response->assertStatus(200);

        $this->assertDatabaseHas('messages', [
            'id' => $message->id,
            'is_read' => true
        ]);
    }

    /** @test */
    public function unauthorized_user_cannot_access_others_conversation()
    {
        $otherUser = User::factory()->create(['user_type' => 'customer']);
        
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => 'Private Conversation',
            'last_message_at' => now()
        ]);

        $response = $this->actingAs($otherUser)
            ->get(route('user.chat.show', encrypt($conversation->id)));

        $response->assertStatus(403);
    }

    /** @test */
    public function api_returns_conversation_list()
    {
        $conversation = Conversation::create([
            'sender_id' => $this->customer->id,
            'receiver_id' => $this->seller->id,
            'title' => 'API Test Conversation',
            'product_id' => $this->product->id,
            'last_message_at' => now()
        ]);

        $response = $this->actingAs($this->customer, 'sanctum')
            ->getJson('/api/v2/user-chat/conversations');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'title',
                    'other_user',
                    'product',
                    'last_message',
                    'unread_count',
                    'updated_at'
                ]
            ],
            'pagination'
        ]);
    }
}

<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Shop;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class SellerApplicationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_reapply_after_rejection()
    {
        // 创建一个普通用户
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 1, // 普通客户状态
        ]);

        // 创建一个商店申请
        $shop = Shop::create([
            'user_id' => $user->id,
            'name' => 'Test Shop',
            'address' => 'Test Address',
            'verification_status' => 0, // 待审核
        ]);

        // 用户申请时状态应该变为2
        $user->status = 2;
        $user->save();

        // 模拟管理员拒绝申请
        $shop->verification_status = -1; // 拒绝
        $shop->rejection_reason = 'Test rejection reason';
        $shop->save();

        // 模拟拒绝时的用户状态重置（这是我们修复的部分）
        $user->status = 1; // 重置为普通客户状态
        $user->save();

        // 验证用户状态已重置
        $this->assertEquals(1, $user->fresh()->status);
        $this->assertEquals('customer', $user->fresh()->user_type);

        // 验证商店状态为拒绝
        $this->assertEquals(-1, $shop->fresh()->verification_status);

        // 现在用户应该能够重新申请
        $user->status = 2; // 重新申请
        $user->save();

        $shop->verification_status = 0; // 重新设置为待审核
        $shop->rejection_reason = null;
        $shop->save();

        // 验证重新申请成功
        $this->assertEquals(2, $user->fresh()->status);
        $this->assertEquals(0, $shop->fresh()->verification_status);
        $this->assertNull($shop->fresh()->rejection_reason);
    }

    /** @test */
    public function user_cannot_apply_when_already_applied()
    {
        // 创建一个已申请的用户
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 2, // 已申请状态
        ]);

        // 验证用户不能重复申请
        $this->assertEquals(2, $user->status);

        // 这种情况下，系统应该阻止重复申请
        // 在实际的控制器逻辑中，会返回错误消息
    }

    /** @test */
    public function rejected_user_can_access_verification_form()
    {
        // 创建一个被拒绝的用户
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'customer',
            'status' => 1, // 重置为普通客户状态
        ]);

        // 创建一个被拒绝的商店
        $shop = Shop::create([
            'user_id' => $user->id,
            'name' => 'Test Shop',
            'address' => 'Test Address',
            'verification_status' => -1, // 被拒绝
            'verification_info' => json_encode(['test' => 'data']), // 有申请信息
            'rejection_reason' => 'Test rejection reason',
        ]);

        // 验证用户状态
        $this->assertEquals(1, $user->status);
        $this->assertEquals('customer', $user->user_type);
        $this->assertEquals(-1, $shop->verification_status);

        // 被拒绝的用户应该能够访问申请表单
        // 这在实际的verify_form方法中会被允许
        $this->assertTrue($shop->verification_status == -1);
        $this->assertNotNull($shop->verification_info);
    }
}

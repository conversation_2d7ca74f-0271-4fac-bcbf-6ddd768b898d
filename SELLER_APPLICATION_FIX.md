# 卖家申请重新申请功能修复

## 问题描述

用户申请成为卖家被后台拒绝后，无法重新申请，系统提示"You have already applied. Please do not apply again"。

## 问题原因

当用户申请成为卖家时，用户的`status`字段被设置为2（已申请状态）。当申请被拒绝时，系统没有正确重置用户状态，导致用户无法重新申请。

## 用户状态说明

- `user.status = 1`: 普通客户，可以申请成为卖家
- `user.status = 2`: 已申请成为卖家，等待审核
- `user.status = 3`: 已通过审核，成为卖家

## 商店验证状态说明

- `shop.verification_status = 0`: 待审核
- `shop.verification_status = 1`: 已通过
- `shop.verification_status = -1`: 已拒绝

## 修复内容

### 1. SellerController.php - updateApproved方法
**文件**: `app/Http/Controllers/SellerController.php`
**修改**: 添加对拒绝状态的处理

```php
} else if ($request->status == -1) {
    // 当申请被拒绝时，将用户状态重置为1，允许重新申请
    $user->user_type = "customer";
    $user->status = 1;
    $user->save();
}
```

### 2. SellerController.php - reject_seller_with_reason方法
**文件**: `app/Http/Controllers/SellerController.php`
**修改**: 在拒绝申请时重置用户状态

```php
// 当申请被拒绝时，将用户状态重置为1，允许重新申请
$user = $shop->user;
$user->user_type = "customer";
$user->status = 1;
$user->save();
```

### 3. SellerController.php - reject_seller方法
**文件**: `app/Http/Controllers/SellerController.php`
**修改**: 在重置申请状态时也重置用户状态

```php
// 将用户状态重置为1，允许重新申请
$user = $shop->user;
$user->user_type = "customer";
$user->status = 1;
$user->save();
```

### 4. Seller/ShopController.php - 重新提交方法
**文件**: `app/Http/Controllers/Seller/ShopController.php`
**修改**: 重新提交申请时更新用户状态

```php
// 重新提交申请时，将用户状态设置为2（申请中）
$user = Auth::user();
$user->status = 2;
$user->save();
```

### 5. IsSeller中间件 - 允许被拒绝用户访问
**文件**: `app/Http/Middleware/IsSeller.php`
**修改**: 允许有商店但被拒绝的用户访问重新申请页面

```php
// 特殊处理：允许有商店但被拒绝的用户访问重新申请页面
if (Auth::check() && Auth::user()->user_type == 'customer' && Auth::user()->shop) {
    $shop = Auth::user()->shop;
    // 如果商店被拒绝(-1)或待审核(0)，且访问的是申请验证相关页面，则允许访问
    if (($shop->verification_status == -1 || $shop->verification_status == 0) &&
        (str_contains($request->path(), 'apply_for_verification') ||
         str_contains($request->path(), 'verification_info_store') ||
         str_contains($request->path(), 'dashboard'))) {
        return $next($request);
    }
}
```

### 6. Seller/ShopController.php - verify_form方法
**文件**: `app/Http/Controllers/Seller/ShopController.php`
**修改**: 允许被拒绝的用户重新访问申请表单

```php
// 允许以下情况访问申请表单：
// 1. 从未提交过申请 (verification_info == null)
// 2. 申请被拒绝 (verification_status == -1)
if ($shop->verification_info == null || $shop->verification_status == -1) {
    return view('seller.verify_form', compact('shop'));
}
```

### 7. Seller/ShopController.php - 重新申请后的跳转
**文件**: `app/Http/Controllers/Seller/ShopController.php`
**修改**: 重新申请后跳转到普通用户dashboard

```php
// 重新申请后跳转到普通用户dashboard，因为此时用户还是customer身份
return redirect()->route('dashboard');
```

### 8. ShopController.php - 统一申请流程
**文件**: `app/Http/Controllers/ShopController.php`
**修改**: 允许被拒绝的用户通过首次申请流程重新申请，并统一跳转逻辑

```php
// 检查用户是否有被拒绝的商店，如果有则允许重新申请
$user = Auth::user();
if ($user->shop && $user->shop->verification_status == -1) {
    // 用户有被拒绝的商店，允许重新申请
    return view('frontend.seller_form');
}

// 重新申请：更新现有商店
if($existingShop && $existingShop->verification_status == -1){
    $shop = $existingShop;
    $shop->verification_status = 0; // 重置为待审核
    $shop->rejection_reason = null; // 清除拒绝原因
}

// 统一跳转逻辑：移除首次申请的提前返回，让所有申请都走相同的跳转路径
if($user->status==1 && $user->user_type == 'customer'){
    // 首次申请和重新申请都继续处理，不提前返回
    $user->status = 2;
    $user->save();
}
```

### 9. 用户侧边栏 - Seller Panel访问控制
**文件**: `resources/views/frontend/inc/user_side_nav.blade.php`
**修改**: 根据用户状态显示不同的Seller Panel链接

```php
@if(Auth::user()->user_type == 'seller')
    {{-- 已通过审核的卖家，显示正常的Seller Panel链接 --}}
    <li class="aiz-side-nav-item">
        <a href="{{ route('seller.dashboard') }}" class="aiz-side-nav-link">
            <i class="las la-store-alt aiz-side-nav-icon"></i>
            <span class="aiz-side-nav-text">{{ translate('Seller Panel') }}</span> </a>
    </li>
@elseif(Auth::user()->shop && (Auth::user()->shop->verification_status == 0 || Auth::user()->shop->verification_status == -1))
    {{-- 有商店但未通过审核的用户，显示审核状态提示 --}}
    <li class="aiz-side-nav-item">
        <a href="javascript:void(0);" class="aiz-side-nav-link" onclick="showVerificationModal()">
            <i class="las la-store-alt aiz-side-nav-icon text-warning"></i>
            <span class="aiz-side-nav-text text-warning">{{ translate('Seller Panel') }}</span>
            <small class="badge badge-warning ml-1">{{ translate('Reviewing') }}</small>
        </a>
    </li>
@endif
```

### 10. IsSeller中间件 - 严格访问控制
**文件**: `app/Http/Middleware/IsSeller.php`
**修改**: 移除未审核用户的特殊访问权限

```php
// 仅允许已通过审核的卖家访问卖家后台
if (Auth::check() && Auth::user()->user_type == 'seller' && !Auth::user()->banned) {
    return $next($request);
}

// 移除特殊处理：不再允许未通过审核的用户访问seller路由
```

## 修复后的流程

### 统一申请流程
现在首次申请和重新申请都使用相同的流程和表单：

1. **用户首次申请**:
   - 访问 `/shops/create` 页面，填写完整申请表单
   - `user.status` 从 1 变为 2，`shop.verification_status` 设为 0
   - 跳转到普通用户dashboard，显示"申请审核中"

2. **申请被拒绝**:
   - `user.status` 从 2 重置为 1，`shop.verification_status` 设为 -1
   - 用户在dashboard看到拒绝原因

3. **用户重新申请**:
   - 访问 `/shops/create` 页面（与首次申请相同），填写完整申请表单
   - `user.status` 从 1 变为 2，`shop.verification_status` 从 -1 重置为 0
   - 跳转到普通用户dashboard，显示"申请审核中"

4. **申请通过**:
   - `user.status` 从 2 变为 3，`user.user_type` 变为 "seller"
   - 自动跳转到卖家dashboard

### Seller Panel访问控制
现在根据用户状态显示不同的Seller Panel：

1. **已通过审核的卖家** (`user_type == 'seller'`):
   - 显示正常的"Seller Panel"链接
   - 可以正常访问卖家后台

2. **未通过审核的用户** (有商店但`verification_status != 1`):
   - 显示带警告标识的"Seller Panel"链接
   - 显示"Reviewing"徽章
   - 点击时弹出审核状态提示
   - 无法访问卖家后台路由

3. **普通用户** (无商店):
   - 不显示"Seller Panel"链接

## 关键修复点

### 状态重置逻辑
- 所有拒绝申请的方法都会将用户状态重置为1
- 重新提交申请时会将用户状态设置为2

### 权限控制
- `IsSeller`中间件允许被拒绝的用户访问重新申请相关页面
- `verify_form`方法允许被拒绝的用户重新访问申请表单

### 用户体验
- 被拒绝的用户会看到拒绝原因和重新申请的提示
- 重新申请流程与初次申请完全一致

## 测试验证

创建了测试文件 `tests/Feature/SellerApplicationTest.php` 来验证修复功能：

- 测试用户被拒绝后可以重新申请
- 测试用户在已申请状态下不能重复申请

## 影响范围

此修复只影响卖家申请流程，不会影响其他功能：

- 已通过审核的卖家不受影响
- 正在审核中的申请不受影响
- 其他用户类型（admin、salesman）不受影响

## 部署说明

此修复只涉及代码更改，无需数据库迁移或其他配置更改。可以直接部署到生产环境。

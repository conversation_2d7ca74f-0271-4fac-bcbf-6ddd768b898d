# 用户与卖家对话系统

## 功能概述

这个系统实现了用户与卖家之间的实时对话功能，类似淘宝的客服对话系统。主要功能包括：

### 🚀 核心功能

1. **实时对话** - 用户可以与卖家进行实时聊天
2. **产品分享** - 一键分享产品信息到对话中
3. **消息通知** - 实时显示未读消息数量
4. **邮件通知** - 新消息自动发送邮件提醒
5. **对话历史** - 完整的对话记录管理
6. **多媒体支持** - 支持文本、产品卡片、图片等消息类型

### 📱 用户界面

- **产品详情页** - 增强的"Chat with Seller"按钮
- **对话列表** - 显示所有与卖家的对话
- **对话详情** - 现代化的聊天界面
- **实时通知** - 侧边栏显示未读消息数量

## 🛠 技术实现

### 数据库结构

#### 扩展的 conversations 表
```sql
- product_id (关联的产品ID)
- is_tip (是否已提示)
- last_message_at (最后消息时间)
```

#### 扩展的 messages 表
```sql
- message_type (消息类型: text, product, image, file)
- message_data (JSON格式的消息数据)
- attachment_url (附件URL)
- is_read (是否已读)
```

### 主要文件

#### 控制器
- `app/Http/Controllers/UserChatController.php` - 主要聊天控制器
- `app/Http/Controllers/Api/V2/UserChatController.php` - API控制器
- `app/Http/Controllers/NotificationController.php` - 通知控制器

#### 模型
- `app/Models/Conversation.php` - 对话模型（已扩展）
- `app/Models/Message.php` - 消息模型（已扩展）

#### 视图
- `resources/views/frontend/user/chat/index.blade.php` - 对话列表
- `resources/views/frontend/user/chat/show.blade.php` - 对话详情
- `resources/views/frontend/user/chat/partials/message.blade.php` - 消息组件

#### 邮件
- `app/Mail/NewChatMessage.php` - 新消息邮件
- `resources/views/emails/new_chat_message.blade.php` - 邮件模板

## 🔧 安装和配置

### 1. 运行数据库迁移
```bash
php artisan migrate
```

### 2. 确保对话系统已启用
在后台管理中确保 `conversation_system` 设置为 `1`

### 3. 配置邮件设置
确保 `.env` 文件中的邮件配置正确：
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=your-email
MAIL_FROM_NAME="${APP_NAME}"
```

## 📋 使用说明

### 用户端使用

1. **开始对话**
   - 在产品详情页点击"Chat with Seller"按钮
   - 填写初始消息并发送
   - 系统自动创建对话并分享产品信息

2. **管理对话**
   - 在用户面板的"Chat with Sellers"中查看所有对话
   - 点击对话进入聊天界面
   - 实时收发消息

3. **分享产品**
   - 在对话中点击产品分享按钮
   - 选择要分享的产品
   - 产品信息以卡片形式发送

### 卖家端使用

卖家可以通过相同的界面回复用户消息，系统会自动处理权限和通知。

## 🔗 API 接口

### 用户聊天 API (需要认证)

```
GET /api/v2/user-chat/conversations - 获取对话列表
GET /api/v2/user-chat/conversation/{id} - 获取对话详情
POST /api/v2/user-chat/send-message - 发送消息
POST /api/v2/user-chat/share-product - 分享产品
POST /api/v2/user-chat/create-conversation - 创建对话
```

### 通知 API

```
GET /notifications/chat/unread-count - 获取未读消息数量
GET /notifications/chat/latest - 获取最新消息
POST /notifications/chat/mark-read - 标记消息为已读
```

## 🎨 自定义样式

聊天界面的样式可以通过修改以下CSS类来自定义：

- `.chat-container` - 聊天容器
- `.message` - 消息容器
- `.message-bubble` - 消息气泡
- `.product-card` - 产品卡片

## 🔍 故障排除

### 常见问题

1. **消息不显示**
   - 检查 `conversation_system` 设置
   - 确认用户权限
   - 查看浏览器控制台错误

2. **邮件通知不工作**
   - 检查邮件配置
   - 查看 Laravel 日志
   - 确认队列设置（如果使用队列）

3. **实时通知不更新**
   - 检查 JavaScript 控制台错误
   - 确认路由配置正确
   - 验证用户认证状态

### 日志位置
- Laravel 日志: `storage/logs/laravel.log`
- 邮件错误会记录在应用日志中

## 🚀 性能优化建议

1. **使用队列处理邮件**
   ```bash
   php artisan queue:work
   ```

2. **缓存对话列表**
   - 考虑使用 Redis 缓存热门对话

3. **数据库索引**
   - 为 `conversations.last_message_at` 添加索引
   - 为 `messages.is_read` 添加索引

## 📞 技术支持

如果遇到问题，请检查：
1. Laravel 版本兼容性
2. 数据库迁移是否完成
3. 路由是否正确注册
4. 权限设置是否正确

---

**注意**: 这个系统需要 Laravel 框架和相关依赖包的支持。确保所有依赖都已正确安装。

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnhanceConversationsForProductSharing extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 为conversations表添加新字段
        Schema::table('conversations', function (Blueprint $table) {
            $table->integer('product_id')->nullable()->after('title');
            $table->boolean('is_tip')->default(0)->after('receiver_viewed');
            $table->timestamp('last_message_at')->nullable()->after('is_tip');
        });

        // 为messages表添加新字段以支持不同类型的消息
        Schema::table('messages', function (Blueprint $table) {
            $table->string('message_type')->default('text')->after('message'); // text, product, image, file
            $table->json('message_data')->nullable()->after('message_type'); // 存储产品信息、文件信息等
            $table->string('attachment_url')->nullable()->after('message_data');
            $table->boolean('is_read')->default(0)->after('attachment_url');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('conversations', function (Blueprint $table) {
            $table->dropColumn(['product_id', 'is_tip', 'last_message_at']);
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn(['message_type', 'message_data', 'attachment_url', 'is_read']);
        });
    }
}

<?php
/**
 * 测试Seller Panel访问控制
 */

echo "=== 测试Seller Panel访问控制 ===\n\n";

// 检查用户侧边栏文件
$sideNavFile = file_get_contents('resources/views/frontend/inc/user_side_nav.blade.php');

echo "1. 检查用户侧边栏Seller Panel显示逻辑:\n";

// 检查是否有条件显示逻辑
if (strpos($sideNavFile, 'Auth::user()->user_type == \'seller\'') !== false) {
    echo "✓ 包含卖家身份检查\n";
} else {
    echo "✗ 缺少卖家身份检查\n";
}

// 检查是否有审核状态检查
if (strpos($sideNavFile, 'verification_status') !== false) {
    echo "✓ 包含审核状态检查\n";
} else {
    echo "✗ 缺少审核状态检查\n";
}

// 检查是否有审核中提示
if (strpos($sideNavFile, 'Reviewing') !== false || strpos($sideNavFile, 'badge-warning') !== false) {
    echo "✓ 包含审核中状态提示\n";
} else {
    echo "✗ 缺少审核中状态提示\n";
}

// 检查是否有弹窗函数
if (strpos($sideNavFile, 'showVerificationModal') !== false) {
    echo "✓ 包含审核状态弹窗函数\n";
} else {
    echo "✗ 缺少审核状态弹窗函数\n";
}

echo "\n2. 检查IsSeller中间件访问控制:\n";

// 检查IsSeller中间件
$isSellerMiddleware = file_get_contents('app/Http/Middleware/IsSeller.php');

// 检查是否只允许seller访问
if (strpos($isSellerMiddleware, 'user_type == \'seller\'') !== false) {
    echo "✓ 只允许seller用户类型访问\n";
} else {
    echo "✗ 访问控制不严格\n";
}

// 检查是否移除了特殊处理
if (strpos($isSellerMiddleware, '移除特殊处理') !== false) {
    echo "✓ 已移除未审核用户的特殊访问权限\n";
} else {
    echo "✗ 仍然允许未审核用户访问\n";
}

echo "\n3. 检查不同用户状态的显示逻辑:\n";

// 分析侧边栏逻辑
$lines = explode("\n", $sideNavFile);
$inSellerPanelSection = false;
$hasSellerCheck = false;
$hasVerificationCheck = false;
$hasWarningBadge = false;

foreach ($lines as $line) {
    if (strpos($line, 'Seller Panel') !== false) {
        $inSellerPanelSection = true;
    }
    
    if ($inSellerPanelSection) {
        if (strpos($line, 'user_type == \'seller\'') !== false) {
            $hasSellerCheck = true;
        }
        if (strpos($line, 'verification_status') !== false) {
            $hasVerificationCheck = true;
        }
        if (strpos($line, 'badge-warning') !== false) {
            $hasWarningBadge = true;
        }
        if (strpos($line, '@endif') !== false && $inSellerPanelSection) {
            break;
        }
    }
}

if ($hasSellerCheck) {
    echo "✓ 已通过审核的卖家：显示正常Seller Panel链接\n";
} else {
    echo "✗ 缺少已审核卖家的处理\n";
}

if ($hasVerificationCheck && $hasWarningBadge) {
    echo "✓ 未通过审核的用户：显示审核中提示\n";
} else {
    echo "✗ 缺少未审核用户的处理\n";
}

echo "\n4. 检查弹窗消息内容:\n";

// 检查弹窗消息
if (strpos($sideNavFile, 'under review') !== false) {
    echo "✓ 包含待审核状态消息\n";
} else {
    echo "✗ 缺少待审核状态消息\n";
}

if (strpos($sideNavFile, 'was rejected') !== false) {
    echo "✓ 包含被拒绝状态消息\n";
} else {
    echo "✗ 缺少被拒绝状态消息\n";
}

if (strpos($sideNavFile, 'rejection_reason') !== false) {
    echo "✓ 包含拒绝原因显示\n";
} else {
    echo "✗ 缺少拒绝原因显示\n";
}

echo "\n5. 总体评估:\n";

$allChecks = [
    strpos($sideNavFile, 'Auth::user()->user_type == \'seller\'') !== false,
    strpos($sideNavFile, 'verification_status') !== false,
    strpos($sideNavFile, 'showVerificationModal') !== false,
    strpos($isSellerMiddleware, 'user_type == \'seller\'') !== false,
    strpos($isSellerMiddleware, '移除特殊处理') !== false
];

$passedChecks = array_filter($allChecks);
$totalChecks = count($allChecks);
$passedCount = count($passedChecks);

if ($passedCount == $totalChecks) {
    echo "✓ 所有检查通过 ({$passedCount}/{$totalChecks})\n";
    echo "✓ Seller Panel访问控制已正确实现\n";
    echo "✓ 未通过审核的用户将看到'正在审核'提示\n";
    echo "✓ 只有已通过审核的卖家才能访问Seller Panel\n";
} else {
    echo "✗ 部分检查失败 ({$passedCount}/{$totalChecks})\n";
    echo "需要进一步修复访问控制逻辑\n";
}

echo "\n=== 测试完成 ===\n";
?>

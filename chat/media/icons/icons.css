@font-face {
  font-family: "Support Board Icons";
  src: url("../../media/icons/support-board.eot?v=2");
  src: url("../../media/icons/support-board.eot?#iefix") format("embedded-opentype"), url("../../media/icons/support-board.woff?v=2") format("woff"), url("../../media/icons/support-board.ttf?v=2") format("truetype"), url("../../media/icons/support-board.svg#support-board?v=2") format("svg");
  font-weight: normal;
  font-style: normal; }
[class^="sb-icon-"]:before,
[class*=" sb-icon-"]:before {
  font-family: "Support Board Icons" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.sb-icon-filter:before {
  content: "\e901"; }

.sb-icon-sms:before {
  content: "\e91c"; }

.sb-icon-automation:before {
  content: "\e915"; }

.sb-icon-chat:before {
  content: "\64"; }

.sb-icon-clip:before {
  content: "\65"; }

.sb-icon-download:before {
  content: "\66"; }

.sb-icon-envelope:before {
  content: "\67"; }

.sb-icon-marker:before {
  content: "\68"; }

.sb-icon-message:before {
  content: "\69"; }

.sb-icon-desktop:before {
  content: "\6a"; }

.sb-icon-plane:before {
  content: "\6b"; }

.sb-icon-phone:before {
  content: "\6c"; }

.sb-icon-settings:before {
  content: "\6d"; }

.sb-icon-user:before {
  content: "\6e"; }

.sb-icon-arrow-down:before {
  content: "\61"; }

.sb-icon-arrow-up:before {
  content: "\61";
  transform: rotate(180deg);
  display: inline-block; }

.sb-icon-calendar:before {
  content: "\62"; }

.sb-icon-search:before {
  content: "\6f"; }

.sb-icon-close:before {
  content: "\70"; }

.sb-icon-message-add:before {
  content: "\71"; }

.sb-icon-emoji:before {
  content: "\72"; }

.sb-icon-menu:before {
  content: "\73"; }

.sb-icon-arrow-left:before {
  content: "\74"; }

.sb-icon-arrow-right:before {
  content: "\75"; }

.sb-icon-check:before {
  content: "\77"; }

.sb-icon-check-circle:before {
  content: "\43"; }

.sb-icon-loader:before {
  content: "\76"; }

.sb-icon-delete:before {
  content: "\78"; }

.sb-icon-padlock:before {
  content: "\79"; }

.sb-icon-shuffle:before {
  content: "\7a"; }

.sb-icon-back:before {
  content: "\41"; }

.sb-icon-file:before {
  content: "\42"; }

.sb-icon-reload:before {
  content: "\44"; }

.sb-icon-help:before {
  content: "\45"; }

.sb-icon-currency:before {
  content: "\48"; }

.sb-icon-language:before {
  content: "\49"; }

.sb-icon-smartphone:before {
  content: "\4a"; }

.sb-icon-clock:before {
  content: "\46"; }

.sb-icon-next:before {
  content: "\47"; }

.sb-icon-plus:before {
  content: "\30"; }

.sb-icon-plus-2:before {
  content: "\4d"; }

.sb-icon-dislike:before {
  content: "\4e"; }

.sb-icon-like:before {
  content: "\4f"; }

.sb-icon-send:before {
  content: "\50"; }

.sb-icon-refresh:before {
  content: "\51"; }

.sb-icon-woocommerce:before {
  content: "\52"; }

.sb-icon-social-fb:before {
  content: "\53"; }

.sb-icon-social-tw:before {
  content: "\54"; }

.sb-icon-social-li:before {
  content: "\55"; }

.sb-icon-social-pi:before {
  content: "\56"; }

.sb-icon-social-wa:before {
  content: "\57"; }

.sb-icon-social-me:before {
  content: "\58"; }

.sb-icon-bar-chart:before {
  content: "\59"; }

.sb-icon-circle:before {
  content: "\e900"; }

/*# sourceMappingURL=icons.css.map */

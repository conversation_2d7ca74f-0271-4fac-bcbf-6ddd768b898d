"use strict";!function(e){function t(e,t=!1){return at.infoBottom(e,t)}function i(e,t="info",i=!1,a="",s="",n=!1,o=!1,r=!1){return at.infoPanel(e,t,i,a,s,n,o,r)}function a(e){return at.activeUser(e)}function s(e){return at.loading(e)}function n(e=!0,t=!0){return at.loadingGlobal(e,t)}function o(e){return SB_TRANSLATIONS&&e in SB_TRANSLATIONS?SB_TRANSLATIONS[e]:e}function r(){typeof caches!==We&&caches.delete("sb-pwa-cache")}function l(t,i){let a=(t=e(t)).find("> div, > ul");a.css({height:"","max-height":""}),t.find(".sb-collapse-btn").remove(),t.hasClass("sb-collapse")&&e(a).prop("scrollHeight")>i&&(t.sbActive(!0).attr("data-height",i),t.append(`<a class="sb-btn-text sb-collapse-btn">${o("View more")}</a>`),a.css({height:i+"px","max-height":i+"px"}))}function c(t,i){let a=e(t).parent().find("i"),s=e(t).val();SBF.search(s,()=>{a.sbLoading(!0),i(s,a)})}function d(t,i=!1,a=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-a)}function u(t=!1){if(!Xe){if(!1===Je)return Je=!0,void e.getScript(SB_URL+"/vendor/editorjs.js",()=>{u(t)});p(),Xe=!0,Je=new EditorJS({data:h(t)?{time:Date.now(),blocks:[t?{id:"sb",type:"raw",data:{html:t}}:{id:"sb",type:"paragraph",data:{text:""}}]}:t,i18n:{messages:{ui:{blockTunes:{toggler:{"Click to tune":o("Click to tune")}},inlineToolbar:{converter:{"Convert to":o("Convert to")}},toolbar:{toolbox:{Add:o("Add")}}},toolNames:{Text:o("Text"),Heading:o("Heading"),List:o("List"),Image:o("Image"),Code:o("Code"),"Raw HTML":o("Raw HTML"),Bold:o("Bold"),Italic:o("Italic"),Link:o("Link")},tools:{list:{Ordered:o("Ordered"),Unordered:o("Unordered")}},blockTunes:{delete:{Delete:o("Delete")},moveUp:{"Move up":o("Move up")},moveDown:{"Move down":o("Move down")}}},direction:w.hasClass("sb-rtl")?"rtl":"ltr"},tools:{list:{class:List,inlineToolbar:!0},image:{class:ImageTool,config:{uploader:{uploadByFile(t){let i=new FormData;return i.append("file",t),new Promise(t=>{e.ajax({url:SB_URL+"/include/upload.php",cache:!1,contentType:!1,processData:!1,data:i,type:"POST",success:function(e){"success"==(e=JSON.parse(e))[0]?t({success:1,file:{url:e[1]}}):console.log(e)}})})}}}},header:Header,code:CodeTool,raw:RawTool},onReady:()=>{Xe=!1,Ge=!0,Re=!1},onChange:()=>{Ge?Ge=!1:Re=!0},minHeight:50})}}function p(){typeof Je.destroy!==We&&(Je.destroy(),Je=!1)}function h(e){return"string"==typeof e}function f(e){ze||window.history.pushState("","",e)}function g(){return SB_ADMIN_SETTINGS.cloud?"&cloud="+SB_ADMIN_SETTINGS.cloud.token:""}function b(e){return e.replace("https://","").replace("http://","").replace("www.","").replace(/\/$/,"")}function v(e){SBF.search(e,()=>{let t="",i=!(e.length>1),a=Te.find(".sb-replies-list > ul"),s=`<li class="sb-no-results">${o("No results found.")}</li>`;for(var n=0;n<Ce.length;n++)(i||Ce[n]["reply-name"].toLowerCase().includes(e)||Ce[n]["reply-text"].toLowerCase().includes(e))&&(t+=`<li><div>${Ce[n]["reply-name"]}</div><div>${Ce[n]["reply-text"]}</div></li>`);if(a.html(t),!i&&SB_ADMIN_SETTINGS.dialogflow){let i=a.closest(".sb-popup").find(".sb-icon-search");i.sbLoading(!0),Ye.dialogflow.getIntents(n=>{let o=Ye.dialogflow.searchIntents(e,!0),r="";for(var l=0;l<o.length;l++){let e=o[l].messages[0].text;e&&e.text&&(r+=`<li><div>${o[l].displayName}</div><div>${o[l].messages[0].text.text[0]}</div></li>`)}a.append(r||(t?"":s)),i.sbLoading(!1)})}else t||a.html(s)})}function m(e){let i=w.find("#sb-whatsapp-send-template-box");n(),SBF.ajax({function:"whatsapp-get-templates"},a=>{let s='<option value=""></option>',o=a[0],r="twilio"==o;if((a=a[1]).error)return t(a.error.message,"error");if(!Array.isArray(a))return t(a,"error");for(var l=0;l<a.length;l++)"official"!=o||"APPROVED"!=a[l].status||SB_ACTIVE_AGENT.department&&a[l].department&&SB_ACTIVE_AGENT.department!=a[l].department||(s+=`<option value="${a[l].name}" data-languages="${a[l].languages}" data-phone-id="${a[l].phone_number_id}">${a[l].name} (${a[l].label})</option>`),r&&(s+=`<option value="${a[l].sid}">${a[l].friendly_name}</option>`);i.attr("data-provider",o),i.find("#sb-whatsapp-send-template-list").html(s),SBForm.clear(i),i.find(".sb-direct-message-users").val(e.length?e.join(","):"all"),i.find(".sb-bottom > div").html(""),i.find(".sb-loading").sbLoading(!1),n(!1),i.sbShowLightbox()})}function S(e,t,a){window.open(e),i(`${o("For security reasons, delete the file after downloading it. Close this window to automatically delete it. File location:")}<pre>${e}</pre>`,"info",!1,t,a)}function _(){Be=!1,ye=!1,setTimeout(()=>{_e&&(_e[1].css("transform",""),_e[1].removeClass("sb-touchmove"))},100)}function B(e){return I.find(`[data-conversation-id="${e}"]`)}function y(){SBF.ajax({function:"get-conversations"},e=>{e.length||A.find(".sb-board").addClass("sb-no-conversation"),tt.populateList(e),je&&A.find(".sb-admin-list").sbActive(!0),SBF.getURL("conversation")?(A.sbActive()||x.find(".sb-admin-nav #sb-conversations").click(),tt.openConversation(SBF.getURL("conversation"))):je||SBF.getURL("user")||SBF.getURL("setting")||SBF.getURL("report")||SBF.getURL("area")&&"conversations"!=SBF.getURL("area")||tt.clickFirst(),tt.startRealTime(),tt.datetime_last_conversation=SB_ADMIN_SETTINGS.now_db,n(!1)}),SBF.serviceWorker.init(),SB_ADMIN_SETTINGS.push_notifications&&SBF.serviceWorker.initPushNotifications(),setInterval(function(){SBF.ajax({function:"get-active-user",db:!0},e=>{e||SBF.reset()})},36e5)}var w,x,k,A,T,C,$,I,F,N,L,E,M,D,R,G,j,U,P,O,V,q,H,W,z,J,X,Y,Z,K,Q,ee,te,ie,ae,se,ne,oe,re,le,ce,de,ue,pe,he,fe,ge,be,ve,me,Se,_e,Be,ye,we,xe,ke,Ae=[],Te=!1,Ce=!1,$e=!1,Ie=!1,Fe=1,Ne=1,Le={},Ee=1,Me=1,De=!0,Re=!1,Ge=!0,je=e(window).width()<465,Ue={last:0,header:!0,always_hidden:!1},Pe="localhost"===location.hostname||"127.0.0.1"===location.hostname,Oe=new Date,Ve=!1,qe=!0,He=!1,We="undefined",ze=!1,Je=!1,Xe=!1;e.fn.miniTip=function(t){var i=e.extend({title:"",content:!1,delay:300,anchor:"n",event:"hover",fadeIn:200,fadeOut:200,aHide:!0,maxW:"250px",offset:5,stemOff:0,doHide:!1},t);w&&0==w.find("#miniTip").length&&w.append('<div id="miniTip" class="sb-tooltip"><div></div></div>');var a=w.find("#miniTip"),s=a.find("div");return i.doHide?(a.stop(!0,!0).fadeOut(i.fadeOut),!1):this.each(function(){var t=e(this),n=i.content?i.content:t.attr("title");if(""!=n&&void 0!==n){window.delay=!1;var o=!1,r=!0;i.content||t.removeAttr("title"),"hover"==i.event?(t.hover(function(){a.removeAttr("click"),r=!0,l.call(this)},function(){r=!1,c()}),i.aHide||a.hover(function(){o=!0},function(){o=!1,setTimeout(function(){!r&&!a.attr("click")&&c()},20)})):"click"==i.event&&(i.aHide=!0,t.click(function(){return a.attr("click","t"),a.data("last_target")!==t?l.call(this):"none"==a.css("display")?l.call(this):c(),a.data("last_target",t),e("html").unbind("click").click(function(t){"block"==a.css("display")&&!e(t.target).closest("#miniTip").length&&(e("html").unbind("click"),c())}),!1}));var l=function(){i.show&&i.show.call(this,i),i.content&&""!=i.content&&(n=i.content),s.html(n),i.render&&i.render(a),a.hide().width("").width(a.width()).css("max-width",i.maxW);var o=t.is("area");if(o){var r,l=[],c=[],d=t.attr("coords").split(",");function u(e,t){return e-t}for(r=0;r<d.length;r++)l.push(d[r++]),c.push(d[r]);var p=t.parent().attr("name"),h=e("img[usemap=\\#"+p+"]").offset(),f=parseInt(h.left,10)+parseInt((parseInt(l.sort(u)[0],10)+parseInt(l.sort(u)[l.length-1],10))/2,10),g=parseInt(h.top,10)+parseInt((parseInt(c.sort(u)[0],10)+parseInt(c.sort(u)[c.length-1],10))/2,10)}else g=parseInt(t.offset().top,10),f=parseInt(t.offset().left,10);var b=o?0:parseInt(t.outerWidth(),10),v=o?0:parseInt(t.outerHeight(),10),m=a.outerWidth(),S=a.outerHeight(),_=Math.round(f+Math.round((b-m)/2)),B=Math.round(g+v+i.offset+8),y=Math.round(m-16)/2-parseInt(a.css("borderLeftWidth"),10),w=0,x=f+b+m+i.offset+8>parseInt(e(window).width(),10),k=m+i.offset+8>f,A=S+i.offset+8>g-e(window).scrollTop(),T=g+v+S+i.offset+8>parseInt(e(window).height()+e(window).scrollTop(),10),C=i.anchor;k||"e"==i.anchor&&!x?"w"!=i.anchor&&"e"!=i.anchor||(C="e",w=Math.round(S/2-8-parseInt(a.css("borderRightWidth"),10)),y=-8-parseInt(a.css("borderRightWidth"),10),_=f+b+i.offset+8,B=Math.round(g+v/2-S/2)):(x||"w"==i.anchor&&!k)&&("w"!=i.anchor&&"e"!=i.anchor||(C="w",w=Math.round(S/2-8-parseInt(a.css("borderLeftWidth"),10)),y=m-parseInt(a.css("borderLeftWidth"),10),_=f-m-i.offset-8,B=Math.round(g+v/2-S/2))),T||"n"==i.anchor&&!A?"n"!=i.anchor&&"s"!=i.anchor||(C="n",w=S-parseInt(a.css("borderTopWidth"),10),B=g-(S+i.offset+8)):(A||"s"==i.anchor&&!T)&&("n"!=i.anchor&&"s"!=i.anchor||(C="s",w=-8-parseInt(a.css("borderBottomWidth"),10),B=g+v+i.offset+8)),"n"==i.anchor||"s"==i.anchor?m/2>f?(_=_<0?y+_:y,y=0):f+m/2>parseInt(e(window).width(),10)&&(_-=y,y*=2):A?(B+=w,w=0):T&&(B-=w,w*=2),delay&&clearTimeout(delay),delay=setTimeout(function(){a.css({"margin-left":_+"px","margin-top":B+"px"}).stop(!0,!0).fadeIn(i.fadeIn)},i.delay),a.attr("class","sb-tooltip "+C)},c=function(){(!i.aHide&&!o||i.aHide)&&(delay&&clearTimeout(delay),delay=setTimeout(function(){d()},i.delay))},d=function(){!i.aHide&&!o||i.aHide?(a.stop(!0,!0).fadeOut(i.fadeOut),i.hide&&i.hide.call(this)):setTimeout(function(){c()},200)}}})},e.fn.sbLanguageSwitcher=function(t=[],i="",a=!1){let s=`<div class="sb-language-switcher" data-source="${i}">`,n=[],r=e(this).hasClass("sb-language-switcher-cnt")?e(this):e(this).find(".sb-language-switcher-cnt");for(var l=0;l<t.length;l++){let e=h(t[l])?t[l]:t[l][0],i=!(h(t[l])||!t[l][1])&&t[l][1];n.includes(e)||(s+=`<span ${a==e?'class="sb-active" ':""}data-language="${e}"${i?' data-id="'+i+'"':""}><i class="sb-icon-close"></i><img src="${SB_URL}/media/flags/${e.toLowerCase()}.png" /></span>`,n.push(e))}return r.find(".sb-language-switcher").remove(),r.append(s+`<i data-sb-tooltip="${o("Add translation")}" class="sb-icon-plus"></i></div>`),r.sbInitTooltips(),this},e.fn.sbShowLightbox=function(t=!1,i=""){return w.find(".sb-lightbox").sbActive(!1),me.sbActive(!0),e(this).sbActive(!0),t?e(this).addClass("sb-popup-lightbox").attr("data-action",i):e(this).css({"margin-top":e(this).outerHeight()/-2+"px","margin-left":e(this).outerWidth()/-2+"px"}),e("body").addClass("sb-lightbox-active"),setTimeout(()=>{at.open_popup=this},500),this.preventDefault,this},e.fn.sbHideLightbox=function(){return e(this).find(".sb-lightbox,.sb-popup-lightbox").sbActive(!1).removeClass("sb-popup-lightbox").removeAttr("data-action"),me.sbActive(!1),e("body").removeClass("sb-lightbox-active"),at.open_popup=!1,this},e.fn.sbInitTooltips=function(){return e(this).find("[data-sb-tooltip]").each(function(){e(this).miniTip({content:e(this).attr("data-sb-tooltip"),anchor:"s",delay:500})}),this};var Ye={dialogflow:{intents:!1,token:SBF.storage("dialogflow-token"),dialogflow_languages:[],original_response:!1,smart_reply_busy:!1,smartReply:function(e=!1){let t=SBChat.conversation.id;this.smart_reply_busy!=t&&(this.smart_reply_busy=t,SBF.ajax({function:"dialogflow-smart-reply",message:e,token:this.token,conversation_id:t,dialogflow_languages:this.dialogflow_languages},e=>{if(this.smart_reply_busy=!1,SBChat.conversation.id&&t===SBChat.conversation.id){let t=e.suggestions,a="",s=T[0].scrollTop===T[0].scrollHeight-T[0].offsetHeight,n=!(!SBChat.conversation||!SBChat.conversation.getLastMessage())&&SBChat.conversation.getLastMessage().message;e.token&&(this.token=e.token,SBF.storage("dialogflow-token",e.token));for(var i=0;i<t.length;i++)t[i]!=n&&(a+=`<span>${t[i]}</span>`);R.html(a),s&&SBChat.scrollBottom()}e.dialogflow_languages&&(this.dialogflow_languages=e.dialogflow_languages)}))},showCreateIntentBox:function(e){let t="",i=SBChat.conversation.getMessage(e),a=i.message;if(SBF.isAgent(i.get("user_type")))(t=SBChat.conversation.getLastUserMessage(i.get("index")))&&t.payload("sb-human-takeover")&&(t=SBChat.conversation.getLastUserMessage(t.get("index"))),t&&(t=t.payload("translation")?t.payload("translation"):t.message),i.payload("original-message")&&(a=i.payload("original-message"));else{t=i.payload("translation")?i.payload("translation"):a;let e=SBChat.conversation.getNextMessage(i.id,"agent");e&&(a=e.payload("original-message")?e.payload("original-message"):e.message)}D.hasClass("sb-dialogflow-disabled")||this.getIntents(e=>{let i='<option value="">'+o("New Intent")+"</option>";for(var a=0;a<e.length;a++)i+=`<option value="${e[a].name}">${e[a].displayName}</option>`;D.find("#sb-intents-select").html(i),Ye.openAI.generateIntents(t)}),D.attr("data-message-id",i.id),D.find(".sb-type-text:not(.sb-first)").remove(),D.find(".sb-type-text input").val(t),D.find("#sb-intents-select").val(""),D.find(".sb-search-btn").sbActive(!1).find("input").val(""),this.searchIntents(""),this.original_response=a,D.find("textarea").val(a),D.sbShowLightbox()},submitIntent:function(i){if(s(i))return;let a=[],n=D.find("textarea").val(),o=D.find("#sb-intents-select").val(),r=D.find("#sb-train-chatbots").val();if(D.find(".sb-type-text input").each(function(){e(this).val()&&a.push(e(this).val())}),!n&&!o||0==a.length)SBForm.showErrorMessage(D,"Please insert the bot response and at least one user expression."),e(i).sbLoading(!1);else{let s="open-ai"==r||D.hasClass("sb-dialogflow-disabled");SBF.ajax({function:s?"open-ai-qea-training":o?"dialogflow-update-intent":"dialogflow-create-intent",questions_answers:!!s&&[[a[0],n]],expressions:a,response:n,agent_language:D.find(".sb-dialogflow-languages select").val(),conversation_id:SBChat.conversation.id,intent_name:o,services:r},a=>{e(i).sbLoading(!1),!0===a?(w.sbHideLightbox(),t("Training completed")):SBForm.showErrorMessage(D,a.error&&a.error.message?a.error&&a.error.message:"Error")})}},getIntents:function(e){!1===this.intents?SBF.ajax({function:"dialogflow-get-intents"},t=>{this.intents=Array.isArray(t)?t:[],e(this.intents)}):e(this.intents)},searchIntents:function(e,t=!1){let i=!(e.length>1),a=i?`<option value="">${o("New Intent")}</option>`:"",s=this.intents,n=[];e=e.toLowerCase();for(var r=0;r<s.length;r++){let o=i||s[r].displayName.toLowerCase().includes(e);if(!o&&s[r].trainingPhrases){let t=s[r].trainingPhrases;for(var l=0;l<t.length;l++){for(var c=0;c<t[l].parts.length;c++)if(t[l].parts[c].text.toLowerCase().includes(e)){o=!0;break}if(o)break}}o&&(t?n.push(s[r]):a+=`<option value="${s[r].name}">${s[r].displayName}</option>`)}if(t)return n;D.find("#sb-intents-select").html(a).change(),e||D.find("textarea").val(this.original_response)},previewIntent:function(e){let t="",a=this.getIntent(e);if(a){let e=a.trainingPhrases?a.trainingPhrases:[],o=e.length;if(o>1){for(var s=0;s<o;s++)for(var n=0;n<e[s].parts.length&&(t+=`<span>${e[s].parts[n].text}</span>`,15!=n);n++);i(t,"info",!1,"intent-preview-box","",o>10)}}},getIntent:function(e){for(var t=0;t<this.intents.length;t++)if(this.intents[t].name==e)return this.intents[t];return!1},translate:function(e,t,i,a,s){e.length&&SBF.ajax({function:"google-translate",strings:e,language_code:t,token:this.token,message_ids:a,conversation_id:s},e=>{if(this.token=e[1],!Array.isArray(e[0]))return SBF.error(JSON.stringify(e[0]),"SBApps.dialogflow.translate"),!1;i(e[0])})}},openAI:{urls_history:[],progress:1,rewriteButton:function(e){G.length&&G.sbActive(e.length>2&&e.indexOf(" "))},rewrite:function(e,t=!1,i){SBF.ajax({function:"open-ai-message",model:SB_ADMIN_SETTINGS.open_ai_model,message:(SB_ADMIN_SETTINGS.open_ai_prompt_rewrite?SB_ADMIN_SETTINGS.open_ai_prompt_rewrite:"Make the following sentence more friendly and professional")+` and use ${SB_LANGUAGE_CODES[SB_ADMIN_SETTINGS.active_agent_language]} language${t?", add greetings":""}: """${e.replace('"',"'")}"""`,extra:"rewrite"},t=>{t[0]||console.error("OpenAI: "+JSON.stringify(t[1])),tt.previous_editor_text=e,i(t)})},generateIntents:function(e){if(SB_ADMIN_SETTINGS.open_ai_user_expressions&&e){let t=D.find('[data-value="add"]');t.sbLoading(!0),D.find(".sb-open-ai-intent").remove(),SBF.ajax({function:"open-ai-user-expressions",message:e},e=>{let i="";for(var a=0;a<e.length;a++)e[a]&&(i+=`<div class="sb-input-setting sb-type-text sb-open-ai-intent"><input type="text" value="${e[a].replace(/"/g,"")}"></div>`);i&&D.find("> div > .sb-type-text").last().after(i),t.sbLoading(!1)})}},train:{urls:[],errors:[],base_url:!1,start_urls:[],sitemap_processed_urls:[],active_source:!1,history:[],training_button:!1,extract_url:[],skip_files:[],files:function(e,t=0){let i=re.prop("files");if(t>=i.length)return e(!0);let a=i[t].name;this.isFile(a)&&!this.skip_files.includes(a)?(w.find("#sb-embeddings-box p").html(o("We are processing the source")+"<pre>"+a+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",i.length-t)+"</span>"),re.sbUploadFiles(i=>{"success"==(i=JSON.parse(i))[0]&&SBF.ajax({function:"open-ai-file-training",url:i[1]},i=>{this.isError(i)||this.files(e,t+1)})},t)):this.files(e,t+1)},website:function(e,t=0){if(t>=this.urls.length)return e(!0);let i=this.urls[t];i&&i.includes("http")?(w.find("#sb-embeddings-box p").html(o("We are processing the source")+"<pre>"+i+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",this.urls.length-t)+"</span>"),i.includes(".xml")?SBF.ajax({function:"get-sitemap-urls",sitemap_url:i},i=>{Array.isArray(i)?this.urls=this.urls.concat(i):this.errors.push(i),this.website(e,t+1)}):!this.sitemap_processed_urls.includes(i)&&this.extract_url[t]?(this.sitemap_processed_urls.push(i),this.sitemap(i,i=>{this.urls=this.urls.concat(i),this.website(e,t+1)})):SBF.ajax({function:"open-ai-url-training",url:i},i=>{this.isError(i)||(i[0]||!0===i[0][0]||this.errors.push(i),this.website(e,t+1))})):(i&&this.errors.push(o("Use a valid URL starting with http. The URL {R} is not valid.").replace("{R}",i)),this.website(e,t+1))},sitemap:function(e,t,i=[]){w.find("#sb-embeddings-box p").html(o("We are generating the sitemap")+"<pre>"+e+"</pre>"),SBF.ajax({function:"generate-sitemap",url:e},e=>{t(e)})},qea:function(e){w.find("#sb-embeddings-box p").html(o("We are processing the Q&A."));let t=Ze.repeater("get",se.find(".repeater-item")).map(function(e){return[e["open-ai-faq-question"],e["open-ai-faq-answer"]]});SBF.ajax({function:"open-ai-qea-training",questions_answers:t,reset:!0},t=>{e(t)})},articles:function(e){w.find("#sb-embeddings-box p").html(o("We are processing the articles.")),SBF.ajax({function:"open-ai-articles-training"},t=>{e(t)})},isFile:function(e){return e.includes(".pdf")||e.includes(".txt")},isError:function(e){let t="chars-limit-exceeded"==e[1],a=t||e[1]&&e[1][0]&&e[1][0].error;return a&&(te.find("#sb-train-chatbot").sbLoading(!1),i(t?o("The chatbot cannot be trained with these sources because the limit of your plan is {R} characters. Upgrade your plan to increase the number of characters.").replace("{R}",e[2]):e[1][0].error.message)),a}},playground:{messages:[],last_response:!1,addMessage:function(e,t="user"){oe.append(`<div data-type="${t}"><div>${o("user"==t?"User":"Assistant")}<div><i class="sb-icon-close sb-btn-icon sb-btn-red"></i></div></div><div>${new SBMessage({id:1,message:e,creation_time:"0000-00-00 00:00:00",status_code:0,user_type:"agent"}).getCode()}</div></div>`),oe[0].scrollTop=oe[0].scrollHeight,this.messages.push([t,e])}},init:function(){SBF.ajax({function:"open-ai-get-training-files"},e=>{let t=["",""];for(var i=0;i<e.length;i++)if(!["sb-conversations","sb-articles","sb-database"].includes(e[i])){let a=this.train.isFile(e[i]);t[a?1:0]+=`<tr data-url="${e[i]}"><td><input type="checkbox" /></td><td>${a?SBF.beautifyAttachmentName(e[i].split("/").pop()):b(e[i])}</td><td></td><td><i class="sb-icon-delete"></i></td></tr>`}ie.html(t[1]).sbLoading(!1),ae.html(t[0]).sbLoading(!1),te.find("#sb-chatbot-delete-website").setClass("sb-hide",!t[0])}),SBF.ajax({function:"open-ai-get-qea-training"},e=>{let t=e.map(e=>({"open-ai-faq-question":e[0],"open-ai-faq-answer":e[1]}));t.length&&se.find(".sb-repeater").html(Ze.repeater("set",t,se.find(".repeater-item:last-child")))})}},messenger:{check:function(e){return["fb","ig"].includes(e.get("source"))},send:function(e,t,i="",a=[],s,n=!1,o=!1){SBF.ajax({function:"messenger-send-message",psid:e,facebook_page_id:t,message:i,message_id:n,attachments:a,metadata:s},e=>{o&&o(e),Ye.unsupportedRichMessages(i,"Messenger")})}},whatsapp:{check:function(e){return"wa"==e.get("source")},send:function(e,t="",a=[],s=!1,n=!1){SBF.ajax({function:"whatsapp-send-message",to:e,message:t,attachments:a,phone_id:s},e=>{e.error&&i(e.error.message,"info",!1,"error-wa"),n&&n(e),Ye.unsupportedRichMessages(t,"WhatsApp")})},activeUserPhone:function(e=a()){return!!e.getExtra("phone")&&e.getExtra("phone").value.replace("+","")}},telegram:{check:function(e){return"tg"==e.get("source")},send:function(e,t="",i=[],a=!1,s=!1){SBF.ajax({function:"telegram-send-message",chat_id:e,message:t,attachments:i,conversation_id:a},e=>{s&&s(e),Ye.unsupportedRichMessages(t,"Telegram")})}},viber:{check:function(e){return"vb"==e.get("source")},send:function(e,t="",i=[],a=!1){SBF.ajax({function:"viber-send-message",viber_id:e,message:t,attachments:i},e=>{a&&a(e),Ye.unsupportedRichMessages(t,"Viber")})}},twitter:{check:function(e){return"tw"==e.get("source")},send:function(e,t="",i=[],a=!1){SBF.ajax({function:"twitter-send-message",twitter_id:e,message:t,attachments:i},e=>{a&&a(e),Ye.unsupportedRichMessages(t,"Twitter")})}},gbm:{token:!1,check:function(e){return"bm"==e.get("source")},send:function(e,t="",i=[],a=!1){SBF.ajax({function:"gbm-send-message",google_conversation_id:e,message:t,attachments:i,token:this.token},e=>{a&&a(e),this.token=e[2],Ye.unsupportedRichMessages(t,"Google Business Messages")})}},line:{check:function(e){return"ln"==e.get("source")},send:function(e,t="",i=[],a=!1,s=!1){SBF.ajax({function:"line-send-message",line_id:e,message:t,attachments:i,conversation_id:a},e=>{s&&s(e),Ye.unsupportedRichMessages(t,"LINE")})}},wechat:{token:!1,check:function(e){return"wc"==e.get("source")},send:function(e,t="",i=[],a=!1){SBF.ajax({function:"wechat-send-message",open_id:e,message:t,attachments:i,token:this.token},e=>{Array.isArray(e)&&(this.token=e[1],e=e[0]),a&&a(e),Ye.unsupportedRichMessages(t,"WeChat")})}},aecommerce:{panel:!1,conversationPanel:function(){let t="",i=a().getExtra("aecommerce-id");this.panel||(this.panel=A.find(".sb-panel-aecommerce")),i&&!s(this.panel)&&SBF.ajax({function:"aecommerce-get-conversation-details",aecommerce_id:i.value},i=>{t=`<h3>${SB_ADMIN_SETTINGS.aecommerce_panel_title}</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.currency_symbol}${i.total}</span></div></div><div class="sb-title">${o("Cart")}</div><div class="sb-list-items sb-list-links sb-aecommerce-cart">`;for(a=0;a<i.cart.length;a++){let e=i.cart[a];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="sb-title">${o("Orders")}</div><div class="sb-list-items sb-list-links sb-aecommerce-orders">`;for(var a=0;a<i.orders.length;a++){let e=i.orders[a],s=e.id;t+=`<a data-id="${s}" href="${e.url}" target="_blank"><span>#${e.id}</span> <span>${SBF.beautifyTime(e.time,!0)}</span> <span>${i.currency_symbol}${e.price}</span></a>`}t+="</div>"}e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},martfury:{panel:!1,conversationPanel:function(){let t=a().getExtra("martfury-id");this.panel||(this.panel=A.find(".sb-panel-martfury")),t&&!s(this.panel)&&SBF.ajax({function:"martfury-get-conversation-details",martfury_id:t.value},t=>{e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html("")}},whmcs:{panel:!1,conversationPanel:function(){let t="",i=a().getExtra("whmcs-id");this.panel||(this.panel=A.find(".sb-panel-whmcs")),i&&!s(this.panel)&&SBF.ajax({function:"whmcs-get-conversation-details",whmcs_id:i.value},i=>{let a=["products","addons","domains"];t=`<h3>WHMCS</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of services")}</div><span>${i.services_count} ${o("services")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.currency_symbol}${i.total}</span></div></div>`,t+="</div>";for(var s=0;s<a.length;s++){let e=i[a[s]];if(e.length){t+=`<div class="sb-title">${o(SBF.slugToString(a[s]))}</div><div class="sb-list-items">`;for(var n=0;n<e.length;n++)t+=`<div>${e[n].name}</div>`;t+="</div>"}}t+=`<a href="${SB_ADMIN_SETTINGS.whmcs_url}/clientssummary.php?userid=${i["client-id"]}" target="_blank" class="sb-btn sb-whmcs-link">${o("View on WHMCS")}</a>`,e(this.panel).html(t).sbLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},perfex:{conversationPanel:function(){let e=a().getExtra("perfex-id");A.find(".sb-panel-perfex").html(e?`<a href="${SB_ADMIN_SETTINGS.perfex_url}/admin/clients/client/${e.value}" target="_blank" class="sb-btn sb-perfex-link">${o("View on Perfex")}</a>`:"")}},ump:{panel:!1,conversationPanel:function(){if(s(this.panel))return;this.panel||(this.panel=A.find(".sb-panel-ump"));let t,i="";SBF.ajax({function:"ump-get-conversation-details"},a=>{if((t=a.subscriptions).length){i='<i class="sb-icon-refresh"></i><h3>Membership</h3><div class="sb-list-names">';for(var s=0;s<t.length;s++){let e=t[s].expired;i+=`<div${e?' class="sb-expired"':""}><span>${t[s].label}</span><span>${o(e?"Expired on":"Expires on")} ${SBF.beautifyTime(t[s].expire_time,!1,!e)}</span></div>`}i+=`</div><span class="sb-title">${o("Total spend")} ${a.currency_symbol}${a.total}</span>`}e(this.panel).html(i).sbLoading(!1),l(this.panel,160)})}},armember:{panel:!1,conversationPanel:function(){let t=a().getExtra("wp-id");if(this.panel||(this.panel=A.find(".sb-panel-armember")),SBF.null(t)||s(this.panel))e(this.panel).html("");else{let i,s="";t=t.value,SBF.ajax({function:"armember-get-conversation-details",wp_user_id:t},t=>{if((i=t.subscriptions).length){s=`<i class="sb-icon-refresh"></i><h3>${o("Plans")}</h3><div class="sb-list-names">`;for(var n=0;n<i.length;n++){let e=i[n].expired;s+=`<div${e?' class="sb-expired"':""}><span>${i[n].arm_current_plan_detail.arm_subscription_plan_name}</span><span>${"never"==i[n].expire_time?"":o(e?"Expired on":"Expires on")+" "+SBF.beautifyTime(i[n].expire_time,!1,!e)}</span></div>`}s+=`</div><span class="sb-title">${o("Total spend")} ${t.currency_symbol}${t.total}<a href="${window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"?page=arm_manage_members&member_id="+a().getExtra("wp-id").value}" target="_blank" class="sb-btn-text"><i class="sb-icon-user"></i> ${o("View member")}</a></span>`}e(this.panel).html(s).sbLoading(!1),l(this.panel,160)})}}},zendesk:{conversationPanel:function(){if(!SB_ADMIN_SETTINGS.zendesk_active)return;let t=a().getExtra("zendesk-id"),i=a().getExtra("phone"),n=a().get("email"),o=A.find(".sb-panel-zendesk");(t||i||n)&&!s(o)?SBF.ajax({function:"zendesk-get-conversation-details",conversation_id:SBChat.conversation.id,zendesk_id:!!t&&t.value,phone:!!i&&i.value,email:n},t=>{e(o).html(t).sbLoading(!1),o.find(".sb-zendesk-date").each(function(){e(this).html(SBF.beautifyTime(e(this).html()))}),l(o,160)}):e(o).html("")}},woocommerce:{popupPaginationNumber:1,popupLanguage:"",popupCache:[],panel:!1,timeout:!1,popupCode:function(e,t=!0){let i="";for(var a=0;a<e.length;a++)i+=`<li data-id="${e[a].id}"><div class="sb-image" style="background-image:url('${e[a].image}')"></div><div><span>${e[a].name}</span><span>${SB_ADMIN_SETTINGS.currency}${e[a].price}</span></div></li>`;return t?i||`<p>${o("No products found")}</p>`:i},popupSearch:function(t){c(t,(t,i)=>{t?(this.popupPaginationNumber=1,SBF.ajax({function:"woocommerce-search-products",search:t},t=>{Ie.html(this.popupCode(t)),e(i).sbLoading(!1)})):this.popupPopulate(function(){e(i).sbLoading(!1)})})},popupFilter:function(t){s(Ie)||(Ie.html(""),this.popupPaginationNumber=1,SBF.ajax({function:"woocommerce-get-products",user_language:this.popupLanguage,filters:{taxonomy:e(t).data("value")}},e=>{Ie.html(this.popupCode(e)).sbLoading(!1)}))},popupPopulate:function(e=!1){this.popupLanguage=0!=a()&&SB_ADMIN_SETTINGS.languages.includes(a().language)?a().language:"",this.popupPaginationNumber=1,Ie.html("").sbLoading(!0),SBF.ajax({function:"woocommerce-products-popup",user_language:this.popupLanguage},t=>{let i="",a=$e.find(".sb-select");for(var s=0;s<t[1].length;s++)i+=`<li data-value="${t[1][s].id}">${t[1][s].name}</li>`;a.find("> p").html(o("All")),a.find("ul").html(`<li data-value="" class="sb-active">${o("All")}</li>`+i),Ie.html(this.popupCode(t[0])).sbLoading(!1),!1!==e&&e()})},popupPagination:function(t){this.popupPaginationNumber&&(Ie.sbLoading(t),SBF.ajax({function:"woocommerce-get-products",filters:{taxonomy:e(t).parent().find(".sb-select p").attr("data-value")},pagination:this.popupPaginationNumber,user_language:this.popupLanguage},e=>{Ie.append(this.popupCode(e,!1)).sbLoading(!1),this.popupPaginationNumber++,e.length||(this.popupPaginationNumber=0)}))},conversationPanel:function(){if(s(this.panel))return;this.panel||(this.panel=A.find(".sb-panel-woocommerce"));let t="";SBF.ajax({function:"woocommerce-get-conversation-details"},i=>{t=`<i class="sb-icon-refresh"></i><h3>WooCommerce</h3><div><div class="sb-split"><div><div class="sb-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="sb-title">${o("Total spend")}</div><span>${i.currency_symbol}${i.total}</span></div></div><div class="sb-title">${o("Cart")}<i class="sb-add-cart-btn sb-icon-plus"></i></div><div class="sb-list-items sb-list-links sb-woocommerce-cart">`;for(a=0;a<i.cart.length;a++){let e=i.cart[a];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span><i class="sb-icon-close"></i></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="sb-title">${o("Orders")}</div><div class="sb-list-items sb-woocommerce-orders sb-accordion">`;for(var a=0;a<i.orders.length;a++){let e=i.orders[a],s=e.id;t+=`<div data-id="${s}"><span><span>#${s}</span> <span>${SBF.beautifyTime(e.date,!0)}</span><a href="${Se}/wp-admin/post.php?post=${s}&action=edit" target="_blank" class="sb-icon-next"></a></span><div></div></div>`}t+="</div>"}e(this.panel).html(t).sbLoading(!1),l(this.panel,160)})},conversationPanelOrder:function(e){let t=this.panel.find(`[data-id="${e}"] > div`);t.html(""),SBF.ajax({function:"woocommerce-get-order",order_id:e},e=>{let i="",a=this.panel.find(".sb-collapse-btn:not(.sb-active)");if(e){let t=e.products;i+=`<div class="sb-title">${o("Order total")}: <span>${e.currency_symbol}${e.total}<span></div><div class="sb-title">${o("Order status")}: <span>${SBF.slugToString(e.status.replace("wc-",""))}<span></div><div class="sb-title">${o("Date")}: <span>${SBF.beautifyTime(e.date,!0)}<span></div><div class="sb-title">${o("Products")}</div>`;for(s=0;s<t.length;s++)i+=`<a href="${Se}?p=${t[s].id}" target="_blank"><span>#${t[s].id}</span> <span>${t[s].quantity} x</span> <span>${t[s].name}</span></a>`;for(var s=0;s<2;s++){let t=0==s?"shipping":"billing";e[t+"_address"]&&(i+=`<div class="sb-title">${o((0==s?"Shipping":"Billing")+" address")}</div><div class="sb-multiline">${e[t+"_address"].replace(/\\n/g,"<br>")}</div>`)}}a.length&&a.click(),t.html(i)})},conversationPanelUpdate:function(e,t="added"){let i=!1,a=0;this.timeout=setInterval(()=>{i||(SBF.ajax({function:"woocommerce-get-conversation-details"},s=>{let n=!0;for(var o=0;o<s.cart.length;o++)s.cart[o].id==e&&("added"==t?a=61:n=!1);(a>60||n)&&(this.conversationPanel(),A.find(".sb-add-cart-btn,.sb-woocommerce-cart > a i").sbLoading(!1),clearInterval(this.timeout)),a++,i=!1}),i=!0)},1e3)}},opencart:{conversationPanel:function(){let e=A.find(".sb-panel-opencart"),t=a().getExtra("opencart_id"),i=a().getExtra("opencart_store_url");if(!t)return e.html("");s(e)||SBF.ajax({function:"opencart-panel",opencart_id:t.value,store_url:!!i&&i.value},t=>{e.html(t).sbLoading(!1),l(this.panel,160)})},openOrder:function(e){SBF.ajax({function:"opencart-order-details",order_id:e},t=>{at.infoPanel(t,"info",!1,"opencart-order-details",o("Order")+" #"+e,!0)})}},wordpress:{ajax:function(t,i,a){e.ajax({method:"POST",url:SB_WP_AJAX_URL,data:e.extend({action:"sb_wp_ajax",type:t},i)}).done(e=>{!1!==a&&a(e)})}},is:function(e){if(typeof SB_VERSIONS==We)return!1;switch(e){case"opencart":case"gbm":case"zendesk":case"twitter":case"wechat":case"line":case"viber":case"telegram":case"armember":case"aecommerce":case"martfury":case"whmcs":case"perfex":case"ump":case"messenger":case"whatsapp":case"woocommerce":case"dialogflow":case"slack":case"tickets":return typeof SB_VERSIONS[e]!=We&&SB_VERSIONS[e];case"wordpress":return typeof SB_WP!=We;case"sb":return!0}return!1},unsupportedRichMessages:function(e,i,a=[]){a.push("timetable","registration","table","inputs"),["Messenger","WhatsApp"].includes(i)||a.push("email");for(var s=0;s<a.length;s++)e.includes("["+a[s])&&t("The {R} rich message is not supported by {R2}. The rich message was not sent to {R2}.".replace(/{R}/g,SBF.slugToString(a[s])).replace(/{R2}/g,i),"error")},getName:function(e){let t={fb:"Facebook",wa:"WhatsApp",tm:"Text message",ig:"Instagram",tg:"Telegram",tk:"Tickets",wc:"WeChat",em:"Email",tw:"Twitter",bm:"Business Messages",vb:"Viber",ln:"LINE"};return e in t?t[e]:e}},Ze={init:!1,save:function(i=!1){if(i&&s(i))return;let a={},n={};switch(H.find(" > .sb-tab > .sb-nav .sb-active").attr("id")){case"tab-automations":let s=J.find(".sb-active").attr("data-id");Ze.automations.save(a=>{t(!0===a?"Automations saved":a),Ze.automations.populate(),J.find(`[data-id="${s}"]`).click(),i&&e(i).sbLoading(!1)});break;case"tab-translations":this.translations.updateActive(),SBF.ajax({function:"save-translations",translations:JSON.stringify(this.translations.to_update)},()=>{t("Translations saved"),i&&e(i).sbLoading(!1)});break;default:H.find(".sb-setting").each((t,i)=>{let s=this.get(i),o=e(i).data("setting");if(s[0])if(typeof o!=We){let t=!1;if(e(i).find("[data-language]").length){let a=e(i).find("[data-language].sb-active");if(t=s[0]in this.translations.originals&&this.translations.originals[s[0]],this.translations.save(i,!!a.length&&a.attr("data-language")),t&&"string"!=typeof t)for(var r in t)t[r]=[t[r],s[1][r][1]]}o in a||(a[o]={}),a[o][s[0]]=[t||s[1],s[2]]}else n[s[0]]=[s[1],s[2]]}),SBF.ajax({function:"save-settings",settings:JSON.stringify(n),external_settings:a,external_settings_translations:this.translations.translations},()=>{i&&(t("Settings saved. Reload to apply the changes."),e(i).sbLoading(!1))})}},initPlugins:function(){H.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),H.find("[data-setting] .sb-language-switcher-cnt").each(function(){e(this).sbLanguageSwitcher(Ze.translations.getLanguageCodes(e(this).closest("[data-setting]").attr("id")),"settings")})},initColorPicker:function(t=!1){e(t||H).find(".sb-type-color input").colorPicker({renderCallback:function(t,i){e(t.context).closest(".input").find("input").css("background-color",t.text)}})},initHTML:function(e){if(e["slack-agents"]){let i="";for(var t in e["slack-agents"][0])i+=`<div data-id="${t}"><select><option value="${e["slack-agents"][0][t]}"></option></select></div>`;H.find("#slack-agents .input").html(i)}},get:function(t){let i=(t=e(t)).attr("id"),a=t.data("type");switch(a){case"upload":case"range":case"number":case"text":case"password":case"color":case"upload-file":return[i,t.find("input").val(),a];case"textarea":return[i,t.find("textarea").val(),a];case"select":return[i,t.find("select").val(),a];case"checkbox":return[i,t.find("input").is(":checked"),a];case"radio":let s=t.find("input:checked").val();return SBF.null(s)&&(s=""),[i,s,a];case"upload-image":let n=t.find(".image").attr("data-value");return SBF.null(n)&&(n=""),[i,n,a];case"multi-input":let o={};return t.find(".input > div").each((e,t)=>{let i=this.get(t);i[0]&&(o[i[0]]=[i[1],i[2]])}),[i,o,a];case"select-images":return[i,t.find(".input > .sb-active").data("value"),a];case"repeater":return[i,this.repeater("get",t.find(".repeater-item")),a];case"double-select":let r={};return t.find(".input > div").each(function(){let t=e(this).find("select").val();-1!=t&&(r[e(this).attr("data-id")]=[t])}),[i,r,a];case"select-checkbox":return[i,t.find(".sb-select-checkbox input:checked").map(function(){return e(this).attr("id")}).get(),a];case"timetable":let l={};return t.find(".sb-timetable > [data-day]").each(function(){let t=e(this).attr("data-day"),i=[];e(this).find("> div > div").each(function(){let t=e(this).html(),a=e(this).attr("data-value");SBF.null(a)?i.push(["",""]):"closed"==a?i.push(["closed","Closed"]):i.push([a,t])}),l[t]=i}),[i,l,a];case"color-palette":return[i,t.attr("data-value"),a]}return["","",""]},set:function(t,i){let a=e(i)[1],s=e(i)[0];switch(t=`#${t}`,a){case"color":case"upload":case"number":case"text":case"password":case"upload-file":H.find(`${t} input`).val(s);break;case"textarea":H.find(`${t} textarea`).val(s);break;case"select":H.find(`${t} select`).val(s);break;case"checkbox":H.find(`${t} input`).prop("checked","false"!=s&&s);break;case"radio":H.find(`${t} input[value="${s}"]`).prop("checked",!0);break;case"upload-image":s&&H.find(t+" .image").attr("data-value",s).css("background-image",`url("${s}")`);break;case"multi-input":for(var n in s)this.set(n,s[n]);break;case"range":let i=s;H.find(t+" input").val(i),H.find(t+" .range-value").html(i);break;case"select-images":H.find(t+" .input > div").sbActive(!1),H.find(t+` .input > [data-value="${s}"]`).sbActive(!0);break;case"select-checkbox":for(o=0;o<s.length;o++)H.find(`input[id="${s[o]}"]`).prop("checked",!0);H.find(t+" .sb-select-checkbox-input").val(s.join(", "));break;case"repeater":let r=this.repeater("set",s,H.find(t+" .repeater-item:last-child"));r&&H.find(t+" .sb-repeater").html(r);break;case"double-select":for(var n in s)H.find(`${t} .input > [data-id="${n}"] select`).val(s[n]);break;case"timetable":for(var n in s){let i=H.find(`${t} [data-day="${n}"] > div > div`);for(var o=0;o<i.length;o++)e(i[o]).attr("data-value",s[n][o][0]).html(s[n][o][1])}break;case"color-palette":s&&H.find(t).attr("data-value",s)}},repeater:function(t,i,a=!1){if("set"==t){var s="";if(a=e(a).html(),i.length){e(a).find(".sb-icon-close").remove();for(var n=0;n<i.length;n++){let t=e(e.parseHTML(`<div>${a}</div>`));for(var o in i[n])this.setInput(t.find(`[data-id="${o}"]`),i[n][o]);s+=`<div class="repeater-item">${t.html().replace('<i class="sb-icon-close"></i>',"")}<i class="sb-icon-close"></i></div>`}}return s}if("get"==t){let t=[],a=this;return e(i).each(function(){let i={},s=!0;e(this).find("[data-id]").each(function(){let t=a.getInput(this);s&&t&&"hidden"!=e(this).attr("type")&&"auto-id"!=e(this).attr("data-type")&&(s=!1),i[e(this).attr("data-id")]=t}),s||t.push(i)}),t}},repeaterAdd:function(t){let i=e(t).parent();(t=e(e.parseHTML(`<div>${i.find(".repeater-item:last-child").html()}</div>`))).find("[data-id]").each(function(){if(Ze.resetInput(this),"auto-id"==e(this).data("type")){let t=1;i.find('[data-type="auto-id"]').each(function(){let i=parseInt(e(this).val());i>t&&(t=i)}),e(this).attr("value",t+1)}}),i.find(".sb-repeater").append(`<div class="repeater-item">${t.html()}</div>`)},repeaterDelete:function(t){let i=e(t).parent();i.parent().find(".repeater-item").length>1?i.remove():i.find("[data-id]").each((e,t)=>{this.resetInput(t)})},setInput:function(t,i){if(i=e.trim(i),(t=e(t)).is("select"))t.find(`option[value="${i}"]`).attr("selected","");else if(t.is(":checkbox")&&i&&"false"!=i)t.attr("checked","");else if(t.is("textarea"))t.html(i);else{let e=t.is("div");e||t.is("i")||t.is("li")?(t.attr("data-value",i),e&&t.hasClass("image")&&t.css("background-image",i?`url("${i}")`:"")):t.attr("value",i)}},getInput:function(t){if((t=e(t)).is(":checkbox"))return t.is(":checked");if(t.is("div")||t.is("i")||t.is("li")){let e=t.attr("data-value");return SBF.null(e)?"":e}return t.val()},resetInput:function(t){(t=e(t)).is("select")?t.val("").find("[selected]").removeAttr("selected"):t.is(":checkbox")?t.removeAttr("checked").prop("checked",!1):t.is("textarea")?t.html(""):t.removeAttr("value").removeAttr("style").removeAttr("data-value").val("")},getSettingObject:function(t){return e(t)[0].hasAttribute("data-setting")?e(t):e(t).closest("[data-setting]")},visibility:function(e,t){let i=[["#push-notifications-onesignal-sw-url, #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path","#push-notifications-id, #push-notifications-key"],["#messenger-key, #messenger-path-btn","#messenger-sync-btn"],["#open-ai-assistant-id","#open-ai-prompt,#open-ai-model, #open-ai-tokens, #open-ai-temperature, #open-ai-presence-penalty, #open-ai-frequency-penalty, #open-ai-logit-bias, #open-ai-custom-model, #open-ai-omit-previous-messages, #open-ai-source-links"]];H.find(i[e][0]).sbActive(!t),H.find(i[e][1]).setClass("sb-hide",!t)},open:function(e){x.find(".sb-admin-nav #sb-settings").click(),setTimeout(()=>{H.find("#tab-"+e).click().get(0).scrollIntoView()},300)},automations:{items:{messages:[],emails:[],sms:[],popups:[],design:[],more:[]},translations:{},conditions:{datetime:["Date time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],repeat:["Repeat",["Every day","Every week","Every month","Every year"]],browsing_time:["Browsing time",[],"seconds"],scroll_position:["Scroll position",[],"px"],include_urls:["Include URLs",["Contains","Does not contain","Is exactly","Is not"],"URLs parts separated by commas"],exclude_urls:["Exclude URLs",["Contains","Does not contain","Is exactly","Is not"],"URLs parts separated by commas"],referring:["Referring URLs",["Contains","Does not contain","Is exactly","Is not"],"URLs parts separated by commas"],user_type:["User type",["Is visitor","Is lead","Is user","Is not visitor","Is not lead","Is not user"]],returning_visitor:["Returning visitor",["First time visitor","Returning visitor"]],countries:["Countries",[],"Country codes separated by commas"],languages:["Languages",[],"Language codes separated by commas"],cities:["Cities",[],"Cities separated by commas"],custom_variable:["Custom variable",[],"variable=value"]},get:function(e){SBF.ajax({function:"automations-get"},t=>{this.items=t[0],this.translations=Array.isArray(t[1])&&!t[1].length?{}:t[1],e(t)})},save:function(e=!1){this.updateActiveItem(),SBF.ajax({function:"automations-save",automations:this.items,translations:this.translations},t=>{e&&e(t)})},show:function(e=!1,t=!1){this.updateActiveItem();let i=t?t in this.translations?this.translations[t]:[]:this.items,a=W.find(" > .sb-tab > .sb-content");!1===e&&(e=this.activeID()),this.hide(!1);for(var s in i)for(var n=0;n<i[s].length;n++){let o=i[s][n],r=o.conditions;if(o.id==e){X.html("");for(var s in o){let e=a.find(`[data-id="${s}"]`);e.hasClass("image")?(e.css("background-image",`url(${o[s]})`).attr("data-value",o[s]),o[s]||e.removeAttr("data-value")):"checkbox"==e.attr("type")?e.prop("checked",o[s]):e.val(o[s])}if(r)for(var s in r){this.addCondition();let e=X.find(" > div:last-child");e.find("select").val(r[s][0]),this.updateCondition(e.find("select")),e.find(" > div").eq(1).find("select,input").val(r[s][1]),r[s].length>2&&e.find(" > div").eq(2).find("input").val(r[s][2])}return X.parent().setClass("sb-hide",t),a.sbLanguageSwitcher(this.getTranslations(e),"automations",t),!0}}return!1},add:function(){let e=SBF.random(),t=`${o("Item")} ${J.find("li:not(.sb-no-results)").length+1}`;this.updateActiveItem(),this.items[this.activeType()].push(this.itemArray(this.activeType(),e,t)),this.hide(!1),J.find(".sb-active").sbActive(!1),J.find(".sb-no-results").remove(),J.append(`<li class="sb-active" data-id="${e}">${t}<i class="sb-icon-delete"></i></li>`),W.find(".sb-automation-values").find("input, textarea").val(""),W.sbLanguageSwitcher([],"automations"),X.html("")},delete:function(t){this.items[this.activeType()].splice(e(t).parent().index(),1),e(t).parent().remove(),this.hide(),0==this.items[this.activeType()].length&&J.html(`<li class="sb-no-results">${o("No results found.")}</li>`)},populate:function(e=!1){!1===e&&(e=this.activeType());let t="",i=this.items[e];if(this.updateActiveItem(),i.length)for(a=0;a<i.length;a++)t+=`<li data-id="${i[a].id}">${i[a].name}<i class="sb-icon-delete"></i></li>`;else t=`<li class="sb-no-results">${o("No results found.")}</li>`;switch(J.html(t),t="",e){case"emails":t=`<h2>${o("Subject")}</h2><div class="sb-input-setting sb-type-text"><div><input data-id="subject" type="text"></div></div>`;break;case"popups":t=`<h2>${o("Title")}</h2><div class="sb-input-setting sb-type-text"><div><input data-id="title" type="text"></div></div><h2>${o("Profile image")}</h2><div data-type="upload-image" class="sb-input-setting sb-type-upload-image"><div class="input"><div data-id="profile_image" class="image"><i class="sb-icon-close"></i></div></div></div><h2>${o("Message fallback")}</h2><div class="sb-input-setting sb-type-checkbox"><div><input data-id="fallback" type="checkbox"></div></div>`;break;case"design":t=`<h2>${o("Header title")}</h2><div class="sb-input-setting sb-type-text"><div><input data-id="title" type="text"></div></div>`;for(a=1;a<4;a++)t+=`<h2>${o((1==a?"Primary":2==a?"Secondary":"Tertiary")+" color")}</h2><div data-type="color" class="sb-input-setting sb-type-color"><div class="input"><input data-id="color_${a}" type="text"><i class="sb-close sb-icon-close"></i></div></div>`;for(var a=1;a<4;a++)t+=`<h2>${o(1==a?"Header background image":2==a?"Header brand image":"Chat button icon")}</h2><div data-type="upload-image" class="sb-input-setting sb-type-upload-image"><div class="input"><div data-id="${1==a?"background":2==a?"brand":"icon"}" class="image"><i class="sb-icon-close"></i></div></div></div>`;break;case"more":t=`<h2>${o("Department ID")}</h2><div class="sb-input-setting sb-type-number"><div><input data-id="department" type="number"></div></div><h2>${o("Agent ID")}</h2><div class="sb-input-setting sb-type-number"><div><input data-id="agent" type="number"></div></div><h2>${o("Tags")}</h2><div class="sb-input-setting sb-type-text"><div><input data-id="tags" type="text"></div></div><h2>${o("Article IDs")}</h2><div class="sb-input-setting sb-type-number"><div><input data-id="articles" type="text"></div></div><h2>${o("Articles category")}</h2><div class="sb-input-setting sb-type-number"><div><input data-id="articles_category" type="text"></div></div>`}W.find(".sb-automation-extra").html(t),W.attr("data-automation-type",e),Ze.initColorPicker(W),this.hide()},updateActiveItem:function(){let t=this.activeID();if(t){let a=W.find(".sb-language-switcher [data-language].sb-active").attr("data-language"),s=this.activeType(),n=a?a in this.translations?this.translations[a][s]:[]:this.items[s],o=X.find(" > div");for(var i=0;i<n.length;i++)if(n[i].id==t){n[i]={id:t,conditions:[]},W.find(".sb-automation-values").find('input,textarea,[data-type="upload-image"] .image').each(function(){n[i][e(this).attr("data-id")]=e(this).hasClass("image")&&e(this)[0].hasAttribute("data-value")?e(this).attr("data-value"):"checkbox"==e(this).attr("type")?e(this).is(":checked"):e(this).val()}),o.each(function(){let t=[];e(this).find("input,select").each(function(){t.push(e(this).val())}),t[0]&&t[1]&&(2==t.length||t[2])&&n[i].conditions.push(t)}),SBF.null(n[i].name)&&this.delete(J.find(`[data-id="${t}"] i`));break}}},addCondition:function(){X.append(`<div><div class="sb-input-setting sb-type-select sb-condition-1"><select>${this.getAvailableConditions()}</select></div></div>`)},updateCondition:function(t){e(t).parent().siblings().remove();let i=e(t).parents().eq(1);if(e(t).val()){let s=this.conditions[e(t).val()],n="";if(s[1].length){n='<div class="sb-input-setting sb-type-select sb-condition-2"><select>';for(var a=0;a<s[1].length;a++)n+=`<option value="${SBF.stringToSlug(s[1][a])}">${o(s[1][a])}</option>`;n+="</select></div>"}i.append(n+(s.length>2?`<div class="sb-input-setting sb-type-text"><input placeholder="${o(s[2])}" type="text"></div>`:"")),i.siblings().find(".sb-condition-1 select").each(function(){let t=e(this).val();e(this).html(Ze.automations.getAvailableConditions(t)),e(this).val(t)})}else i.remove()},getAvailableConditions:function(t=""){let i='<option value=""></option>',a=[];X.find(".sb-condition-1 select").each(function(){a.push(e(this).val())});for(var s in this.conditions)a.includes(s)&&s!=t||(i+=`<option value="${s}">${o(this.conditions[s][0])}</option>`);return i},addTranslation:function(e=!1,t=!1,i){if(!1===e&&(e=this.activeID()),!1===t&&(t=this.activeType()),this.getTranslations(e).includes(e))return console.warn("Automation translation already in array.");i in this.translations||(this.translations[i]={messages:[],emails:[],sms:[],popups:[],design:[]}),t in this.translations[i]||(this.translations[i][t]=[]),this.translations[i][t].push(this.itemArray(t,e))},getTranslations:function(e=!1){let t=[];!1===e&&(e=this.activeID());for(var i in this.translations){let n=this.translations[i];for(var a in n){let o=n[a];for(var s=0;s<o.length;s++)if(o[s].id==e){t.push(i);break}}}return t},deleteTranslation:function(e=!1,t){if(!1===e&&(e=this.activeID()),t in this.translations){let s=this.translations[t];for(var i in s){let n=s[i];for(var a=0;a<n.length;a++)if(n[a].id==e)return this.translations[t][i].splice(a,1),!0}}return!1},activeID:function(){let e=J.find(".sb-active");return!!e.length&&e.attr("data-id")},activeType:function(){return z.find("li.sb-active").data("value")},itemArray:function(t,i,a="",s=""){return e.extend({id:i,name:a,message:s},"emails"==t?{subject:""}:"popups"==t?{title:"",profile_image:""}:"design"==t?{title:"",color_1:"",color_2:"",color_3:"",background:"",brand:"",icon:""}:{})},hide:function(e=!0){W.find(" > .sb-tab > .sb-content").setClass("sb-hide",e)}},translations:{translations:{},originals:{},to_update:{},add:function(e){let t=Ze.getSettingObject(ue),i=t.attr("id"),a=ue.find("[data-language].sb-active");this.save(t,!!a.length&&a.attr("data-language")),t.find('textarea,input[type="text"]').val(""),this.save(t,e),ue.remove(),t.sbLanguageSwitcher(this.getLanguageCodes(i),"settings",e)},delete:function(e,t){let i=(e=Ze.getSettingObject(e)).attr("id");delete this.translations[t][i],e.find(`.sb-language-switcher [data-language="${t}"]`).remove(),this.activate(e)},activate:function(e,t=!1){let i=(e=Ze.getSettingObject(e)).attr("id"),a=t?this.translations[t][i]:this.originals[i];if(h(a))e.find("input, textarea").val(a);else for(var s in a)e.find("#"+s).find("input, textarea").val(h(a[s])?a[s]:a[s][0])},updateActive:function(){let t=H.find(".sb-translations-list"),i={front:{},admin:{},"admin/js":{},"admin/settings":{}},a=t.attr("data-value");if(!SBF.null(a)){for(var s in i)t.find(' > [data-area="'+s+'"] .sb-input-setting:not(.sb-new-translation)').each(function(){i[s][e(this).find("label").html()]=e(this).find("input").val()}),t.find('> [data-area="'+s+'"] .sb-new-translation').each(function(){let t=e(this).find("input:first-child").val(),a=e(this).find("input:last-child").val();t&&a&&(i[s][t]=a)});this.to_update[a]=i}},save:function(t,i=!1){t=Ze.getSettingObject(t);let a={},s=e(t).attr("id");"multi-input"==t.data("type")?t.find(".multi-input-textarea,.multi-input-text").each(function(){a[e(this).attr("id")]=e(this).find("input, textarea").val()}):a=t.find("input, textarea").val(),i?(i in this.translations||(this.translations[i]={}),this.translations[i][s]=a):this.originals[s]=a},load:function(e){let t=H.find(".sb-translations > .sb-content");t.find(" > .sb-hide").removeClass("sb-hide"),this.updateActive(),SBF.ajax({function:"get-translation",language_code:e},i=>{e in this.to_update&&(i=this.to_update[e]);let a="",s=["front","admin","admin/js","admin/settings"];for(var n=0;n<s.length;n++){let e=i[s[n]];a+=`<div${n?"":' class="sb-active"'} data-area="${s[n]}">`;for(var o in e)a+=`<div class="sb-input-setting sb-type-text"><label>${o}</label><div><input type="text" value="${e[o]}"></div></div>`;a+="</div>"}t.find(".sb-translations-list").attr("data-value",e).html(a),t.find(".sb-menu-wide li").sbActive(!1).eq(0).sbActive(!0),t.sbLoading(!1)}),t.sbLoading(!0)},getLanguageCodes:function(e){let t=[];for(var i in this.translations)e in this.translations[i]&&t.push(i);return t}}},Ke={category_list:[],page_url:!1,get:function(e,t=!1,i=!1,a=!0,s=!1){SBF.ajax({function:"get-articles",id:t,categories:i,articles_language:s,full:a},t=>{e(t)})},save:function(e=!1){let t,i=this.activeID();if(!(t={id:i,title:Y.find(".sb-article-title input").val(),content:Y.find(".sb-article-content textarea").val(),link:Y.find(".sb-article-link input").val(),parent_category:ee.val(),category:Q.val(),language:Y.find(".sb-language-switcher [data-language].sb-active").attr("data-language")}).title&&!t.content)return e(!1);t.language&&(t.parent_id=this.activeID(!0)),Je&&typeof Je.save!==We?Je.save().then(i=>{t.editor_js=i,t.content=function(e){let t="";return e.map(e=>{switch(e.type){case"header":t+=`<h${e.data.level}>${e.data.text}</h${e.data.level}>`;break;case"paragraph":t+=`<p>${e.data.text}</p>`;break;case"image":t+=`<img class="img-fluid" src="${e.data.file.url}" title="${e.data.caption}" /><em>${e.data.caption}</em>`;break;case"list":t+='<ul class="sb-ul-'+e.data.style+'">',e.data.items.forEach(function(e){t+=`<li>${e}</li>`}),t+="</ul>";break;case"code":t+=`<code>${e.data.code}</code>`;break;case"raw":t+=`<div class="bxc-raw-html">${e.data.html}</div>`}}),t}(i.blocks),this.save_2(t,e)}).catch(e=>{console.log(e)}):this.save_2(t,e)},save_2:function(e,i=!1){SBF.ajax({function:"save-article",article:JSON.stringify(e)},a=>{let s=!0!==a&&!isNaN(a);if(Re=!1,s&&(Z.attr("data-id",a),e.id=a,this.viewButton(a),e.language)){let t=Ke.translations.get(e.parent_id);Z.find(`.sb-language-switcher [data-language="${e.language}"]`).attr("data-id",a);for(var n=0;n<t.length;n++)if(t[n][0]==e.language){t[n][1]=e.id,Ke.translations.list[e.parent_id]=t;break}}e.language||Y.find(".ul-articles .sb-active").html(e.title+'<i class="sb-icon-delete"></i>').attr("data-id",e.id),i&&i(a),t(!0===a||s?"Article saved":a)})},show:function(e){e&&(s(Z),this.get(t=>{Z.sbLoading(!1),t=t[0],Z.sbLanguageSwitcher(this.translations.get(t.parent_id?t.parent_id:e),"articles",t.language),Z.attr("data-id",e),Y.find(".sb-article-title input").val(t.title),Y.find(".sb-article-link input").val(t.link),Y.find("#sb-article-id").html(`ID <span>${e}</span>`),Y.find(".sb-article-categories").setClass("sb-hide",t.language),Je||Y.find("#editorjs").length?u(t.editor_js?h(t.editor_js)?JSON.parse(t.editor_js):t.editor_js:t.content):Y.find(".sb-article-content textarea").val(t.content),t.language||(ee.val(t.parent_category),Q.val(t.category)),this.viewButton(e),Re=!1},e))},add:function(){let e=Y.find(".ul-articles");e.find(".sb-active").sbActive(!1),e.append('<li class="sb-active"></li>'),Z.sbLanguageSwitcher([],"articles"),this.clear()},clear:function(){Z.removeAttr("data-id").removeClass("sb-hide"),Z.find("input, textarea, select").val(""),Z.find("input").prop("checked",!1),u(),this.viewButton(),Re=!1},delete:function(e,t=!1){SBF.ajax({function:"save-article",article:JSON.stringify({id:e,delete:!0})},e=>{this.clear(),t&&t(e)})},populate:function(t,i=!1){let a="";for(var s=0;s<t.length;s++)a+=`<li data-id="${t[s].id}">${t[s].title}<i class="sb-icon-delete"></i></li>`;Y.find(i?".ul-categories":".ul-articles").html(a),Y.find(i?".ul-categories > li":".ul-articles > li").eq(0).click(),t.length||e(i?K:Z).sbLoading(!1).addClass("sb-hide")},activeID:function(e=!1){return e?Y.find(".ul-articles .sb-active").attr("data-id"):Z.attr("data-id")},viewButton:function(e=!1){if(this.page_url){Y.find(".sb-view-article").attr("href",e?this.page_url+"?article_id="+e:"")}},categories:{list:[],save:function(e=!1){this.updateActive(),SBF.ajax({function:"save-articles-categories",categories:JSON.stringify(this.list)},i=>{e&&e(i),t(!0===i?"Categories saved":i)})},show:function(t,i=!1){let a=this.getIndex(t);if(!1!==a){let t=i?this.list[a].languages[i]:this.list[a],s=K.find("#category-image");this.updateActive(),K.find("#category-title").val(t.title),K.find("#category-description").val(t.description),K.find("#category-parent").prop("checked",!!t.parent),K.sbLanguageSwitcher(e.map(this.list[a].languages,function(e,t){return t}),"article-categories",i),t.image?s.attr("data-value",t.image).css("background-image",`url("${t.image}")`):s.removeAttr("data-value").removeAttr("style"),K.find(".category-parent").setClass("sb-hide",i)}K.sbLoading(!1)},add:function(){let e=SBF.random();this.list.push({id:e,title:"",description:"",image:"",languages:[]});let t=`<li data-id="${e}">${o("New category")}<i class="sb-icon-delete"></i></li>`;Y.find(".ul-categories").append(t),Y.find(".ul-categories li").eq(Y.find(".ul-categories li").length-1).click(),K.removeClass("sb-hide")},delete:function(e){let t=this.getIndex(e),i=Y.find(".ul-categories");return!1!==t&&(this.list.splice(t,1),i.find(`[data-id="${e}"]`).remove(),i.find("li").eq(0).click(),!0)},update:function(){let e=Q.val(),t=ee.val(),i=["","<option></option>"];for(var a=0;a<this.list.length;a++)i[this.list[a].parent?0:1]+=`<option value="${this.list[a].id}">${this.list[a].title}</option>`;ee.html(i[0]),Q.html(i[1]),ee.val(t),Q.val(e)},updateActive:function(){let e=this.activeID();if(e){let t=this.getIndex(e),i=K.find(".sb-language-switcher .sb-active").attr("data-language"),a={title:K.find("#category-title").val(),description:K.find("#category-description").val(),image:K.find("#category-image").attr("data-value")};i?this.list[t].languages[i]=a:(a.id=SBF.stringToSlug(a.title),a.parent=K.find("#category-parent").is(":checked"),a.languages=this.list[t].languages,this.list[t]=a,Y.find(".ul-categories .sb-active").html(a.title+'<i class="sb-icon-delete"></i>').attr("data-id",a.id))}},clear:function(){K.find("input, textarea").val(""),K.find("#category-image").removeAttr("data-value").removeAttr("style")},getIndex:function(e){for(var t=0;t<this.list.length;t++)if(this.list[t].id==e)return t;return!1},activeID:function(){return Y.find(".ul-categories .sb-active").attr("data-id")},translations:{add:function(t,i=!1){Ke.categories.updateActive(),i||(i=Ke.categories.activeID());let a=Ke.categories.getIndex(i);SBF.null(Ke.categories.list[a].languages)&&(Ke.categories.list[a].languages={}),Ke.categories.list[a].languages[t]={title:"",description:"",image:""},K.sbLanguageSwitcher(e.map(Ke.categories.list[a].languages,function(e,t){return t}),"article-categories",t),Ke.categories.clear()},delete:function(e,t=!1){let i=Ke.categories.activeID();t||(t=Ke.categories.activeID(i)),delete Ke.categories.list[Ke.categories.getIndex(t)].languages[e],Ke.categories.show(i)}}},translations:{list:{},add:function(e,t=!1){t||(t=Ke.activeID(!0));let i=this.get(t);i.push([e,!1]),this.list[t]=i,Z.sbLanguageSwitcher(i,"articles",e),Ke.clear()},delete:function(e,t=!1){t||(t=Ke.activeID(!0));let i=this.get(t);for(var a=0;a<i.length;a++)if(i[a][0]==e)return i.splice(a,1),this.list[t]=i,!0;return!1},get:function(e){return e in this.list?this.list[e]:[]}}},Qe={chart:!1,active_report:!1,active_date_range:!1,initChart:function(e,t="line",i=1){let a=[],s=[],n=SB_ADMIN_SETTINGS.color?[SB_ADMIN_SETTINGS.color]:["#049CFF","#74C4F7","#B9E5FF","#0562A0","#003B62","#1F74C4","#436786"];for(var o in e)a.push(e[o][0]),s.push(o);if("line"!=t&&a.length>6)for(var r=0;r<a.length;r++)n.push("hsl(210, "+Math.floor(100*Math.random())+"%, "+Math.floor(100*Math.random())+"%)");this.chart&&this.chart.destroy(),this.chart=new Chart(xe.find("canvas"),{type:t,data:{labels:s,datasets:[{data:a,backgroundColor:"line"==t?SB_ADMIN_SETTINGS.color?"#cbcbcb82":"#028be530":n,borderColor:"line"==t?SB_ADMIN_SETTINGS.color?SB_ADMIN_SETTINGS.color:"#049CFF":"#FFFFFF",borderWidth:0}]},options:{legend:{display:!1},scales:{yAxes:[{ticks:{callback:function(e,t,a){return 1==i?e:2==i?new Date(1e3*e).toISOString().substr(11,8):e},beginAtZero:!0}}],xAxes:[{ticks:{beginAtZero:!0}}]},tooltips:{callbacks:{label:function(e,t){let s=e.index,n=t.datasets[0].data[s];switch(i){case 1:return n;case 2:return new Date(1e3*a[s]).toISOString().substr(11,8);case 3:return n+"%";case 4:let e=xe.find(".sb-table tbody tr").eq(s).find("td");return e.eq(0).text()+" "+e.eq(1).text()}}},displayColors:!1}}})},initTable:function(e,t,i=!1){let a="<thead><tr>",s=t[Object.keys(t)[0]].length-1,n=[];for(r=0;r<e.length;r++)a+=`<th>${e[r]}</th>`;a+="</tr></thead><tbody>";for(var o in t)0!=t[o][s]&&n.push([o,t[o][s]]);i&&n.reverse();for(var r=0;r<n.length;r++)a+=`<tr><td><div>${n[r][0]}</div></td><td>${n[r][1]}</td></tr>`;a+="</tbody>",xe.find("table").html(a)},initReport:function(e=!1,t=!1){let i=xe.find(".sb-tab > .sb-content");t=SBF.null(t)?[!1,!1]:t.split(" - "),i.sbLoading(!0),e&&(this.active_report=e),this.active_report&&(this.active_date_range=t,this.getData(this.active_report,t[0],t[1],e=>{0==e?i.addClass("sb-no-results-active"):(i.removeClass("sb-no-results-active"),this.initChart(e.data,e.chart_type,e.label_type),this.initTable(e.table,e.data,e.table_inverse),xe.find(".sb-reports-title").html(e.title),xe.find(".sb-reports-text").html(e.description),xe.find(".sb-collapse-btn").remove(),je||l(xe.find(".sb-collapse"),xe.find("canvas").outerHeight()-135)),i.sbLoading(!1)}))},getData:function(e,t=!1,i=!1,a){SBF.ajax({function:"reports",name:e,date_start:t,date_end:i,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},e=>{a(e)})},initDatePicker:function(){let e={ranges:{},locale:{format:"DD/MM/YYYY",separator:" - ",applyLabel:o("Apply"),cancelLabel:o("Cancel"),fromLabel:o("From"),toLabel:o("To"),customRangeLabel:o("Custom"),weekLabel:o("W"),daysOfWeek:[o("Su"),o("Mo"),o("Tu"),o("We"),o("Th"),o("Fr"),o("Sa")],monthNames:[o("January"),o("February"),o("March"),o("April"),o("May"),o("June"),o("July"),o("August"),o("September"),o("October"),o("November"),o("December")],firstDay:1},showCustomRangeLabel:!0,alwaysShowCalendars:!0,autoApply:!0,opens:w.hasClass("sb-rtl")?"left":"right"};e.ranges[o("Today")]=[moment(),moment()],e.ranges[o("Yesterday")]=[moment().subtract(1,"days"),moment().subtract(1,"days")],e.ranges[o("Last 7 Days")]=[moment().subtract(6,"days"),moment()],e.ranges[o("Last 30 Days")]=[moment().subtract(29,"days"),moment()],e.ranges[o("This Month")]=[moment().startOf("month"),moment().endOf("month")],e.ranges[o("Last Month")]=[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")],xe.find("#sb-date-picker").daterangepicker(e).val("")},export:function(e){SBF.ajax({function:"reports-export",name:this.active_report,date_start:this.active_date_range[0],date_end:this.active_date_range[1],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},t=>{e(t)})},open:function(e){x.find(".sb-admin-nav #sb-reports").click(),setTimeout(()=>{xe.find("#"+e).click().get(0).scrollIntoView()},300)}},et={real_time:null,datetime_last_user:"2000-01-01 00:00:00",sorting:["creation_time","DESC"],user_types:["visitor","lead","user"],user_main_fields:["id","first_name","last_name","email","password","profile_image","user_type","creation_time","token","last_activity","department"],search_query:"",init:!1,busy:!1,table_extra:!1,history:[],get:function(e,t=!1,i=!1,a=!1){let n=[];for(var o=0;o<O.length;o++)n.push(O.eq(o).find("li.sb-active").data("value"));s(U),SBF.ajax({function:t?"get-online-users":i?"search-users":"get-users",sorting:t?this.sorting[0]:this.sorting,pagination:!!a&&Ee,user_types:this.user_types,search:this.search_query,extra:this.table_extra,department:n[0],source:n[1],tag:n[2]},t=>{e(t),U.sbLoading(!1)})},filter:function(e){e="all"==e?["visitor","lead","user"]:"agent"==e?["agent","admin"]:[e],this.user_types=e,Ee=1,Me=1,this.get(e=>{this.populate(e)},"online"==e[0])},sort:function(e,t="DESC"){this.sorting=[e,t],Ee=1,Me=1,this.get(e=>{this.populate(e)})},search:function(t){c(t,(t,i)=>{Ee=1,Me=1,this.search_query=t,this.get(t=>{this.user_types=["visitor","lead","user"],this.populate(t),e(i).sbLoading(!1),P.find("li").sbActive(!1).eq(0).sbActive(!0)},!1,t.length>1)})},populate:function(t){let i="",a=t.length;if(a)for(var s=0;s<a;s++)i+=this.getRow(new SBUser(t[s],t[s].extra));else i=`<p class="sb-no-results">${o("No users found.")}</p>`;U.parent().scrollTop(0),U.find("tbody").html(i),this.user_types.includes("agent")&&SBF.ajax({function:"get-online-users",agents:!0},t=>{let i=[];for(var a=0;a<t.length;a++)i.push(t[a].id);U.find("[data-user-id]").each(function(){e(this).find(".sb-td-profile").addClass("sb-"+(i.includes(e(this).attr("data-user-id"))?"online":"offline"))})})},update:function(){if(!this.busy){let e=["user","visitor","lead","agent"],t=e.includes(this.user_types[0])&&!this.search_query,i=P.find(".sb-active").data("type");"online"==i?this.filter(i):(this.busy=!0,SBF.ajax({function:"get-new-users",datetime:this.datetime_last_user},a=>{let s=a.length;if(this.busy=!1,s>0){let o="";for(n=0;n<s;n++){let e=new SBUser(a[n]);Le[e.id]=e,this.updateMenu("add",e.type),t&&(o+=this.getRow(e))}if(t&&(U.find("tbody").prepend(o),e.includes(i))){let t="";for(var n=0;n<e.length;n++)t+=e[n]==i?"":`[data-user-type="${e[n]}"],`;U.find(t.slice(0,-1)).remove()}this.datetime_last_user=a[0].creation_time}}))}},getRow:function(e){if(e instanceof SBUser){let i="";for(var t=0;t<this.table_extra.length;t++){let a=this.table_extra[t];i+=`<td class="sb-td-${a}">${this.user_main_fields.includes(a)?e.get(a):e.getExtra(a)}</td>`}return`<tr data-user-id="${e.id}" data-user-type="${e.type}"><td><input type="checkbox" /></td><td class="sb-td-profile"><a class="sb-profile"><img loading="lazy" src="${e.image}" /><span>${e.name}</span></a></td>${i}<td class="sb-td-email">${e.get("email")}</td><td class="sb-td-ut">${o(e.type)}</td><td>${SBF.beautifyTime(e.get("last_activity"),!0)}</td><td>${SBF.beautifyTime(e.get("creation_time"))}</td></tr>`}return SBF.error("User not of type SBUser","SBUsers.getRow"),!1},updateRow:function(e){let t=U.find(`[data-user-id="${e.id}"]`);if(t.length){let i=P.find(".sb-active").data("type");if(e.type==i||"admin"==e.type&&"agent"==i||"all"==i)t.replaceWith(this.getRow(e));else{let i=w.find(`[data-type="${"admin"==e.type?"agent":e.type}"] span`),a=parseInt(i.attr("data-count"));i.html(a+1).attr("data-count",a+1),t.remove()}}else U.find("tbody").append(this.getRow(e))},updateMenu:function(e="all",t=!1){let i=["all","user","lead","visitor"];"all"==e?SBF.ajax({function:"count-users"},e=>{for(var t=0;t<i.length;t++)this.updateMenuItem("set",i[t],e[i[t]])}):this.updateMenuItem(e,t)},updateMenuItem:function(e="set",t=!1,i=1){let a=P.find(`[data-type="${t}"] span`),s=["user","lead","visitor"];"set"!=e&&(i=parseInt(a.attr("data-count"))+1*("add"==e?1:-1)),a.html(`(${i})`).attr("data-count",i),i=0;for(var n=0;n<s.length;n++)i+=parseInt(P.find(`[data-type="${s[n]}"] span`).attr("data-count"));P.find('[data-type="all"] span').html(`(${i})`).attr("data-count",i)},delete:function(e){if(s(U),Array.isArray(e)){if(SB_ADMIN_SETTINGS.cloud&&!(e=SBCloud.removeAdminID(e)).length)return;SBF.ajax({function:"delete-users",user_ids:e},()=>{for(var i=0;i<e.length;i++)delete Le[e[i]],U.find(`[data-user-id="${e[i]}"]`).remove(),I.find(`[data-user-id="${e[i]}"]`).remove(),SBF.event("SBUserDeleted",e[i]);0==U.find("[data-user-id]").length&&this.filter(P.find(".sb-active").data("type")),t("Users deleted"),this.updateMenu(),U.sbLoading(!1)})}else Le[e].delete(()=>{let i=I.find(`[data-user-id="${e}"]`);a().id==e&&a(!1),i.sbActive()&&(SBChat.conversation=!1,setTimeout(()=>{tt.clickFirst()},300)),delete Le[e],U.find(`[data-user-id="${e}"]`).remove(),i.remove(),w.sbHideLightbox(),t("User deleted"),this.updateMenu(),U.sbLoading(!1)})},startRealTime:function(){SBPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update()},1e3))},stopRealTime:function(){clearInterval(this.real_time)},csv:function(){SBF.ajax({function:"csv-users",users_id:et.getSelected()},e=>{S(e,"sb-export-users-close","Users exported"),window.open(e)})},updateUsersActivity:function(){SBF.updateUsersActivity(qe?SB_ACTIVE_AGENT.id:-1,a()?a().id:-1,function(e){et.setActiveUserStatus("online"==e)})},setActiveAgentStatus:function(e=!0){let t=e?"online":"offline";qe=e,x.find('[data-value="status"]').html(o(SBF.slugToString(t))).attr("class","sb-"+t),SBPusher.active&&(e?(SBPusher.presence(),SB_ADMIN_SETTINGS.routing_only&&SBF.ajax({function:"assign-conversations-active-agent"},()=>{tt.update()})):SBPusher.presenceUnsubscribe()),SB_ADMIN_SETTINGS.reports_disabled||SBF.ajax({function:"reports-update",name:t})},setActiveUserStatus:function(e=!0){let t=A.find(".sb-conversation .sb-top > .sb-labels");t.find(".sb-status-online").remove(),e&&t.prepend(`<span class="sb-status-online">${o("Online")}</span>`),SBChat.user_online=e},onlineUserNotification:function(e){let t=SB_ADMIN_SETTINGS.online_users_notification;if(t){let i=e.info.first_name+" "+e.info.last_name,a=this.userProfileImage(e.info.profile_image);SB_ADMIN_SETTINGS.push_notifications&&e.info.id&&!this.history.includes(e.info.id)?SBF.ajax({function:"push-notification",title:t,message:i,icon:a,interests:SB_ACTIVE_AGENT.id,user_id:e.info.id}):tt.desktop_notifications&&SBChat.desktopNotification(t,i,a,!1,e.info.id),this.history.push(e.info.id)}},userProfileImage:function(e){return!e||e.indexOf("user.svg")?SB_ADMIN_SETTINGS.notifications_icon:e},getSelected:function(){let t=[];return U.find("tr").each(function(){e(this).find('td input[type="checkbox"]').is(":checked")&&t.push(e(this).attr("data-user-id"))}),t}},tt={real_time:null,datetime_last_conversation:"2000-01-01 00:00:00",user_typing:!1,desktop_notifications:!1,flash_notifications:!1,busy:!1,is_search:!1,menu_count_ajax:!1,previous_editor_text:!1,open:function(e=-1,t){-1!=e&&this.openConversation(e,t),w.sbHideLightbox(),x.find(".sb-admin-nav a").sbActive(!1).parent().find("#sb-conversations").sbActive(!0),w.find(" > main > div").sbActive(!1),A.sbActive(!0).find(".sb-board").removeClass("sb-no-conversation"),ke.find(" > p").attr("data-id","").attr("data-value","").html(o("None")),this.notes.update([]),this.tags.update([]),this.startRealTime()},openConversation:function(t,i=!1,s=!0){if(!1===i&&t)SBF.ajax({function:"get-user-from-conversation",conversation_id:t},e=>{SBF.null(e.id)?SBF.error("Conversation not found","SBAdmin.openConversation"):this.openConversation(t,e.id,s)});else{let n=SBF.null(Le[i])||!Le[i].details.email,r=A.find(`[data-conversation-id="${t}"]`),l=I.find("li");T.html(""),T.sbLoading(!0),n?(a(new SBUser({id:i})),a().update(()=>{Le[i]=a(),this.updateUserDetails()})):(a(Le[i]),this.updateCurrentURL()),SBPusher.active&&(SBPusher.event("client-typing",e=>{e.user_id==a().id&&(tt.typing(!0),clearTimeout(ge),ge=setTimeout(()=>{tt.typing(!1)},1e3))}),SBPusher.event("new-message",()=>{SBChat.update()}),SBPusher.event("agent-active-conversation-changed",e=>{e.previous_conversation_id==t&&A.find(".sb-conversation-busy").remove()},"agents"),SBPusher.event("init",e=>{tt.updateCurrentURL(e.current_url)}),SBPusher.event("message-status-update",e=>{SBChat.conversation&&SBChat.conversation.updateMessagesStatus(e.message_ids)})),SB_ADMIN_SETTINGS.smart_reply&&R.html(""),l.sbActive(!1),SB_ADMIN_SETTINGS.departments_show||l.attr("data-color",""),r.sbActive(!0),-1!=t?a().getFullConversation(t,i=>{let n=i.status_code,c=F.eq(0),d=c.find(".sb-active").attr("data-value");if(SBChat.setConversation(i),SBChat.populate(),this.setReadIcon(n),A.find(".sb-conversation-busy").remove(),this.updateUserDetails(),A.find(".sb-top > a").html(i.get("title")),at.must_translate=SB_ADMIN_SETTINGS.translation&&a().language&&SB_ADMIN_SETTINGS.active_agent_language!=a().language,at.must_translate){let e=[],a=[],s=[],n=[];for(f=0;f<i.messages.length;f++){let t=i.messages[f];t.message&&(t.payload("original-message")&&(!t.payload("original-message-language")||t.payload("original-message-language")==SB_ADMIN_SETTINGS.active_agent_language)||t.payload("translation")&&(!t.payload("translation-language")||t.payload("translation-language")==SB_ADMIN_SETTINGS.active_agent_language)?n.push(t):(e.push(t.message),a.push(t.id),s.push(t.get("user_type"))))}e.length?Ye.dialogflow.translate(e,SB_ADMIN_SETTINGS.active_agent_language,e=>{if(e)for(var t=0;t<e.length;t++)this.openConversation_2(a[t],s[t],e[t]);SB_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(SBChat.conversation,R)},a,t):SB_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(SBChat.conversation,R);for(f=0;f<n.length;f++)this.openConversation_2(n[f].id,n[f].user_type,n[f].payload("original-message")?n[f].payload("original-message"):n[f].payload("translation"))}if(ke.length){let e=!!i.get("department")&&this.getDepartments(i.get("department")),a=e?e["department-color"]:"";SB_ADMIN_SETTINGS.departments_show||l.attr("data-color",""),B(t).attr("data-color",a),ke.find(" > p").attr("data-id",e?e["department-id"]:"").attr("data-value",a).html(e?e["department-name"]+"<span></span>":o("None"))}let u=A.find("#conversation-agent");if(u.length){let e=u.find(`[data-id="${i.get("agent_id")}"]`);u.find(" > p").attr("data-value",e.data("id")).html(e.html())}[1,2,"1","2"].includes(n)&&(n=0),d==n||e($).find(".sb-search-btn").sbActive()||tt.filters()[1]||tt.filters()[3]||(c.find(`[data-value="${n}"]`).click(),c.find("ul").sbActive(!1)),je&&this.mobileOpenConversation(),r.length||d!=n&&(0!=d||1!=n)||I.prepend(tt.getListCode(i).replace("<li",'<li class="sb-active"')),I.find("li").sbActive(!1),r.sbActive(!0),s&&this.scrollTo(),this.notificationsCounterReset(t,r),T.sbInitTooltips();let p=i.get("busy");p&&A.find(".sb-editor > .sb-labels").prepend(`<span data-agent="${p.id}" class="sb-status-warning sb-conversation-busy">${p.first_name} ${p.last_name} ${o("is replying to this conversation")}</span>`),Ye.is("woocommerce")&&Ye.woocommerce.conversationPanel(),Ye.is("ump")&&Ye.ump.conversationPanel(),Ye.is("perfex")&&Ye.perfex.conversationPanel(),Ye.is("whmcs")&&Ye.whmcs.conversationPanel(),Ye.is("aecommerce")&&Ye.aecommerce.conversationPanel(),Ye.is("martfury")&&Ye.martfury.conversationPanel(),Ye.is("armember")&&Ye.armember.conversationPanel(),Ye.is("zendesk")&&Ye.zendesk.conversationPanel(),Ye.is("opencart")&&Ye.opencart.conversationPanel(),this.notes.update(i.details.notes),this.tags.update(i.details.tags),this.attachments(),SB_ADMIN_SETTINGS.smart_reply&&!at.must_translate&&this.openConversation_1(i,R);for(f=i.messages.length-1;f>0;f--){let e=i.messages[f].get("payload"),t=!1;if(e&&e["rich-messages"])for(var h in e["rich-messages"]){let i=e["rich-messages"][h];if("rating"==i.type){A.find(".sb-profile-list > ul").append(`<li data-id="rating"><i class="sb-icon sb-icon-${1==i.result.rating?"like":"dislike"}"></i><span>${o("User rating")}</span><label>${o(1==i.result.rating?"Helpful":"Not helpful")}</label></li>`),t=!0;break}}if(t)break}if(a().getConversations(function(e){A.find(".sb-user-conversations").html(1==e.length?"":a().getConversationsCode(e)).prev().setClass("sb-hide",1==e.length)}),this.is_search){let e=$.find(".sb-search-btn input").val();for(var f=0;f<i.messages.length;f++)if(i.messages[f].message.toLowerCase().includes(e)){let e=i.messages[f].id;setTimeout(()=>{let t=T.find(`[data-id="${e}"]`);t.addClass("sb-highlight"),setTimeout(()=>{t.removeClass("sb-highlight")},3600),t.index()?t.prev()[0].scrollIntoView():t[0].scrollIntoView()},300)}}T.sbLoading(!1)}):(SBChat.clear(),I.find("li").sbActive(!1),T.sbLoading(!1),A.find(".sb-top > a").html("")),n||this.updateUserDetails(),A.find(".sb-board").removeClass("sb-no-conversation"),et.updateUsersActivity(),this.startRealTime(),SBF.getURL("conversation")!=t&&-1!=t&&f("?conversation="+t)}},openConversation_1:function(e,t){let i=e.getLastUserMessage();t.html(""),i&&i.payload("sb-human-takeover")&&(i=e.getLastUserMessage(i.get("index"))),i&&Ye.dialogflow.smartReply(i.payload("translation")?i.payload("translation"):i.payload("original-message")?i.payload("original-message"):i.message)},openConversation_2:function(e,t,i){let a=SBChat.conversation.getMessage(e);a&&(a.set("translation",i),T.find(`[data-id="${e}"]`).replaceWith(a.getCode(!0)),T.find(`[data-id="${e}"] .sb-menu`).prepend(`<li data-value="original">${o(SBF.isAgent(t)&&"bot"!=t?"View translation":"View original message")}</li>`))},populate:function(e,t,i){this.openConversation(e,t,i)},populateList:function(e){let t="";Ae=[];for(var i=0;i<e.length;i++)t+=this.getListCode(e[i]),Ae.push(new SBConversation([new SBMessage(e[i])],e[i]));t||(t=`<p class="sb-no-results">${o("No conversations found.")}</p>`),I.html(t),this.updateMenu(),SBF.event("SBAdminConversationsLoaded",{conversations:e})},update:function(){if(!this.busy&&0==F.eq(0).find("p").attr("data-value")){let t=tt.filters();if(this.busy=!0,SBF.ajax({function:"get-new-conversations",datetime:this.datetime_last_conversation,department:t[1],source:t[2],tag:t[3]},t=>{if(this.busy=!1,t.length){let n,o="",r="",l=SBChat.conversation?SBChat.conversation.id:-1,c=!1,d=[];this.datetime_last_conversation=t[0].last_update_time;for(i=0;i<t.length;i++)if(!d.includes(t[i].id)){let e=new SBMessage(t[i]),s=new SBConversation([e],t[i]),u=s.status_code,p=2==u||SB_ADMIN_SETTINGS.order_by_date&&(0==u||1==u),h=s.user_id,f=s.id,g=B(f),b=g.index(),v=g.length,m=e.get("user_type"),S=s.get("message"),_=l==f,y=!SBF.isAgent(m);if(!S&&e.payload("preview")&&(S=e.payload("preview")),p&&(!_||"hidden"==SBF.visibility_status)&&(y||e.payload("human-takeover-message-confirmation"))){let t=SBF.storage("notifications-counter");t||(t={}),t[f]||(t[f]=[]),t[f].includes(e.id)||(t[f].push(e.id),SBF.storage("notifications-counter",t))}let w=this.getListCode(s,null),x=t[i].payload?JSON.parse(t[i].payload):{};if(_?(w=w.replace("<li",'<li class="sb-active"'),this.updateUserDetails(),v?(S&&g.replaceWith(w),Ae[b].set("status_code",u),this.setReadIcon(u)):c=!0):v&&(Ae[b]=s,B(f).remove()),h in Le||(Le[h]=new SBUser({id:h,first_name:s.get("first_name"),last_name:s.get("last_name"),profile_image:s.get("profile_image"),user_type:m})),_&&v||(p?(o+=w,Ae.unshift(s)):0!=u&&1!=u||((n=I.find('[data-conversation-status="2"]').last()).length?(Ae.splice(n.index()+1,0,s),r+=w):o+=w),a()&&h==a().id&&a().getConversations(e=>{A.find(".sb-user-conversations").html(a().getConversationsCode(e))}),SBF.event("SBAdminNewConversation",{conversation:s})),!a()||"update-user"!=x.event&&Le[h].type==m||a().update(()=>{this.updateUserDetails(),Le[a().id]=a()}),!SBChat.tab_active&&2==u&&(y||x.preview)&&(S||s.getAttachments().length||x.preview)){if(this.desktop_notifications){let e=[Le[h].nameBeautified,Le[h].image];SBChat.desktopNotification(e[0],x.preview?x.preview:S,e[1],f,h)}this.flash_notifications&&SBChat.flashNotification(),SBChat.audio&&SB_ADMIN_SETTINGS.sound&&SBChat.playSound()}d.push(f)}tt.is_search||(o&&I.prepend(o),r&&e(r).insertAfter(n),c&&this.scrollTo(),this.updateMenu());for(var i=0;i<SBChat.notifications.length;i++){let e=!1;for(var s=0;s<Ae.length;s++)if(Ae[s].id==SBChat.notifications[i][0]){e=2==Ae[s].status_code;break}e||(SBChat.notifications.splice(i,1),i--)}}}),SB_ADMIN_SETTINGS.assign_conversation_to_agent||SB_ACTIVE_AGENT.department){let t=I.find(" > li").map(function(){return e(this).attr("data-conversation-id")}).get();t.length&&SBF.ajax({function:"check-conversations-assignment",conversation_ids:t,agent_id:!!SB_ADMIN_SETTINGS.assign_conversation_to_agent&&SB_ACTIVE_AGENT.id,department:SB_ACTIVE_AGENT.department},e=>{if(e)for(var t=0;t<e.length;t++)B(e[t]).remove()})}}},updateMenu:function(){let e=I.find('[data-conversation-status="2"]').length,t=F.eq(0),i=t.find(" > p span");if(100==e||this.menu_count_ajax||SB_ADMIN_SETTINGS.order_by_date){let e=t.find("li.sb-active").data("value");this.menu_count_ajax=!0,SBF.ajax({function:"count-conversations",status_code:0==e?2:e},e=>{i.html(`(${e})`)})}else i.html(`(${e})`)},messageMenu:function(e,t=!1){let i=(t&&SB_ADMIN_SETTINGS.chatbot_features?`<li data-value="bot">${o("Train chatbot")}</li>`:"")+(e&&!SB_ADMIN_SETTINGS.supervisor&&SB_ADMIN_SETTINGS.allow_agent_delete_message||SB_ADMIN_SETTINGS.supervisor&&SB_ADMIN_SETTINGS.allow_supervisor_delete_message?`<li data-value="delete">${o("Delete")}</li>`:"");return i?`<i class="sb-menu-btn sb-icon-menu"></i><ul class="sb-menu">${i}</ul>`:""},updateUserDetails(){a()&&(A.find(`[data-user-id="${a().id}"] .sb-name`).html(a().name),A.find(".sb-top > a").html(SBChat.conversation?SBChat.conversation.title:a().name),C.find(".sb-profile").setProfile(),it.populate(a(),A.find(".sb-profile-list")))},setReadIcon(e){let t=2==e;A.find('.sb-top [data-value="read"],.sb-top [data-value="unread"]').sbActive([0,1,2].includes(parseInt(e))).attr("data-value",t?"read":"unread").attr("data-sb-tooltip",o(t?"Mark as read":"Mark as unread")).parent().sbInitTooltips().find("i").attr("class",t?"sb-icon-check-circle":"sb-icon-circle")},getListCode:function(e,t){e instanceof SBConversation||(e=new SBConversation([new SBMessage(e)],e));let i=e.getCode(!0),a=SB_ADMIN_SETTINGS.tags_show?e.get("tags"):"",s=e.get("department"),n="";return i.length>110&&(i=i.substr(0,110)+" ..."),SBF.null(t)&&(t=e.status_code),a&&(a=tt.tags.codeLeft(a)),SBChat.conversation&&SBChat.conversation.id==e.id&&"hidden"!=SBF.visibility_status||((n=SBF.storage("notifications-counter"))&&n[e.id]&&n[e.id].length?2==t?n=`<span class="sb-notification-counter">${n[e.id].length}</span>`:(n[e.id]=[],SBF.storage("notifications-counter",n),n=""):n=""),`<li data-user-id="${e.get("user_id")}" data-conversation-id="${e.id}" data-conversation-status="${t}"${s?` data-department="${s}"${SB_ADMIN_SETTINGS.departments_show?' data-color="'+this.getDepartments(s)["department-color"]+'"':""}`:""}${SBF.null(e.get("source"))?"":` data-conversation-source="${e.get("source")}"`}>${""+n}<div class="sb-profile"><img loading="lazy" src="${e.get("profile_image")}"><span class="sb-name">${e.get("first_name")+" "+e.get("last_name")}</span>${a}<span class="sb-time">${SBF.beautifyTime(e.get("last_update_time"))}</span></div><p>${(new SBMessage).strip(i)}</p></li>`},startRealTime:function(){SBPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update(),this.updateCurrentURL()},1e4),SBChat.startRealTime())},stopRealTime:function(){clearInterval(this.real_time),SBChat.stopRealTime()},transcript:function(e,t,i=!1,s=!1){SBF.ajax({function:"transcript",conversation_id:e},e=>{"email"==i?a()&&a().id==t&&!a().get("email")||SBChat.sendEmail(SB_ADMIN_SETTINGS.transcript_message,[[e,e]],t,t=>{s&&s(!0===t?e:t)}):(s&&s(e),window.open(e))})},typing:function(e){e?(SBChat.user_online||et.setActiveUserStatus(!0),this.user_typing||(A.find(".sb-conversation .sb-top > .sb-labels").append('<span class="sb-status-typing">'+o("Typing")+"</span>"),this.user_typing=!0)):this.user_typing&&(A.find(".sb-conversation .sb-top .sb-status-typing").remove(),this.user_typing=!1)},scrollTo:function(){let e=I.find(".sb-active"),t=e.length?e[0].offsetTop:0;I.parent().scrollTop(t-(je?120:80))},search:function(t){t&&c(t,(t,i)=>{if(Ne=1,t.length>1)SBF.ajax({function:"search-conversations",search:t},t=>{tt.populateList(t),e(i).sbLoading(!1),this.scrollTo(),this.is_search=!0});else{let t=tt.filters();Fe=1,SBF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3]},t=>{tt.populateList(t),e(i).sbLoading(!1),this.is_search=!1,SBChat.conversation&&(B(SBChat.conversation.id).sbActive(!0),this.scrollTo())})}})},notificationsCounterReset:function(e,t=!1){let i=SBF.storage("notifications-counter");if(i&&i[e]){t||(t=I.find('[data-conversation-id="'+e+'"]'));let a=t.find(".sb-notification-counter");i[e]=[],SBF.storage("notifications-counter",i),a.addClass("sb-fade-out"),setTimeout(()=>{a.remove()},200)}},updateCurrentURL:function(e=!1){e?this.ucurl(e):SBChat.user_online&&a()&&a().getExtra("current_url")&&SBF.ajax({function:"current-url"},e=>{e&&this.ucurl(e)})},ucurl(e){let t=a().getExtra("current_url");e=b(e),A.find('.sb-profile-list [data-id="current_url"] label').attr("data-value",e).html(e),t&&(t.value=e,a().setExtra("current_url",t))},assignDepartment:function(e,t,i){SBF.ajax({function:"update-conversation-department",conversation_id:e,department:t,message:SBChat.conversation.getLastMessage().message},e=>{i(e)})},assignAgent:function(e,t,i=!1){SBF.ajax({function:"update-conversation-agent",conversation_id:e,agent_id:t,message:SBChat.conversation.getLastMessage().message},e=>{i&&i(e)})},setActiveDepartment:function(e){if(SBChat.conversation&&SBChat.conversation.get("department")==e)return;let i=!!e&&this.getDepartments(e),a=i?i["department-color"]:"",s=B(SBChat.conversation.id),n=tt.filters()[1];ke.find(" > p").attr("data-id",i?i["department-id"]:"").attr("data-value",a).html((i?i["department-name"]:o("None"))+"<span></span>").next().sbActive(!1),SBChat.conversation.set("department",e),n&&n!=e||"agent"==SB_ACTIVE_AGENT.user_type&&SB_ACTIVE_AGENT.department&&SB_ACTIVE_AGENT.department!=e?(s.remove(),tt.clickFirst()):s.attr("data-color",a),t("Department updated. The agents have been notified.")},getDepartments:function(e=!1){if(e){for(var t=0;t<SB_ADMIN_SETTINGS.departments.length;t++)if(SB_ADMIN_SETTINGS.departments[t]["department-id"]==e)return SB_ADMIN_SETTINGS.departments[t];return!1}return SB_ADMIN_SETTINGS.departments},setActiveAgent:function(e){let i=A.find("#conversation-agent"),a=i.find(`[data-id="${e}"]`);SBChat.conversation.set("agent_id",e),i.find(" > p").attr("data-value",a.data("value")).html(a.html()).next().sbActive(!1),"agent"!=SB_ACTIVE_AGENT.user_type||SB_ADMIN_SETTINGS.assign_conversation_to_agent&&!e||(B(SBChat.conversation.id).remove(),tt.clickFirst()),e&&t("Agent assigned. The agent has been notified.")},mobileOpenConversation:function(){A.find(".sb-admin-list").sbActive(!1),k.sbActive(!0),x.addClass("sb-hide")},mobileCloseConversation:function(){I.find("li.sb-active").sbActive(!1),A.find(".sb-admin-list").sbActive(!0),A.find(".sb-conversation,.sb-user-details").removeClass("sb-active"),w.find('.sb-menu-mobile [data-value="panel"]').sbActive(!1),x.removeClass("sb-hide"),window.history.replaceState({},document.title,SBF.URL())},clickFirst:function(e=!1){e||(e=I.find("li:first-child")),e.length?(e.click(),tt.scrollTo()):(A.find(".sb-board").addClass("sb-no-conversation"),I.find("li").length||I.html(`<p class="sb-no-results">${o("No conversations found.")}</p>`),SBF.getURL("conversation")&&window.history.replaceState({},document.title,SBF.URL().replace("?conversation="+SBF.getURL("conversation"),"")))},savedReplies:function(t,i){let a=i.charAt(t.selectionStart-1),s=i.substr(0,i.lastIndexOf("#"));if("#"==a){if(i.length>1&&"#"==i.charAt(t.selectionStart-2))return e(t).val(s.substr(0,s.length-1)),k.find(".sb-btn-saved-replies").click();SBChat.editor_listening=!0}if(SBChat.editor_listening&&" "==a){let a=i.substr(i.lastIndexOf("#")+1).replace(" ","");SBChat.editor_listening=!1;for(var n=0;n<Ce.length;n++)if(Ce[n]["reply-name"]==a)return void e(t).val(s+Ce[n]["reply-text"])}},attachments:function(){if(E.length){let i=SBChat.conversation.getAttachments(),a="",s="",n=[];for(t=0;t<i.length;t++){let e=SBF.getFileType(i[t][1]);a+=`<a href="${i[t][1]}" target="_blank"><i class="sb-icon sb-icon-download"></i>${i[t][0]}</a>`,n.includes(e)||n.push(e)}if(i.length>4&&n.length>1){s=`<div id="sb-attachments-filter" class="sb-select"><p>${o("All")}</p><ul><li data-value="">${o("All")}</li>`;for(var t=0;t<n.length;t++)s+=`<li data-value="${n[t]}">${o(SBF.slugToString(n[t])+"s")}</li>`;s+="</ul></div>"}e(E).html(a?`<h3${s?' class="sb-flex"':""}>${o("Attachments")}${s}</h3><div class="sb-list-items sb-list-links sb-list-icon">${a}</div>`:""),l(E,160)}},filters:function(){let e=[];for(var t=0;t<F.length;t++)e.push(F.eq(t).find("li.sb-active").data("value"));return(e[1]||e[3])&&(e[0]="all"),e},notes:{busy:!1,add:function(e,t,i,a,s=!1,n=!1){SBF.ajax({function:n?"update-note":"add-note",conversation_id:e,user_id:t,note_id:n,name:i,message:a},e=>{s&&s(e)})},update:function(e,t=!1){if(N.length){let a="",s=N.find(" > div");for(var i=0;i<e.length;i++){let t=e[i];a+=`<div data-id="${t.id}"><span${SB_ADMIN_SETTINGS.notes_hide_name?' class="sb-noname-note"':""}>${SB_ADMIN_SETTINGS.notes_hide_name?"":t.name}${SB_ACTIVE_AGENT.id==t.user_id?'<i class="sb-edit-note sb-icon-edit"></i><i class="sb-delete-note sb-icon-close"></i>':""}</span><span class="sb-note-text">${t.message.replace(/\n/g,"<br>")}</span></div>`}a=a.autoLink({target:"_blank"}),t?s.append(a):s.html(a),s.attr("style",""),N.find(".sb-collapse-btn").remove(),l(N,155),this.busy=!1}},delete:function(e,t,i=!1){this.busy||(this.busy=!0,SBF.ajax({function:"delete-note",conversation_id:e,note_id:t},e=>{this.busy=!1,i&&i(e)}))}},tags:{busy:!1,update:function(e){if(L.length){let i="",a=L.find(" > div");for(var t=0;t<e.length;t++){let a=this.get(e[t]);a&&(i+=this.code(a))}a.html(i),this.busy=!1}},get:function(e){for(var t=0;t<SB_ADMIN_SETTINGS.tags.length;t++)if(e==SB_ADMIN_SETTINGS.tags[t]["tag-name"])return SB_ADMIN_SETTINGS.tags[t]},getAll:function(e=[]){let t="";for(var i=0;i<SB_ADMIN_SETTINGS.tags.length;i++)t+=this.code(i,e.includes(SB_ADMIN_SETTINGS.tags[i]["tag-name"]));return t},code:function(e,t){let i=isNaN(e)?e:SB_ADMIN_SETTINGS.tags[e];if(i){let e=i["tag-name"];return`<span data-value="${e}" data-color="${i["tag-color"]}"${t?' class="sb-active"':""}>${o(e)}</span>`}return""},codeLeft:function(e){let t='<span class="sb-tags-area">';for(var i=0;i<e.length;i++){let a=this.get(e[i]);a&&(t+=`<i class="sb-icon-tag" data-color-text="${a["tag-color"]}"></i>`)}return t+"</span>"}},showDirectMessageBox:function(e,t=[]){if("whatsapp"==e)m(t);else{let i="custom_email"==e;SBForm.clear(M),M.find(".sb-direct-message-users").val(t.length?t.join(","):"all"),M.find(".sb-bottom > div").html(""),M.find(".sb-top-bar > div:first-child").html(o(`Send a ${{sms:"text message",custom_email:"email",message:"chat message"}[e]}`)),M.find(".sb-loading").sbLoading(!1),M.find(".sb-direct-message-subject").sbActive(i).find("input").attr("required",i),M.attr("data-type",e),M.sbShowLightbox()}},getDeliveryFailedMessage:function(e){return setTimeout(()=>{k.sbInitTooltips()},300),`<i class="sb-icon-warning sb-delivery-failed" data-sb-tooltip="${o("Message not delivered to {R}.").replace("{R}",Ye.getName(e))}"> </i>`}},it={getAll:function(e){return SBForm.getAll(e)},get:function(e){return SBForm.get(e)},set:function(e,t){return SBForm.set(e,t)},show:function(e){n(),a(new SBUser({id:e})),a().update(()=>{this.populate(a(),V.find(".sb-profile-list")),V.find(".sb-profile").setProfile(),a().getConversations(t=>{let i=a().type,s=a().getConversationsCode(t);SBF.isAgent(i)&&this.agentData(),V.find(".sb-user-conversations").html(s).prev().setClass("sb-hide",!s),V.find(".sb-top-bar [data-value]").sbActive(!1),SBF.null(a().get("email"))||V.find('.sb-top-bar [data-value="email"]').sbActive(!0),a().getExtra("phone")&&(SB_ADMIN_SETTINGS.sms&&V.find('.sb-top-bar [data-value="sms"]').sbActive(!0),V.find('.sb-top-bar [data-value="whatsapp"]').sbActive(!0)),this.boxClasses(V,i),V.attr("data-user-id",a().id).sbShowLightbox(),n(!1,!1),SBF.event("SBProfileBoxOpened",{user_id:e})}),Le[e]=a(),SBF.getURL("user")==e||SBF.getURL("conversation")||f("?user="+e)})},showEdit:function(e){if(!(e instanceof SBUser))return SBF.error("User not of type SBUser","SBUsers.showEdit"),!1;{let i=q.find("#password input"),a=e.type,s=q.find("#user_type select");q.removeClass("sb-user-new").attr("data-user-id",e.id),q.find(".sb-top-bar .sb-save").html(`<i class="sb-icon-check"></i>${o("Save changes")}`),q.find(".sb-profile").setProfile(),q.find(".sb-custom-detail").remove(),q.find("input,select,textara").removeClass("sb-error");let n="",r=q.find(".sb-additional-details [id]").map(function(){return this.id}).get().concat(["wp-id","perfex-id","whmcs-id","aecommerce-id","facebook-id","ip","os","current_url","country_code","browser_language","browser","martfury-id","martfury-session"]);for(var t in e.extra)r.includes(t)||(n+=`<div id="${t}" data-type="text" class="sb-input sb-custom-detail"><span>${o(e.extra[t].name)}</span><input type="text"></div>`);q.find(".sb-additional-details .sb-edit-box").append(n),this.populateEdit(e,q),this.updateRequiredFields(a),"admin"==SB_ACTIVE_AGENT.user_type&&SBF.isAgent(a)&&s.html(`<option value="agent">${o("Agent")}</option><option value="admin"${"admin"==a?" selected":""}>${o("Admin")}</option>`),i.val()&&i.val("********"),SB_ADMIN_SETTINGS.cloud&&q.setClass("sb-cloud-admin",1==e.id),this.boxClasses(q,a),q.sbShowLightbox(),SBF.event("SBProfileEditBoxOpened",{user_id:e.id})}},populate:function(e,t){let i=["first_name","last_name","password","profile_image"],a="";if(t.hasClass("sb-profile-list-conversation")&&SBChat.conversation){let e=SBChat.conversation.get("source");a=this.profileRow("conversation-id",SBChat.conversation.id,o("Conversation ID")),SBF.null(e)||(a+=this.profileRow("conversation-source",Ye.getName(e),o("Source")))}"admin"!=SB_ACTIVE_AGENT.user_type&&i.push("token");for(var s in e.details)i.includes(s)||(a+=this.profileRow(s,e.get(s),"id"==s?"User ID":s));if(e.isExtraEmpty())SBF.ajax({function:"get-user-extra",user_id:e.id},i=>{for(var s=0;s<i.length;s++){let t=i[s].slug;e.setExtra(t,i[s]),a+=this.profileRow(t,i[s].value,i[s].name)}t.html(`<ul>${a}</ul>`),l(t,145)});else{for(var s in e.extra){let t=e.getExtra(s);a+=this.profileRow(s,t.value,t.name)}t.html(`<ul>${a}</ul>`),l(t,145)}},profileRow:function(e,t,i=e){if(!t)return"";let a,s={id:"user",full_name:"user",email:"envelope",phone:"phone",user_type:"user",last_activity:"calendar",creation_time:"calendar",token:"shuffle",currency:"currency",location:"marker",country:"marker",address:"marker",city:"marker",postal_code:"marker",browser:"desktop",os:"desktop",current_url:"next",timezone:"clock"},n=`<i class="sb-icon sb-icon-${e in s?s[e]:"plane"}"></i>`,r=!1;switch(e){case"last_activity":case"creation_time":t=SBF.beautifyTime(t);break;case"user_type":t=SBF.slugToString(t);break;case"country_code":case"language":case"browser_language":n=`<img src="${SB_URL}/media/flags/${t.toLowerCase()}.png" />`;break;case"browser":(a=t.toLowerCase()).includes("chrome")?r="chrome":a.includes("edge")?r="edge":a.includes("firefox")?r="firefox":a.includes("opera")?r="opera":a.includes("safari")&&(r="safari");break;case"os":(a=t.toLowerCase()).includes("windows")?r="windows":a.includes("mac")||a.includes("apple")||a.includes("ipad")||a.includes("iphone")?r="apple":a.includes("android")?r="android":a.includes("linux")?r="linux":a.includes("ubuntu")&&(r="ubuntu");break;case"conversation-source":r=t.toLowerCase();case"browser":case"os":case"conversation-source":r&&(n=`<img src="${SB_URL}/media/${"conversation-source"==e?"apps":"devices"}/${r}.svg" />`);break;case"current_url":t=b(t)}return`<li data-id="${e}">${n}<span>${o(SBF.slugToString(i))}</span><label>${t}</label></li>`},populateEdit:function(t,i){i.find(".sb-details .sb-input").each((i,a)=>{this.set(a,t.details[e(a).attr("id")])}),i.find(".sb-additional-details .sb-input").each((i,a)=>{let s=e(a).attr("id");s in t.extra?this.set(a,t.extra[s].value):this.set(a,"")})},clear:function(e){SBForm.clear(e)},errors:function(e){return SBForm.errors(e.find(".sb-details"))},showErrorMessage:function(e,t){SBForm.showErrorMessage(e,t)},agentData:function(){let e=`<div class="sb-title">${o("Feedback rating")}</div><div class="sb-rating-area sb-loading"></div>`,t=V.find(".sb-agent-area");t.html(e),SBF.ajax({function:"get-rating"},i=>{if(0==i[0]&&0==i[1])e=`<p class="sb-no-results">${o("No ratings yet.")}</p>`;else{let t=i[0]+i[1],a=100*i[0]/t,s=100*i[1]/t;e=`<div><div>${o("Helpful")}</div><span data-count="${i[0]}" style="width: ${Math.round(2*a)}px"></span><div>${a.toFixed(2)} %</div></div><div><div>${o("Not helpful")}</div><span data-count="${i[1]}" style="width: ${Math.round(2*s)}px"></span><div>${s.toFixed(2)} %</div></div><p class="sb-rating-count">${t} ${o("Ratings")}</p>`}t.find(".sb-rating-area").html(e).sbLoading(!1)})},boxClasses:function(t,i=!1){e(t).removeClass("sb-type-admin sb-type-agent sb-type-lead sb-type-user sb-type-visitor").addClass(`${0!=i?`sb-type-${i}`:""} sb-agent-${SB_ACTIVE_AGENT.user_type}`)},updateRequiredFields:function(e){let t=SBF.isAgent(e);q.find("#password input").prop("required",t),q.find("#email input").prop("required",t)}},at={infoBottom:function(e,t=!1){var i=w.find(".sb-info-card");t?"error"==t?i.addClass("sb-info-card-error"):i.addClass("sb-info-card-info"):(i.removeClass("sb-info-card-error sb-info-card-warning sb-info-card-info"),clearTimeout(pe),pe=setTimeout(()=>{i.sbActive(!1)},5e3)),i.html(`<h3>${o(e)}</h3>`).sbActive(!0)},infoPanel:function(t,i="info",a=!1,s="",n="",r=!1,l=!1,c=!1){if(l&&a)return a();let d=w.find(".sb-dialog-box").attr("data-type",i),u=d.find("p");d.attr("id",s).setClass("sb-scroll-area",r).css("height",r?parseInt(e(window).height())-200+"px":""),d.find(".sb-title").html(o(n)),u.html(("alert"==i?o("Are you sure?")+" ":"")+o(t)),d.sbActive(!0).css({"margin-top":d.outerHeight()/-2+"px","margin-left":d.outerWidth()/-2+"px"}),me.sbActive(!0),he=a,fe=c,setTimeout(()=>{at.open_popup=d},500)},genericPanel:function(e,t,i,a=[],s="",n=!1){let r="",l=w.find("#sb-generic-panel");for(var c=0;c<a.length;c++)a[c]=h(a[c])||a[c]instanceof String?[a[c],!1]:a[c],r+=`<a id="sb-${SBF.stringToSlug(a[c][0])}" class="sb-btn${a[c][1]?" sb-icon":""}">${a[c][1]?`<i class="sb-icon-${a[c][1]}"></i>`:""} ${o(a[c][0])}</a>`;l.html(`<div class="sb-lightbox sb-${e}-box"${s}><div class="sb-info"></div><div class="sb-top-bar"><div>${o(t)}</div>\n                        <div>\n                            ${r}\n                            <a class="sb-close sb-btn-icon sb-btn-red">\n                                <i class="sb-icon-close"></i>\n                            </a>\n                        </div>\n                    </div>\n                    <div class="sb-main${n?" sb-scroll-area":""}">\n                        ${i}\n                    </div>\n             </div>`),l.find("> div").sbShowLightbox()},activeUser:function(e){if(typeof e==We)return window.sb_current_user;window.sb_current_user=e},loadingGlobal:function(t=!0,i=!0){w.find(".sb-loading-global").sbActive(t),i&&(me.sbActive(t),e("body").setClass("sb-lightbox-active",t))},loading:function(t){return!!e(t).sbLoading()||(e(t).sbLoading(!0),!1)},open_popup:!1,must_translate:!1,is_logout:!1,conversations:tt,users:et,settings:Ze,profile:it,apps:Ye};window.SBAdmin=at,e(document).ready(function(){function c(e,i,a=0,s=[],n,r=!1,l=!1,d=!1,u=!1,p=!1){let h={whatsapp:["whatsapp-send-template","messages"," a phone number.","direct-whatsapp"],email:["create-email","emails"," an email address.",!1],custom_email:["send-custom-email","emails"," an email address.","direct-emails"],sms:["send-sms","text messages"," a phone number.","direct-sms"]}[n];SBF.ajax({function:h[0],to:e[a].value,recipient_id:e[a].id,sender_name:SB_ACTIVE_AGENT.full_name,sender_profile_image:SB_ACTIVE_AGENT.profile_image,subject:r,message:i,template_name:l,parameters:d,template_languages:u,template:!1,phone_id:p},l=>{let d=e.length,u="whatsapp"==n?w.find("#sb-whatsapp-send-template-box"):M;if(u.find(".sb-bottom > div").html(`${o("Sending")} ${o(h[1])}... ${a+1} / ${d}`),l){if(!0!==l&&("status"in l&&400==l.status||"error"in l&&![131030,131009].includes(l.error.code)))return SBForm.showErrorMessage(u,l.error?l.error.message:`${l.message} Details at ${l.more_info}`),console.error(l),u.find(".sb-loading").sbLoading(!1),void u.find(".sb-bottom > div").html("");if(a<d-1)return c(e,i,a+1,s,n,r);s.length?c(s,i,0,[],"sms",!1):(w.sbHideLightbox(),h[3]&&SBF.ajax({function:"reports-update",name:h[3],value:i.substr(0,18)+" | "+d})),t(1==d?"The message has been sent.":o("The message was sent to sent to all users who have"+h[2]))}else console.warn(l)})}if(w=e(".sb-admin"),x=w.find("> .sb-header"),A=w.find(".sb-area-conversations"),k=A.find(".sb-conversation"),T=A.find(".sb-conversation .sb-list"),$=A.find(".sb-admin-list"),I=$.find(".sb-scroll-area ul"),F=$.find(".sb-select"),C=A.find(".sb-user-details"),j=w.find(".sb-area-users"),U=j.find(".sb-table-users"),P=j.find(".sb-menu-users"),O=j.find(".sb-filter-btn .sb-select"),V=w.find(".sb-profile-box"),q=w.find(".sb-profile-edit-box"),H=w.find(".sb-area-settings"),W=H.find(".sb-automations-area"),X=W.find(".sb-conditions"),z=W.find(" > .sb-select"),J=W.find(" > .sb-tab > .sb-nav > ul"),xe=w.find(".sb-area-reports"),Y=w.find(".sb-area-articles"),Z=Y.find(".sb-content-articles"),K=Y.find(".sb-content-categories"),ee=w.find("#article-parent-categories"),Q=w.find("#article-categories"),Te=A.find(".sb-replies"),me=w.find(".sb-lightbox-overlay"),Se=typeof SB_URL!=We?SB_URL.substr(0,SB_URL.indexOf("-content")-3):"",$e=A.find(".sb-woocommerce-products"),Ie=$e.find(" > div > ul"),N=A.find(".sb-panel-notes"),L=A.find(".sb-panel-tags"),E=A.find(".sb-panel-attachments"),M=w.find(".sb-direct-message-box"),ze=Ye.is("wordpress")&&e(".wp-admin").length,D=w.find(".sb-dialogflow-intent-box"),R=A.find(".sb-editor > .sb-suggestions"),G=A.find(".sb-btn-open-ai"),ke=A.find("#conversation-department"),re=w.find(".sb-upload-form-admin .sb-upload-files"),te=w.find(".sb-area-chatbot"),ie=te.find("#sb-table-chatbot-files"),ae=te.find("#sb-table-chatbot-website"),se=te.find("#sb-chatbot-qea"),ne=te.find(".sb-playground-editor"),oe=te.find(".sb-playground .sb-scroll-area"),window.onpopstate=function(){w.sbHideLightbox(),je&&A.sbActive()&&k.sbActive()&&tt.mobileCloseConversation(),SBF.getURL("user")?(j.sbActive()||x.find(".sb-admin-nav #sb-users").click(),it.show(SBF.getURL("user"))):SBF.getURL("area")?x.find(".sb-admin-nav #sb-"+SBF.getURL("area")).click():SBF.getURL("conversation")?(A.sbActive()||x.find(".sb-admin-nav #sb-conversations").click(),tt.openConversation(SBF.getURL("conversation"))):SBF.getURL("setting")?(H.sbActive()||x.find(".sb-admin-nav #sb-settings").click(),H.find("#tab-"+SBF.getURL("setting")).click()):SBF.getURL("report")&&(xe.sbActive()||x.find(".sb-admin-nav #sb-reports").click(),xe.find("#"+SBF.getURL("report")).click())},SBF.getURL("area")&&setTimeout(()=>{x.find(".sb-admin-nav #sb-"+SBF.getURL("area")).click()},300),typeof SB_ADMIN_SETTINGS==We){let t=w.find(".sb-intall"),i=window.location.href.replace("/admin","").replace(".php","").replace(/#$|\/$/,"");return e(w).on("click",".sb-submit-installation",function(){if(s(this))return;let a=!1,n=t.find("#first-name").length;SBForm.errors(t)?a=n?"All fields are required. Minimum password length is 8 characters. Be sure you've entered a valid email.":"All fields are required.":n&&t.find("#password input").val()!=t.find("#password-check input").val()?a="The passwords do not match.":(SBF.cookie("SA_VGCKMENS",0,0,"delete"),i.includes("?")&&(i=i.substr(0,i.indexOf("?"))),e.ajax({method:"POST",url:i+"/include/ajax.php",data:{function:"installation",details:e.extend(SBForm.getAll(t),{url:i})}}).done(s=>{if(h(s)&&(s=JSON.parse(s)),0!=s){if(!0===(s=s[1]))return void setTimeout(()=>{window.location.href=i+"/admin.php?refresh=true"},1e3);switch(s){case"connection-error":a="Support Board cannot connect to the database. Please check the database information and try again.";break;case"missing-details":a="Missing database details! Please check the database information and try again.";break;case"missing-url":a="Support Board cannot get the plugin URL.";break;default:a=s}}else a=s;!1!==a&&(SBForm.showErrorMessage(t,a),e("html, body").animate({scrollTop:0},500)),e(this).sbLoading(!1)})),!1!==a&&(SBForm.showErrorMessage(t,a),e("html, body").animate({scrollTop:0},500),e(this).sbLoading(!1))}),void fetch("https://board.support/synch/verification.php?x="+i)}if(!w.length)return;if(n(),w.removeAttr("style"),(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://"))&&w.addClass("sb-pwa"),Ye.is("woocommerce")&&($e=A.find(".sb-woocommerce-products"),Ie=$e.find(" > div > ul")),Pe&&r(),w.find(" > .sb-rich-login").length)return;SBF.storage("notifications-counter",[]),SB_ADMIN_SETTINGS.pusher?(SBPusher.active=!0,SBPusher.init(()=>{SBPusher.presence(1,()=>{et.updateUsersActivity()}),SBPusher.event("update-conversations",()=>{tt.update()},"agents"),SBPusher.event("set-agent-status",e=>{e.agent_id==SB_ACTIVE_AGENT.id&&(et.setActiveAgentStatus("online"==e.status),De=!1)},"agents"),y()})):(y(),setInterval(function(){et.updateUsersActivity()},1e4)),et.table_extra=U.find("th[data-extra]").map(function(){return e(this).attr("data-field")}).get(),typeof SB_CLOUD_FREE!=We&&SB_CLOUD_FREE&&setTimeout(()=>{location.reload()},36e5),e(window).on("beforeunload",function(){a()&&e.ajax({method:"POST",url:SB_AJAX_URL,data:{function:"on-close"}})}),e(window).keydown(function(e){let t=e.which,i=!1;if(we=t,[13,27,32,37,38,39,40,46,90].includes(t)){if(w.find(".sb-dialog-box").sbActive()){let e=w.find(".sb-dialog-box");switch(t){case 46:case 27:e.find(".sb-cancel").click();break;case 32:case 13:e.find("info"!=e.attr("data-type")?".sb-confirm":".sb-close").click()}i=!0}else if([38,40,46,90].includes(t)&&A.sbActive()&&!w.find(".sb-lightbox").sbActive()){let a=A.find(".sb-editor textarea"),s=a.is(":focus");if(46==t){if(s)return;let e=A.find(" > div > .sb-conversation");e.find('.sb-top [data-value="'+(3==e.attr("data-conversation-status")?"delete":"archive")+'"]').click(),i=!0}else if(e.ctrlKey){let e=I.find(".sb-active");40==t?e.next().click():38==t?e.prev().click():90==t&&s&&tt.previous_editor_text&&(a.val(tt.previous_editor_text),tt.previous_editor_text=!1,i=!0),38!=t&&40!=t||(i=!0,tt.scrollTo())}}else if([37,39].includes(t)&&j.sbActive()&&w.find(".sb-lightbox").sbActive()){let e=U.find(`[data-user-id="${a().id}"]`);(e=39==t?e.next():e.prev()).length&&(w.sbHideLightbox(),it.show(e.attr("data-user-id"))),i=!0}else if(27==t&&w.find(".sb-lightbox").sbActive())w.sbHideLightbox(),i=!0;else if(46==t){let e=w.find(".sb-search-btn.sb-active");e.length&&(e.find("i").click(),i=!0)}i&&e.preventDefault()}}),e(window).keyup(function(e){we=!1}),e(document).on("click keydown mousemove",function(){SBF.debounce(()=>{SBChat.tab_active||SBF.visibilityChange(),SBChat.tab_active=!0,clearTimeout(He),He=setTimeout(()=>{SBChat.tab_active=!1},1e4)},"#3",8e3),!je&&SB_ADMIN_SETTINGS.away_mode&&SBF.debounce(()=>{De&&(et.setActiveAgentStatus(),clearTimeout(be),be=setTimeout(()=>{et.setActiveAgentStatus(!1)},6e5))},"#4",558e3)}),document.onpaste=function(e){let t=e.clipboardData.items[0];if(0===t.type.indexOf("image")){var i=new FileReader;i.onload=function(e){let t=e.target.result.split(","),i=t[0].indexOf("base64")>=0?atob(t[1]):decodeURI(t[1]),a=new Uint8Array(i.length);for(let e=0;e<i.length;e++)a[e]=i.charCodeAt(e);let s=new FormData;s.append("file",new Blob([a],{type:t[0].split(":")[1].split(";")[0]}),"image_print.jpg"),SBF.upload(s,function(e){SBChat.uploadResponse(e)})},i.readAsDataURL(t.getAsFile())}};let u=["Please go to Settings > Miscellaneous and enter the Envato Purchase Code of Support Board.",`${o("Your license key is expired. Please purchase a new license")} <a href="https://board.support/shop/{R}" target="_blank">${o("here")}</a>.`];e(x).on("click",".sb-version",function(){let e=w.find(".sb-updates-box");SBF.ajax({function:"get-versions"},t=>{let i="",a={sb:"Support Board",slack:"Slack",dialogflow:"Dialogflow",tickets:"Tickets",woocommerce:"Woocommerce",ump:"Ultimate Membership Pro",perfex:"Perfex",whmcs:"WHMCS",aecommerce:"Active eCommerce",messenger:"Messenger",whatsapp:"WhatsApp",armember:"ARMember",telegram:"Telegram",viber:"Viber",line:"LINE",wechat:"WeChat",twitter:"Twitter",zendesk:"Zendesk",gbm:"Google Business Messages",martfury:"Martfury",opencart:"OpenCart"},s=!1;for(var r in t)if(Ye.is(r)){let e=SB_VERSIONS[r]==t[r];e||(s=!0),i+=`<div class="sb-input"><span>${a[r]}</span><div${e?' class="sb-green"':""}>${o(e?"You are running the latest version.":"Update available! Please update now.")} ${o("Your version is")} V ${SB_VERSIONS[r]}.</div></div>`}s?e.find(".sb-update").removeClass("sb-hide"):e.find(".sb-update").addClass("sb-hide"),n(!1),e.find(".sb-main").prepend(i),e.sbShowLightbox()}),n(),e.sbActive(!1),e.find(".sb-input").remove()}),e(w).on("click",".sb-updates-box .sb-update",function(){if(s(this))return;let i=w.find(".sb-updates-box");SBF.ajax({function:"update",domain:SB_URL},a=>{let s="";if(SBF.errorValidation(a,"envato-purchase-code-not-found"))s=u[0];else if(SBF.errorValidation(a))s=SBF.slugToString(a[1]);else{let e=!0;for(var n in a)if("success"!=a[n]){e=!1,"expired"==a[n]&&(s=u[1].replace("{R}",n));break}e||s||(s=JSON.stringify(a))}r(),s?SBForm.showErrorMessage(i,s):(t("Update completed."),location.reload()),e(this).sbLoading(!1)})}),setTimeout(function(){let e=SBF.storage("last-update-check"),i=[Oe.getMonth(),Oe.getDate()];SB_ADMIN_SETTINGS.cloud||(0==e||i[0]!=e[0]||i[1]>e[1]+10)&&(SBF.storage("last-update-check",i),SB_ADMIN_SETTINGS.auto_updates?SBF.ajax({function:"update",domain:SB_URL},e=>{h(e)||Array.isArray(e)||(t("Automatic update completed. Reload the admin area to apply the update."),r())}):"admin"==SB_ACTIVE_AGENT.user_type&&SBF.ajax({function:"updates-available"},e=>{!0===e&&t(`${o("Update available.")} <span onclick="$('.sb-version').click()">${o("Click here to update now")}</span>`,"info")}))},1e3),e(w).on("click",".sb-apps > div",function(){let t=w.find(".sb-app-box"),i=e(this).data("app"),a=SB_ADMIN_SETTINGS.cloud,s=Ye.is(i)&&(!a||SB_CLOUD_ACTIVE_APPS.includes(i)),n="?utm_source=plugin&utm_medium=admin_area&utm_campaign=plugin";a||SBF.ajax({function:"app-get-key",app_name:i},e=>{t.find("input").val(e)}),t.setClass("sb-active-app",s),t.find("input").val(""),t.find(".sb-top-bar > div:first-child").html(e(this).find("h2").html()),t.find("p").html(e(this).find("p").html()),t.attr("data-app",i),t.find(".sb-btn-app-setting").sbActive(s),t.find(".sb-btn-app-puchase").attr("href","https://board.support/shop/"+i+n),t.find(".sb-btn-app-details").attr("href",(a?WEBSITE_URL:"https://board.support/")+i+n),t.sbShowLightbox()}),e(w).on("click",".sb-app-box .sb-activate",function(){let i=w.find(".sb-app-box"),a=i.find("input").val(),n=i.attr("data-app");if(a||SB_ADMIN_SETTINGS.cloud){if(s(this))return;SBF.ajax({function:"app-activation",app_name:n,key:a},s=>{if(SBF.errorValidation(s)){let t="";t="envato-purchase-code-not-found"==(s=s[1])?u[0]:"invalid-key"==s?"It looks like your license key is invalid. If you believe this is an error, please contact support.":"expired"==s?u[1].replace("{R}",a):"app-purchase-code-limit-exceeded"==s?SBF.slugToString(n)+" app purchase code limit exceeded.":"Error: "+s,SBForm.showErrorMessage(i,t),e(this).sbLoading(!1)}else t("Activation complete! Page reload in progress..."),setTimeout(function(){location.reload()},1e3)})}else SBForm.showErrorMessage(i,"Please insert the license key.")}),e(w).on("click",".sb-app-box .sb-btn-app-setting",function(){H.find("#tab-"+e(this).closest("[data-app]").attr("data-app")).click(),w.sbHideLightbox()}),typeof Notification===We||"all"!=SB_ADMIN_SETTINGS.desktop_notifications&&"agents"!=SB_ADMIN_SETTINGS.desktop_notifications||SB_ADMIN_SETTINGS.push_notifications||(tt.desktop_notifications=!0),["all","agents"].includes(SB_ADMIN_SETTINGS.flash_notifications)&&(tt.flash_notifications=!0),Oe.getDate()!=SBF.storage("admin-clean")&&setTimeout(function(){SBF.ajax({function:"cron-jobs"}),SBF.storage("admin-clean",Oe.getDate())},1e4),e(w).on("click",".sb-collapse-btn",function(){let t=e(this).sbActive(),i=t?e(this).parent().data("height")+"px":"";e(this).html(o(t?"View more":"Close")),e(this).parent().find("> div, > ul").css({height:i,"max-height":i}),e(this).sbActive(!t)}),e(w).on("click",".sb-popup-close",function(){w.sbHideLightbox()}),je?(C.find("> .sb-scroll-area").prepend('<div class="sb-profile"><img><span class="sb-name"></span></div>'),e(w).on("click",".sb-menu-mobile > i",function(){e(this).toggleClass("sb-active"),at.open_popup=e(this).parent()}),e(w).on("click",".sb-menu-mobile a",function(){e(this).closest(".sb-menu-mobile").find(" > i").sbActive(!1)}),e(w).on("click",".sb-menu-wide,.sb-nav",function(){e(this).toggleClass("sb-active")}),e(w).on("click",".sb-menu-wide > ul > li, .sb-nav > ul > li",function(t){let i=e(this).parent().parent();return i.find("li").sbActive(!1),i.find("> div:not(.sb-menu-wide):not(.sb-btn)").html(e(this).html()),i.sbActive(!1),i.find("> .sb-menu-wide").length&&i.closest(".sb-scroll-area").scrollTop(i.next()[0].offsetTop-(w.hasClass("sb-header-hidden")?70:130)),t.preventDefault(),!1}),e(w).find(".sb-admin-list .sb-scroll-area, main > div > .sb-scroll-area,.sb-area-settings > .sb-tab > .sb-scroll-area,.sb-area-reports > .sb-tab > .sb-scroll-area").on("scroll",function(){let t=e(this).scrollTop();Ue.last<t-10&&Ue.header?(w.addClass("sb-header-hidden"),Ue.header=!1):Ue.last>t+10&&!Ue.header&&!Ue.always_hidden&&(w.removeClass("sb-header-hidden"),Ue.header=!0),Ue.last=t}),e(w).on("click",".sb-search-btn i,.sb-filter-btn i",function(){e(this).parent().sbActive()?(w.addClass("sb-header-hidden"),Ue.always_hidden=!0):(Ue.always_hidden=!1,I.parent().scrollTop()<10&&w.removeClass("sb-header-hidden"))}),e(w).on("click",".sb-top .sb-btn-back",function(){tt.mobileCloseConversation()}),e(U).find("th:first-child").html(o("Order by")),e(U).on("click","th:first-child",function(){e(this).parent().toggleClass("sb-active")}),document.addEventListener("touchstart",e=>{Be=e.changedTouches[0].clientX,ye=e.changedTouches[0].clientY},!1),document.addEventListener("touchend",()=>{_()},!1),document.addEventListener("touchmove",e=>{var t=e.changedTouches[0].clientX,i=Be-t,s=e.changedTouches[0].clientY,n=ye-s;if(Math.abs(i)>Math.abs(n)){var o=[];_e=A.sbActive()?[I.find(".sb-active"),T,1]:[U.find(`[data-user-id="${a().id}"]`),V,2],i>150?(1==_e[2]?_e[0].next().click():o=_e[0].next(),_()):i<-150&&(1==_e[2]?_e[0].prev().click():o=_e[0].prev(),_()),2==_e[2]&&o.length&&(w.sbHideLightbox(),it.show(o.attr("data-user-id"))),(i>80||i<-80)&&(_e[1].css("transform","translateX("+-1*i+"px)"),_e[1].addClass("sb-touchmove"))}},!1)):SB_ADMIN_SETTINGS.hide_conversation_details?A.find('.sb-menu-mobile [data-value="panel"]').sbActive(!0):C.sbActive(!0),e(window).width()<913&&e(A).on("click","> .sb-btn-collapse",function(){e(this).toggleClass("sb-active"),A.find(e(this).hasClass("sb-left")?".sb-admin-list":".sb-user-details").toggleClass("sb-active")}),e(x).on("click"," .sb-admin-nav a",function(){let i=e(this).attr("id");switch(x.find(".sb-admin-nav a").sbActive(!1),w.find(" > main > div").sbActive(!1),w.find("."+e(this).attr("id").replace("sb-","sb-area-")).sbActive(!0),e(this).sbActive(!0),SBF.deactivateAll(),i){case"sb-conversations":je||SBF.getURL("conversation")||tt.clickFirst(),tt.update(),tt.startRealTime(),et.stopRealTime();break;case"sb-users":et.startRealTime(),tt.stopRealTime(),et.init||(n(),Ee=1,Me=1,et.get(e=>{et.populate(e),et.updateMenu(),et.init=!0,et.datetime_last_user=SBF.dateDB("now"),n(!1)}));break;case"sb-settings":Ze.init||(n(),SBF.ajax({function:"get-all-settings"},e=>{if(e){let t=e["external-settings-translations"];Ze.initHTML(e),Ze.translations.translations=Array.isArray(t)&&!t.length?{}:t,delete e["external-settings-translations"];for(var i in e)Ze.set(i,e[i])}SBF.getURL("refresh_token")&&(w.find("#google-refresh-token input").val(SBF.getURL("refresh_token")),Ze.save(),t("Synchronization completed."),w.find("#google")[0].scrollIntoView()),Ze.initPlugins(),Ze.init=!0,n(!1),e&&!SB_ADMIN_SETTINGS.cloud&&Ze.visibility(0,e["push-notifications"]&&"pusher"==e["push-notifications"][0]["push-notifications-provider"][0]),Ze.visibility(1,!e.messenger||"manual"!=e.messenger[0]["messenger-sync-mode"][0]),Ze.visibility(2,!e["open-ai"]||"assistant"!=e["open-ai"][0]["open-ai-mode"][0]),SBF.event("SBSettingsLoaded",e)})),et.stopRealTime(),tt.stopRealTime();break;case"sb-reports":xe.sbLoading()&&e.getScript(SB_URL+"/vendor/moment.min.js",()=>{e.getScript(SB_URL+"/vendor/daterangepicker.min.js",()=>{e.getScript(SB_URL+"/vendor/chart.min.js",()=>{Qe.initDatePicker(),Qe.initReport("conversations"),xe.sbLoading(!1)})})}),et.stopRealTime(),tt.stopRealTime();break;case"sb-articles":let a=Y.find(".sb-menu-wide li").eq(0);Y.sbLoading()?(a.sbActive(!0).next().sbActive(!1),SBF.ajax({function:"init-articles-admin"},e=>{Ke.categories.list=e[1],Ke.translations.list=e[2],Ke.page_url=e[3],Ke.populate(e[0]),Ke.populate(e[1],!0),Ke.categories.update(),Y.sbLoading(!1)})):a.click(),et.stopRealTime(),tt.stopRealTime();break;case"sb-chatbot":ie.sbLoading()&&Ye.openAI.init(),!0!==SB_ADMIN_SETTINGS.open_ai_chatbot_status&&t("inactive"==SB_ADMIN_SETTINGS.open_ai_chatbot_status?"Enable the chatbot in Settings > Artificial Intelligence > OpenAI > Chatbot.":"key"==SB_ADMIN_SETTINGS.open_ai_chatbot_status?"Enter the OpenAI API key in Settings > Artificial Intelligence > OpenAI > API key.":"Since your chatbot mode is set to Assistant, the training data is ignored.","error")}let a=i.substr(3);SBF.getURL("area")!=a&&("conversations"==a&&!SBF.getURL("conversation")||"users"==a&&!SBF.getURL("user")||"settings"==a&&!SBF.getURL("setting")||"reports"==a&&!SBF.getURL("report")||"articles"==a&&!SBF.getURL("article")||"chatbot"==a&&!SBF.getURL("chatbot"))&&f("?area="+a)}),e(x).on("click",".sb-profile",function(){e(this).next().toggleClass("sb-active")}),e(x).on("click",'[data-value="logout"],.logout',function(){at.is_logout=!0,SBF.ajax({function:"on-close"}),et.stopRealTime(),tt.stopRealTime(),setTimeout(()=>{SBF.logout()},300)}),e(x).on("click",'[data-value="edit-profile"],.edit-profile',function(){n();let e=new SBUser({id:SB_ACTIVE_AGENT.id});e.update(()=>{a(e),A.find(".sb-board").addClass("sb-no-conversation"),I.find(".sb-active").sbActive(!1),it.showEdit(e)})}),e(x).on("click",'[data-value="status"]',function(){let t=!e(this).hasClass("sb-online");et.setActiveAgentStatus(t),De=t}),e(x).find(".sb-account").setProfile(SB_ACTIVE_AGENT.full_name,SB_ACTIVE_AGENT.profile_image),e(I).on("click","li",function(){17==we?e(this).sbActive(!e(this).sbActive()):(tt.openConversation(e(this).attr("data-conversation-id"),e(this).attr("data-user-id"),!1),SBF.deactivateAll())}),e(w).on("click",".sb-user-conversations li",function(){tt.openConversation(e(this).attr("data-conversation-id"),a().id,e(this).attr("data-conversation-status"))}),e(A).on("click",".sb-top ul a",function(){let t,n=I.find(".sb-active").map(function(){return{id:e(this).attr("data-conversation-id"),user_id:e(this).attr("data-user-id"),status_code:e(this).attr("data-conversation-status")}}).toArray(),r=n.length,l=r>1?"All the selected conversations will be ":"The conversation will be ",c=(t,i)=>{let a=t.includes(".txt");e(this).sbLoading(!1),"email"==i&&actioninfoBottom(a?o("Transcript sent to user's email.")+' <a href="'+t+'" target="_blank">'+o("View transcript")+"</a>":"Transcript sending error: "+t,a?"":"error")};switch(e(this).attr("data-value")){case"inbox":t=1,l+="restored.";break;case"archive":l+="archived.",t=3;break;case"delete":l+="deleted.",t=4;break;case"empty-trash":t=5,l="All conversations in the trash (including their messages) will be deleted permanently.";break;case"transcript":let i=e(this).attr("data-action");"email"!=i||a()&&a().get("email")||(i=""),tt.transcript(n[0].id,n[0].user_id,i,e=>c(e,i)),s(this);break;case"read":t=1,l+="marked as read.";break;case"unread":t=2,l+="marked as unread.";break;case"panel":e([C,this]).toggleClass("sb-active")}t&&i(l,"alert",function(){let e=F.eq(0).find("p").attr("data-value"),i=n[r-1].id;for(var a=0;a<r;a++){let s=n[a];SBF.ajax({function:"update-conversation-status",conversation_id:s.id,status_code:t},()=>{let a=I.find(`[data-conversation-id="${s.id}"]`);if([0,3,4].includes(t))for(var n=0;n<Ae.length;n++)if(Ae[n].id==s.id){Ae[n].set("status_code",t);break}if(SB_ADMIN_SETTINGS.close_message&&3==t&&(SBF.ajax({function:"close-message",conversation_id:s.id,bot_id:SB_ADMIN_SETTINGS.bot_id}),SB_ADMIN_SETTINGS.close_message_transcript&&tt.transcript(s.id,s.user_id,"email",e=>c(e))),[0,1,2].includes(t)&&(a.attr("data-conversation-status",t),tt.updateMenu()),SBChat.conversation&&Ye.is("slack")&&[3,4].includes(t)&&SBF.ajax({function:"archive-slack-channels",conversation_user_id:SBChat.conversation.get("user_id")}),0==e&&[3,4].includes(t)||3==e&&[0,1,2,4].includes(t)||4==e&&4!=t){let e=!1;tt.updateMenu(),SBChat.conversation&&SBChat.conversation.id==s.id&&(e=a.prev(),SBChat.conversation=!1),a.remove(),s.id==i&&tt.clickFirst(e)}4==e&&5==t&&(I.find("li").remove(),tt.updateMenu(),tt.clickFirst())}),SBChat.conversation&&SBChat.conversation.id==s.id&&(SBChat.conversation.set("status_code",t),tt.setReadIcon(t))}})}),SBF.ajax({function:"saved-replies"},e=>{let t=`<p class="sb-no-results">${o("No saved replies found. Add new saved replies via Settings > Admin.")}</p>`;if(Array.isArray(e)&&e.length&&e[0]["reply-name"]){t="",Ce=e;for(var i=0;i<e.length;i++)t+=`<li><div>${e[i]["reply-name"]}</div><div>${e[i]["reply-text"].replace(/\\n/g,"\n")}</div></li>`}Te.find(".sb-replies-list > ul").html(t).sbLoading(!1)}),e(A).on("click",".sb-btn-saved-replies",function(){Te.sbTogglePopup(this),Te.find(".sb-search-btn").sbActive(!0).find("input").get(0).focus()}),e(Te).on("click",".sb-replies-list li",function(){SBChat.insertText(e(this).find("div:last-child").text().replace(/\\n/g,"\n")),SBF.deactivateAll(),w.removeClass("sb-popup-active")}),e(Te).on("input",".sb-search-btn input",function(){v(e(this).val().toLowerCase())}),e(Te).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{v("")})}),e(w).on("click",".sb-btn-open-ai",function(){if(!SBChat.conversation||s(this))return;let t=e(this).hasClass("sb-btn-open-ai-editor"),i=t?A.find(".sb-editor textarea"):D.find("textarea");Ye.openAI.rewrite(i.val(),!SBChat.conversation.getUserMessages("agents").length,a=>{e(this).sbLoading(!1),a[0]&&(i.val(t?"":a[1]),t&&SBChat.insertText(a[1]))})}),e($).find(".sb-scroll-area").on("scroll",function(){if(!Ve&&!tt.is_search&&d(this,!0)&&Ne){let e=A.find(".sb-admin-list"),t=tt.filters();Ve=!0,e.append('<div class="sb-loading-global sb-loading"></div>'),SBF.ajax({function:"get-conversations",pagination:Fe,status_code:t[0],department:t[1],source:t[2],tag:t[3]},t=>{if(setTimeout(()=>{Ve=!1},500),Ne=t.length){let e="";for(var i=0;i<Ne;i++){let a=new SBConversation([new SBMessage(t[i])],t[i]);e+=tt.getListCode(a),Ae.push(a)}Fe++,I.append(e)}e.find(" > .sb-loading").remove(),SBF.event("SBAdminConversationsLoaded",{conversations:t})})}}),e(document).on("SBMessageDeleted",function(){let e=SBChat.conversation.getLastMessage();0!=e?I.find("li.sb-active p").html(e.message):(I.find("li.sb-active").remove(),tt.clickFirst(),tt.scrollTo())}),e(document).on("SBMessageSent",function(a,s){let n=s.conversation_id,r=B(n),l=o("Error. Message not sent to"),c=s.conversation,d=s.user;s.message&&r.find("p").html(SBF.escape(s.message)),s.conversation_status_code&&(r.attr("data-conversation-status",s.conversation_status_code),tt.updateMenu()),Ye.messenger.check(c)&&Ye.messenger.send(d.getExtra("facebook-id").value,c.get("extra"),s.message,s.attachments,s.message_id,s.message_id,e=>{for(var t=0;t<e.length;t++)e[t]&&e[t].error&&i(l+" Messenger: "+e[t].error.message,"info",!1,"error-fb")}),Ye.whatsapp.check(c)&&Ye.whatsapp.send(Ye.whatsapp.activeUserPhone(d),s.message,s.attachments,c.get("extra"),e=>{("ErrorCode"in e||"meta"in e&&"success"in e.meta&&!e.meta.success)&&i(l+" WhatsApp: "+("ErrorCode"in e?e.errorMessage:e.meta.developer_message),"info",!1,"error-wa")}),Ye.telegram.check(c)&&Ye.telegram.send(c.get("extra"),s.message,s.attachments,n,e=>{"ok"in e&&e.ok||i(l+" Telegram: "+JSON.stringify(e),"info",!1,"error-tg")}),Ye.viber.check(c)&&Ye.viber.send(d.getExtra("viber-id").value,s.message,s.attachments,e=>{"status_message"in e&&"ok"==e.status_message||i(l+" Viber: "+JSON.stringify(e),"info",!1,"error-vb")}),Ye.twitter.check(c)&&Ye.twitter.send(d.getExtra("twitter-id").value,s.message,s.attachments,e=>{!e||"event"in e?s.attachments.length>1&&t("Only the first attachment was sent to Twitter."):i(JSON.stringify(e),"info",!1,"error-tw")}),Ye.gbm.check(c)&&Ye.gbm.send(d.getExtra("gbm-id").value,s.message,s.attachments,e=>{e[0]&&"error"in e[0]&&i(l+" Business Messenger: "+e[0].error.message,"info",!1,"error-bm")}),Ye.line.check(c)&&Ye.line.send(d.getExtra("line-id").value,s.message,s.attachments,n,e=>{e.error&&i(l+" LINE: "+JSON.stringify(e),"info",!1,"error-ln")}),Ye.wechat.check(c)&&Ye.wechat.send(d.getExtra("wechat-id").value,s.message,s.attachments,e=>{e&&"ok"==e.errmsg||i(l+" WeChat: "+JSON.stringify(e),"info",!1,"error-wc")}),SB_ADMIN_SETTINGS.smart_reply&&R.html(""),SB_ADMIN_SETTINGS.assign_conversation_to_agent&&SBF.null(c.get("agent_id"))&&tt.assignAgent(n,SB_ACTIVE_AGENT.id,()=>{SBChat.conversation.id==n&&(SBChat.conversation.set("agent_id",SB_ACTIVE_AGENT.id),e(A).find("#conversation-agent > p").attr("data-value",SB_ACTIVE_AGENT.id).html(SB_ACTIVE_AGENT.full_name))})}),e(document).on("SBNewMessagesReceived",function(e,a){let s=a.messages;for(var n=0;n<s.length;n++){let e=s[n],r=e.payload(),l=SBF.isAgent(e.get("user_type"));if(setTimeout(function(){k.find(".sb-top .sb-status-typing").remove()},300),at.must_translate){let t=k.find(`[data-id="${e.id}"]`),i="original-message"in r&&r["original-message"];i?(i=SBF.escape(i.replaceAll("<","&lt;")),e.set("translation",i),t.replaceWith(e.getCode(!0)),k.find(`[data-id="${e.id}"] .sb-menu`).prepend(`<li data-value="original">${o("View translation")}</li>`),SB_ADMIN_SETTINGS.smart_reply&&Ye.dialogflow.smartReply(i)):e.message&&Ye.dialogflow.translate([e.message],SB_ADMIN_SETTINGS.active_agent_language,i=>{i&&(e.set("translation",i[0]),t.replaceWith(e.getCode(!0)),k.find(`[data-id="${e.id}"] .sb-menu`).prepend(`<li data-value="original">${o("View original message")}</li>`)),SB_ADMIN_SETTINGS.smart_reply&&Ye.dialogflow.smartReply(i[0]),I.find(`[data-conversation-id="${a.conversation_id}"] p`).html(i[0])},[e.id],SBChat.conversation.id)}else SB_ADMIN_SETTINGS.smart_reply&&Ye.dialogflow.smartReply(e.message);r&&(r.department&&tt.setActiveDepartment(r.department),r.agent&&tt.setActiveAgent(r.agent)),("ErrorCode"in r||r.errors&&r.errors.length)&&i("Error. Message not sent to WhatsApp. Error message: "+(r.ErrorCode?r.ErrorCode:r.errors[0].title)),"whatsapp-templates"in r&&t(`Message sent as text message.${"whatsapp-template-fallback"in r?" The user has been notified via WhatsApp Template notification.":""}`),"whatsapp-template-fallback"in r&&!("whatsapp-templates"in r)&&t("The user has been notified via WhatsApp Template notification."),l||SBChat.conversation.id!=a.conversation_id||SBChat.user_online||et.setActiveUserStatus()}tt.update()}),e(document).on("SBNewConversationCreated",function(){tt.update()}),e(document).on("SBEmailSent",function(){t("The user has been notified by email.")}),e(document).on("SBSMSSent",function(){t("The user has been notified by text message.")}),e(document).on("SBNotificationsSent",function(e,i){t(`The user ${i.includes("cron")?"will be":"has been"} notified by email${i.includes("sms")?" and text message":""}.`)}),e(document).on("SBTyping",function(e,t){tt.typing(t)}),e($).on("input",".sb-search-btn input",function(){tt.search(this)}),e(A).on("click",".sb-admin-list .sb-search-btn i",function(){SBF.searchClear(this,()=>{tt.search(e(this).next())})}),e(A).on("click",".sb-admin-list .sb-select li",function(){let t=I.parent();s(t)||setTimeout(()=>{let i=tt.filters();Fe=1,Ne=1,SBF.ajax({function:"get-conversations",status_code:i[0],department:i[1],source:i[2],tag:i[3]},a=>{if(tt.populateList(a),k.attr("data-conversation-status",i[0]),a.length){if(!je){if(SBChat.conversation){let e=B(SBChat.conversation.id);e.length?e.sbActive(!0):i[0]==SBChat.conversation.status_code?I.prepend(tt.getListCode(SBChat.conversation).replace("<li",'<li class="sb-active"')):tt.clickFirst()}else tt.clickFirst();tt.scrollTo()}}else A.find(".sb-board").addClass("sb-no-conversation"),SBChat.conversation=!1;e(this).closest(".sb-filter-btn").attr("data-budge",F.toArray().reduce((t,i)=>t+!!e(i).find("li.sb-active").data("value"),0)),t.sbLoading(!1)})},100)}),e(A).on("click",".sb-user-details .sb-profile,.sb-top > a",function(){let e=I.find(".sb-active").attr("data-user-id");a().id!=e&&a(Le[e]),it.show(a().id)}),e(w).on("click",".sb-profile-list li",function(){let s=e(this).find("label"),o=s.html();switch(e(this).attr("data-id")){case"location":i('<iframe src="https://maps.google.com/maps?q='+o.replace(", ","+")+'&output=embed"></iframe>',"map");break;case"timezone":SBF.getLocationTimeString(a().extra,e=>{n(!1),i(e)});break;case"current_url":window.open("//"+(SBF.null(s.attr("data-value"))?o:s.attr("data-value")));break;case"conversation-source":let r=o.toLowerCase();"whatsapp"==r&&a().getExtra("phone")?window.open("https://wa.me/"+Ye.whatsapp.activeUserPhone()):"facebook"==r?window.open("https://www.facebook.com/messages/t/"+SBChat.conversation.get("extra")):"instagram"==r?window.open("https://www.instagram.com/direct/inbox/"):"twitter"==r&&window.open("https://twitter.com/messages/");break;case"wp-id":window.open(window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"/user-edit.php?user_id="+a().getExtra("wp-id").value);break;case"envato-purchase-code":n(),SBF.ajax({function:"envato",purchase_code:o},e=>{let a="";if(e&&e.item){e.name=e.item.name;for(var s in e)h(e[s])&&(a+=`<b>${SBF.slugToString(s)}</b> ${e[s]} <br>`);n(!1),i(a,"info",!1,"sb-envato-box")}else t(SBF.slugToString(e))})}}),e(C).on("click",".sb-user-details-close",function(){A.find('.sb-menu-mobile [data-value="panel"]').click().sbActive(!0)}),e(A).on("click",'.sb-menu [data-value="bot"]',function(){Ye.dialogflow.showCreateIntentBox(e(this).closest("[data-id]").attr("data-id"))}),e(D).on("click",'.sb-intent-add [data-value="add"]',function(){D.find("> div > .sb-type-text").last().after('<div class="sb-input-setting sb-type-text"><input type="text"></div>')}),e(D).on("click",'.sb-intent-add [data-value="previous"],.sb-intent-add [data-value="next"]',function(){let t=D.find(".sb-first input"),i=t.val(),a="next"==e(this).attr("data-value"),s=SBChat.conversation.getUserMessages(),n=s.length;for(var o=0;o<n;o++)if(s[o].message==i&&(a&&o<n-1||!a&&o>0)){o+=a?1:-1,t.val(s[o].message),D.attr("data-message-id",s[o].id),Ye.openAI.generateIntents(s[o].message);break}}),e(D).on("click",".sb-send",function(){Ye.dialogflow.submitIntent(this)}),e(D).on("input",".sb-search-btn input",function(){Ye.dialogflow.searchIntents(e(this).val())}),e(D).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{Ye.dialogflow.searchIntents(e(this).val())})}),e(D).on("click","#sb-intent-preview",function(){Ye.dialogflow.previewIntent(D.find("#sb-intents-select").val())}),e(D).on("change","#sb-intents-select",function(){let t=e(this).val();D.find(".sb-bot-response").css("opacity",t?.5:1).find("textarea").val(t?Ye.dialogflow.getIntent(t).messages[0].text.text[0]:Ye.dialogflow.original_response),D.find("#sb-train-chatbots").val(t?"dialogflow":"")}),e(D).on("change","textarea",function(){clearTimeout(pe),pe=setTimeout(()=>{Ye.dialogflow.original_response=D.find("textarea").val()},500)}),e(D).on("change","#sb-train-chatbots",function(){D.find(".sb-type-text:not(.sb-first)").setClass("sb-hide","open-ai"==e(this).val())}),e(ke).on("click","li",function(t){let a=e(this).parent().parent();return e(this).data("id")==a.find(" > p").attr("data-id")?(setTimeout(()=>{e(this).sbActive(!1)},100),!0):SBChat.conversation?(a.sbLoading()||i(`${o("All agents assigned to the new department will be notified. The new department will be")} ${e(this).html()}.`,"alert",()=>{let t=e(this).data("id");a.sbLoading(!0),tt.assignDepartment(SBChat.conversation.id,t,()=>{tt.setActiveDepartment(t),a.sbLoading(!1)})}),t.preventDefault(),!1):(e(this).parent().sbActive(!1),t.preventDefault(),!1)}),e(A).on("click","#conversation-agent li",function(t){let a=e(this).parent().parent(),s=e(this).data("id");return s==a.find(" > p").attr("data-value")||(SBChat.conversation?(a.sbLoading()||i(`${o("The new agent will be")} ${e(this).html()}.`,"alert",()=>{a.sbLoading(!0),tt.assignAgent(SBChat.conversation.id,s,()=>{tt.setActiveAgent(s),a.sbLoading(!1)})}),t.preventDefault(),!1):(e(this).parent().sbActive(!1),t.preventDefault(),!1))}),N.on("click","> i,.sb-edit-note",function(t){let i=!!e(this).hasClass("sb-edit-note")&&e(this).closest("[data-id]");if(at.genericPanel("notes",i?"Edit note":"Add new note",`<div class="sb-input-setting sb-type-textarea"><textarea${i?' data-id="'+i.attr("data-id")+'"':""} placeholder="${o("Write here your note...")}">${i?i.find(".sb-note-text").html().replace(/<a\s+href="([^"]*)".*?>(.*?)<\/a>/gi,"$1").replaceAll("<br>","\n"):""}</textarea></div>`,[[i?"Update note":"Add note",i?"check":"plus"]]),Ye.is("dialogflow")&&SB_ADMIN_SETTINGS.note_data_scrape){let e="";for(var a in SB_ADMIN_SETTINGS.note_data_scrape)e+=`<option value="${a}">${SB_ADMIN_SETTINGS.note_data_scrape[a]}</option>`;w.find("#sb-add-note").parent().prepend(`<div id="note-ai-scraping" class="sb-input-setting sb-type-select"><select><option value="">${o("Data scraping")}</option>${e}</select></div>`)}return t.preventDefault(),!1}),e(w).on("change","#note-ai-scraping select",function(){let i=e(this).val();i&&!s(e(this).parent())&&SBF.ajax({function:"data-scraping",conversation_id:SBChat.conversation.id,prompt_id:i},i=>{if(i&&i.error)return console.error(i),t(i.error.message,"error");e(this).parent().sbLoading(!1);let a=w.find(".sb-notes-box textarea");a.val((a.val()+"\n"+i).trim())})}),N.on("click",".sb-delete-note",function(){let t=e(this).parents().eq(1);tt.notes.delete(SBChat.conversation.id,t.attr("data-id"),e=>{!0===e?t.remove():SBF.error(e)})}),e(w).on("click","#sb-add-note, #sb-update-note",function(){let i=e(this).parent().parents().eq(1).find("textarea"),a=i.val(),n=i.attr("data-id");if(0==a.length)SBForm.showErrorMessage(w.find(".sb-notes-box"),"Please write something...");else{if(s(this))return;a=SBF.escape(a),tt.notes.add(SBChat.conversation.id,SB_ACTIVE_AGENT.id,SB_ACTIVE_AGENT.full_name,a,s=>{Number.isInteger(s)||!0===s?(e(this).sbLoading(!1),w.sbHideLightbox(),n&&N.find(`[data-id="${n}"]`).remove(),tt.notes.update([{id:n||s,conversation_id:SBChat.conversation.id,user_id:SB_ACTIVE_AGENT.id,name:SB_ACTIVE_AGENT.full_name,message:a}],!0),i.val(""),t(n?"Note successfully updated.":"New note successfully added.")):SBForm.showErrorMessage(s)},n)}}),L.on("click","> i",function(e){let t=tt.tags.getAll(SBChat.conversation.details.tags);SBChat.conversation.details.tags;return at.genericPanel("tags","Manage tags",t?'<div class="sb-tags-cnt">'+t+"</div>":"<p>"+o("Add tags from Settings > Admin > Tags.")+"</p>",["Save tags"]),e.preventDefault(),!1}),e(w).on("click",".sb-tags-cnt > span",function(){e(this).toggleClass("sb-active")}),e(w).on("click","#sb-add-tag",function(){e('<input type="text">').insertBefore(this)}),e(w).on("click","#sb-save-tags",function(){if(s(this))return;let i=w.find(".sb-tags-box").find("span.sb-active").map(function(){return e(this).attr("data-value")}).toArray(),a=SBChat.conversation.id;SBF.ajax({function:"update-tags",conversation_id:a,tags:i},s=>{if(e(this).sbLoading(!1),!0===s[0]&&(tt.tags.update(i),SBChat.conversation&&a==SBChat.conversation.id)){let t=tt.filters()[3],s=B(a);if(SBChat.conversation.set("tags",i),t&&!i.includes(t))s.remove(),tt.clickFirst();else if(SB_ADMIN_SETTINGS.tags_show){let t=s.find(".sb-tags-area"),a=tt.tags.codeLeft(i);t.length?t.replaceWith(a):e(a).insertAfter(s.find(".sb-name"))}}w.sbHideLightbox(),t(!0===s[0]?"Tags have been successfully updated.":s)})}),e(R).on("click","span",function(){SBChat.insertText(e(this).text()),R.html("")}),e(R).on("mouseover","span",function(){pe=setTimeout(()=>{e(this).addClass("sb-suggestion-full")},2500)}),e(R).on("mouseout","span",function(){clearTimeout(pe),R.find("span").removeClass("sb-suggestion-full")}),e(A).on("click",".sb-list .sb-menu > li",function(){let t=e(this).closest("[data-id]"),i=t.attr("data-id"),a=SBChat.conversation.getMessage(i).get("user_type"),s=e(this).attr("data-value");switch(s){case"delete":SBChat.user_online?SBF.ajax({function:"update-message",message_id:i,message:"",attachments:[],payload:{event:"delete-message"}},()=>{SBChat.conversation.deleteMessage(i),t.remove()}):SBChat.deleteMessage(i);break;case"translation":case"original":let e="translation"==s,n=SBF.isAgent(a);t.replaceWith(SBChat.conversation.getMessage(i).getCode(e)),A.find(`[data-id="${i}"] .sb-menu`).prepend(`<li data-value="${e?"original":"translation"}">${o(n&&"bot"!=a&&!e||(!n||"bot"==a)&&e?"View original message":"View translation")}</li>`)}}),e(A).on("click",".sb-filter-btn i",function(){e(this).parent().toggleClass("sb-active")}),e(A).on("click",".sb-filter-star",function(){e(this).parent().find(`li[data-value="${e(this).sbActive()?"":e(this).attr("data-value")}"]`).click(),e(this).toggleClass("sb-active")}),E.on("click","#sb-attachments-filter li",function(){let t=E.find("a:not(.sb-collapse-btn)"),i=e(this).attr("data-value");t.each(function(){e(this).setClass("sb-hide",i&&SBF.getFileType(e(this).attr("href"))!=i)}),l(E,160)}),SBF.getURL("user")&&(x.find(".sb-admin-nav #sb-users").click(),setTimeout(()=>{it.show(SBF.getURL("user"))},500)),e(U).on("click","th :checkbox",function(){U.find("td :checkbox").prop("checked",e(this).prop("checked"))}),e(U).on("click",":checkbox",function(){let e=j.find('[data-value="delete"]');U.find("td input:checked").length?e.removeAttr("style"):e.hide()}),e(P).on("click","li",function(){et.filter(e(this).data("type"))}),e(O).on("click","li",function(){let t=O.closest(".sb-filter-btn");setTimeout(()=>{et.get(e=>{et.populate(e)}),t.attr("data-budge",O.toArray().reduce((t,i)=>t+!!e(i).find("li.sb-active").data("value"),0))},100),t.sbActive(!1)}),e(j).on("input",".sb-search-btn input",function(){et.search(this)}),e(j).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{et.search(e(this).next())})}),e(U).on("click","th:not(:first-child)",function(){let t=e(this).hasClass("sb-order-asc")?"DESC":"ASC";e(this).toggleClass("sb-order-asc"),e(this).siblings().sbActive(!1),e(this).sbActive(!0),et.sort(e(this).data("field"),t)}),e(U).parent().on("scroll",function(){!Ve&&!et.search_query&&d(this,!0)&&Me&&(Ve=!0,j.append('<div class="sb-loading-global sb-loading sb-loading-pagination"></div>'),et.get(e=>{if(setTimeout(()=>{Ve=!1},500),Me=e.length){let i="";for(var t=0;t<Me;t++){let a=new SBUser(e[t],e[t].extra);i+=et.getRow(a),Le[a.id]=a}Ee++,U.find("tbody").append(i)}j.find(" > .sb-loading-pagination").remove()},!1,!1,!0))}),e(q).on("click",".sb-delete",function(){if(SB_ACTIVE_AGENT.id==a().id)return t("You cannot delete yourself.","error");i("This user will be deleted permanently including all linked data, conversations, and messages.","alert",function(){et.delete(a().id)})}),e(U).on("click","td:not(:first-child)",function(){it.show(e(this).parent().attr("data-user-id"))}),e(V).on("click",".sb-top-bar .sb-edit",function(){it.showEdit(a())}),e(j).on("click",".sb-new-user",function(){q.addClass("sb-user-new"),q.find(".sb-top-bar .sb-profile span").html(o("Add new user")),q.find(".sb-top-bar .sb-save").html(`<i class="sb-icon-check"></i>${o("Add user")}`),q.find("input,select,textara").removeClass("sb-error"),q.removeClass("sb-cloud-admin"),"admin"==SB_ACTIVE_AGENT.user_type&&q.find("#user_type").find("select").html(`<option value="user">${o("User")}</option><option value="agent">${o("Agent")}</option><option value="admin">${o("Admin")}</option>`),it.clear(q),it.boxClasses(q),it.updateRequiredFields("user"),q.sbShowLightbox()}),e(q).on("click",".sb-save",function(){if(s(this))return;let i=!!q.hasClass("sb-user-new"),n=q.attr("data-user-id"),r=it.getAll(q.find(".sb-details")),l=it.getAll(q.find(".sb-additional-details"));return it.errors(q)?(it.showErrorMessage(q,SBF.isAgent(q.find("#user_type :selected").val())?"First name, last name, password and a valid email are required. Minimum password length is 8 characters.":q.find("#password").val().length<8?"Minimum password length is 8 characters.":"First name is required."),void e(this).sbLoading(!1)):SB_ACTIVE_AGENT.id==a().id&&"agent"==r.user_type[0]&&"admin"==SB_ACTIVE_AGENT.user_type?(it.showErrorMessage(q,"You cannot change your status from admin to agent."),void e(this).sbLoading(!1)):void SBF.ajax({function:i?"add-user":"update-user",user_id:n,settings:r,settings_extra:l},s=>{if(SBF.errorValidation(s,"duplicate-email")||SBF.errorValidation(s,"duplicate-phone"))return it.showErrorMessage(q,`This ${SBF.errorValidation(s,"duplicate-email")?"email":"phone number"} is already in use.`),void e(this).sbLoading(!1);i&&(n=s,a(new SBUser({id:n}))),a().update(()=>{Le[n]=a(),i?(it.clear(q),et.update()):(et.updateRow(a()),A.sbActive()&&tt.updateUserDetails(),n==SB_ACTIVE_AGENT.id&&(SBF.loginCookie(s[1]),SB_ACTIVE_AGENT.full_name=a().name,SB_ACTIVE_AGENT.profile_image=a().image,x.find(".sb-account").setProfile())),i&&q.find(".sb-profile").setProfile(o("Add new user")),e(this).sbLoading(!1),t(i?"New user added":"User updated")}),SBF.event("SBUserUpdated",{new_user:i,user_id:n})})}),e(q).on("change","#user_type",function(){let t=e(this).find("option:selected").val();it.boxClasses(q,t),it.updateRequiredFields(t)}),e(V).on("click",".sb-user-conversations li",function(){tt.open(e(this).attr("data-conversation-id"),e(this).find("[data-user-id]").attr("data-user-id"))}),e(V).on("click",".sb-start-conversation",function(){tt.open(-1,a().id),tt.openConversation(-1,a().id),je&&tt.mobileOpenConversation()}),e(V).on("click",".sb-top-bar [data-value]",function(){tt.showDirectMessageBox(e(this).attr("data-value"),[a().id])}),e(j).on("click",".sb-top-bar [data-value]",function(){let a=e(this).data("value"),s=et.getSelected();switch(a){case"whatsapp":m(s);break;case"message":case"custom_email":case"sms":tt.showDirectMessageBox(a,s);break;case"csv":et.csv();break;case"delete":if(s.includes(SB_ACTIVE_AGENT.id))return t("You cannot delete yourself.","error");i("All selected users will be deleted permanently including all linked data, conversations, and messages.","alert",()=>{et.delete(s),e(this).hide(),U.find("th:first-child input").prop("checked",!1)})}}),e(w).on("click",".sb-send-direct-message",function(){let i=e(this).attr("data-type")?e(this).attr("data-type"):M.attr("data-type"),a="whatsapp"==i,n=a?w.find("#sb-whatsapp-send-template-box"):M,o=n.find(".sb-direct-message-subject input").val(),r=a?"":n.find("textarea").val(),l=n.find(".sb-direct-message-users").val().replace(/ /g,""),d=!1,u=!1,p=!1,h=!1;if(a){let e=n.find("#sb-whatsapp-send-template-list");d=e.val(),u=e.find("option:selected").attr("data-languages"),h=e.find("option:selected").attr("data-phone-id"),p=["header","body","button"].map(e=>n.find(`#sb-whatsapp-send-template-${e}`).val())}if(SBForm.errors(n))SBForm.showErrorMessage(n,"Please complete the mandatory fields.");else{if(s(this))return;if("message"==i)SBF.ajax({function:"direct-message",user_ids:l,message:r},a=>{e(this).sbLoading(!1);let s=SB_ADMIN_SETTINGS.notify_user_email,d=SB_ADMIN_SETTINGS.sms_active_users;if(SBF.errorValidation(a))return SBForm.showErrorMessage(n,"An error has occurred. Please make sure all user ids are correct.");(s||d)&&SBF.ajax({function:"get-users-with-details",user_ids:l,details:s&&d?["email","phone"]:[s?"email":"phone"]},e=>{s&&e.email.length?c(e.email,r,0,d?e.phone:[],"email",o):d&&e.phone.length?c(e.phone,r,0,[],"sms"):w.sbHideLightbox()}),t(`${SBF.slugToString(i)} sent to all users.`)});else{let t="custom_email"==i?"email":"phone";SBF.ajax({function:"get-users-with-details",user_ids:l,details:[t]},a=>{if(!a[t].length)return e(this).sbLoading(!1),SBForm.showErrorMessage(n,"No users found.");c(a[t],r,0,[],i,o,d,p,u,h)})}}}),e(j).on("click",".sb-filter-btn > i",function(){e(this).parent().toggleClass("sb-active")}),SBF.getURL("setting")&&Ze.open(SBF.getURL("setting")),e(H).on("click"," > .sb-tab > .sb-nav [id]",function(){let t=e(this).attr("id").substr(4);SBF.getURL("setting")!=t&&f("?setting="+t)}),e(w).on("click",'[data-type="upload-image"] .image, [data-type="upload-file"] .sb-btn, #sb-chatbot-add-files, #sb-import-settings a',function(){let a="";le=this,"sb-chatbot-add-files"==e(this).attr("id")?(a=".pdf,.txt",ie.find(".sb-pending").remove(),Ye.openAI.train.skip_files=[],ce=function(){let e=re.prop("files"),t="";for(var i=0;i<e.length;i++){let a=parseInt(e[i].size/1e3);t+=`<tr class="sb-pending" data-name="${e[i].name}"><td><input type="checkbox" /></td><td>${e[i].name}<label>${o("Pending")}</label></td><td>${a||1} KB</td><td><i class="sb-icon-delete"></i></td></tr>`}ie.append(t)}):"sb-import-settings"==e(this).parent().parent().attr("id")?(a=".json",de=(a=>{s(this)||(SBF.ajax({function:"import-settings",file_url:a},a=>{a?t("Settings saved. Reload to apply the changes."):i(a),e(this).sbLoading(!1)}),de=!1)})):e(this).hasClass("image")&&(a=".png,.jpg,.jpeg,.gif,.webp"),re.attr("accept",a).prop("value","").click()}),e(H).on("click",'[data-type="upload-image"] .image > i',function(t){return SBF.ajax({function:"delete-file",path:e(this).parent().attr("data-value")}),e(this).parent().removeAttr("data-value").css("background-image",""),t.preventDefault(),!1}),e(w).on("click",".sb-repeater-add",function(){Ze.repeaterAdd(this)}),e(w).on("click",".repeater-item > i",function(){setTimeout(()=>{Ze.repeaterDelete(this)},100)}),Ze.initColorPicker(),e(H).find('[data-type="color"]').focusout(function(){let t=e(this).closest(".input-color"),i=t.find("input").val();setTimeout(function(){t.find("input").val(""),t.find(".color-preview").css("background-color",i)},300),Ze.set(e(this).attr("id"),i)}),e(H).on("click",".sb-type-color .input i",function(t){e(this).parent().find("input").removeAttr("style").val("")}),e(H).on("click",".sb-color-palette span",function(){let t=e(this).sbActive();e(this).closest(".sb-repeater").find(".sb-active").sbActive(!1),e(this).sbActive(!t)}),e(H).on("click",".sb-color-palette ul li",function(){e(this).parent().parent().attr("data-value",e(this).data("value")).find("span").sbActive(!1)}),e(H).on("click",'[data-type="select-images"] .input > div',function(){e(this).siblings().sbActive(!1),e(this).sbActive(!0)}),e(H).on("click",".sb-select-checkbox-input",function(){e(this).toggleClass("sb-active")}),e(H).on("click",".sb-select-checkbox input",function(){let t=e(this).closest("[data-type]");t.find(".sb-select-checkbox-input").val(Ze.get(t)[1].join(", "))}),e(H).on("click",".sb-save-changes",function(){Ze.save(this)}),e(H).on("click","#open-ai-user-expressions-btn a",function(t){return e(this).sbLoading()||i("Make a backup of your Dialogflow agent first. This operation can take several minutes.","alert",()=>{e(this).sbLoading(!0),SBF.ajax({function:"open-ai-user-expressions-intents"},t=>{i("User expressions successfully generated."+(!0===t?"":" Errors: "+t)),e(this).sbLoading(!1)})}),t.preventDefault(),!1}),e(te).on("click","#sb-train-chatbot",function(t){if(i("<br><br><br><br><br>","info",!1,"sb-embeddings-box"),t.preventDefault(),SB_ADMIN_SETTINGS.cloud&&SBCloud.creditsAlert(this,t))return!1;if(s(this))return!1;Ye.openAI.train.errors=[];return Ye.openAI.train.files(t=>{Ye.openAI.train.urls=te.find('[data-id="open-ai-sources-url"]').map(function(){return e(this).val().trim()}).get(),Ye.openAI.train.extract_url=te.find('[data-id="open-ai-sources-extract-url"]').map(function(){return e(this).is(":checked")}).get(),Ye.openAI.train.website(t=>{Ye.openAI.train.qea(t=>{Ye.openAI.train.articles(t=>{Ye.openAI.init(),te.find("#sb-repeater-chatbot-website .repeater-item i").click(),Ye.openAI.train.errors.length?(i(o("The chatbot has been trained with errors. Check the console for more details.")+"\n\n"+Ye.openAI.train.errors.join("\n"),"info",!1,!1,!1,!0),console.error(Ye.openAI.train.errors)):i("The chatbot has been successfully trained.","info",!1,!1,"Success"),e(this).sbLoading(!1)})})})}),!1}),e(te).on("click","#sb-table-chatbot-files td i, #sb-table-chatbot-website td i, #sb-chatbot-delete-files, #sb-chatbot-delete-website, #sb-chatbot-delete-all-training",function(){let a=e(this).is("i"),n=!!a&&e(this).closest("tr");return a&&n.hasClass("sb-pending")?(Ye.openAI.train.skip_files.push(n.attr("data-name")),void n.remove()):(i("The training data will be permanently deleted.","alert",()=>{let o=[],r=e(this).attr("id");if(a)o=[n.attr("data-url")];else if("sb-chatbot-delete-all-training"==r)o="all";else{let t=e("sb-chatbot-delete-files"==r?ie:ae);t.find("input:checked").length||t.find("input").prop("checked",!0),t.find("tr").each(function(){if(e(this).find("input:checked").length)if(e(this).hasClass("sb-pending"))Ye.openAI.train.skip_files.push(e(this).attr("data-name")),e(this).remove();else{let t=e(this).attr("data-url");o.push(t),Ye.openAI.train.sitemap_processed_urls.indexOf(t)>-1&&(Ye.openAI.train.sitemap_processed_urls[Ye.openAI.train.sitemap_processed_urls.indexOf(t)]=!1)}})}if(o.length){if(s(this))return;SBF.ajax({function:"open-ai-embeddings-delete",sources_to_delete:o},a=>{Ye.openAI.init(),"all"==o&&te.find('.sb-nav [data-value="info"]').click(),!0===a?t("Training data deleted."):i(a),e(this).sbLoading(!1)})}}),!1)}),e(te).on("click",'.sb-nav [data-value="info"]',function(){let e=te.find("#sb-chatbot-info");s(e)||SBF.ajax({function:"open-ai-get-information"},t=>{let i=[["files","Files"],["website","Website URLs"],["qea","Q&A"],["articles","Articles"],["conversations","Conversations"]],a=`<h2>${o("Sources")}</h2><p>`;for(var s=0;s<i.length;s++)a+=`${t[i[s][0]][1]} ${o(i[s][1])} (${t[i[s][0]][0]} ${o("chars")})<br>`;a+=`</p><h2>${o("Total detected characters")}</h2><p>${t.total} ${o("chars")+(t.limit?" / "+t.limit+" "+o("limit"):"")}</p><hr><div id="sb-chatbot-delete-all-training" class="sb-btn sb-btn-white">${o("Delete all training data")}</div>`,e.html(a),e.sbLoading(!1)})}),e(te).on("click",".sb-menu-chatbot [data-type]",function(){let t=e(this).data("type");switch(t){case"training":case"playground":te.find("> [data-id]").sbActive(!1),te.find(`> [data-id="${t}"]`).sbActive(!0);break;case"settings":Ze.open("dialogflow")}}),e(ne).on("click",'[data-value="add"], [data-value="send"]',function(){let t=ne.find("textarea"),a=t.val().trim();t.val(""),a&&Ye.openAI.playground.addMessage(a,ne.find('[data-value="user"], [data-value="assistant"]').attr("data-value")),"send"==e(this).data("value")&&Ye.openAI.playground.messages.length&&!s(this)&&SBF.ajax({function:"open-ai-playground-message",messages:Ye.openAI.playground.messages},t=>{if(t[0]){if(t[1]&&(Ye.openAI.playground.addMessage(t[1],"assistant"),t[4])){let e="";for(var a in t[4].usage)e+=`<b>${SBF.slugToString(a)}</b>: ${t[4].usage[a]}<br>`;Ye.openAI.playground.last_response=t[4],te.find(".sb-playground-info").html(e+`<div id="sb-playground-query" class="sb-btn-text">${o("View code")}</div>${t[4].embeddings?`<div id="sb-playground-embeddings" class="sb-btn-text">${o("Embeddings")}</div>`:""}`)}}else i(t),console.error(t);e(this).sbLoading(!1)})}),e(te).on("click","#sb-playground-query",function(){i("<pre>"+JSON.stringify(Ye.openAI.playground.last_response.query,null,4).replaceAll('\\"','"')+"</pre>","info",!1,"sb-playground-query-panel",!1,!0)}),e(te).on("click","#sb-playground-embeddings",function(){let e="",t=Ye.openAI.playground.last_response.embeddings;for(var a=t.length-1;a>-1;a--)e+=`<span><b>${o("Source")}</b>: ${t[a].source?t[a].source.autoLink({target:"_blank"}):""}<br><b>${o("Score")}</b>: ${t[a].score}<br><span>${t[a].text}</span></span>`;i(e,"info",!1,"sb-playground-embeddings-panel",!1,!0)}),e(ne).on("click",'[data-value="clear"]',function(){Ye.openAI.playground.messages=[],oe.html("")}),e(oe).on("click",".sb-icon-close",function(){let t=e(this).closest("[data-type]");Ye.openAI.playground.messages.splice(t.index(),1),t.remove()}),e(ne).on("click",'[data-value="user"], [data-value="assistant"]',function(){let t="user"==e(this).attr("data-value");e(this).attr("data-value",t?"assistant":"user").html('<i class="sb-icon-reload"></i> '+o(t?"Assistant":"User"))}),e(w).on("click","#open-ai-troubleshoot a, #google-troubleshoot a",function(a){if(a.preventDefault(),!s(this))return SBF.ajax({function:e(this).parent().attr("id")},a=>{!0===a?t("Success. No issues found."):i(a),e(this).sbLoading(!1),e(A).find(".sb-admin-list .sb-select li.sb-active").click()}),!1}),e(H).on("change",'#user-additional-fields [data-id="extra-field-slug"], #saved-replies [data-id="reply-name"], [data-id="rich-message-name"]',function(){e(this).val(SBF.stringToSlug(e(this).val()))}),e(H).on("click","#timetable-utc input",function(){e(this).val()||e(this).val(Math.round(Oe.getTimezoneOffset()/60))}),e(H).on("click","#dialogflow-sync-btn a",function(e){let t="https://accounts.google.com/o/oauth2/auth?scope=https%3A%2F%2Fwww.googleapis.com/auth/dialogflow%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-translation%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-language&response_type=code&access_type=offline&redirect_uri="+SB_URL+"/apps/dialogflow/functions.php&client_id={client_id}&prompt=consent";if(!SB_ADMIN_SETTINGS.cloud||"auto"!=H.find("#google-sync-mode select").val()){let a=H.find("#google-client-id input").val();return a&&H.find("#google-client-secret input").val()?window.open(t.replace("{client_id}",a)):i("Before continuing enter Client ID and Client secret. Check the docs for more details."),e.preventDefault(),!1}if(SB_ADMIN_SETTINGS.credits)return window.open(t.replace("{client_id}",SB_ADMIN_SETTINGS.google_client_id)),e.preventDefault(),!1}),e(H).on("click","#dialogflow-redirect-url-btn a",function(e){return i(`<pre>${SB_URL}/apps/dialogflow/functions.php</pre>`),e.preventDefault(),!1}),e(H).on("click","#dialogflow-saved-replies a",function(t){return i("","alert",()=>{s(this)||SBF.ajax({function:"dialogflow-saved-replies"},()=>{e(this).sbLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#test-email-user a, #test-email-agent a",function(){let t=e(this).parent().find("input").val();t&&t.indexOf("@")>0&&!s(this)&&SBF.ajax({function:"send-test-email",to:t,email_type:"test-email-user"==e(this).parent().parent().attr("id")?"user":"agent"},()=>{i("The message has been sent.","info"),e(this).sbLoading(!1)})}),e(H).on("click","#email-server-troubleshoot a",function(e){return H.find("#test-email-user input").val(SB_ACTIVE_AGENT.email).next().click(),e.preventDefault(),!1}),e(H).on("click","#test-sms-user a, #test-sms-agent a",function(){let t=e(this).parent().find("input").val();t&&!s(this)&&SBF.ajax({function:"send-sms",message:"Hello World!",to:t},t=>{i(t&&["sent","queued"].includes(t.status)?"The message has been sent.":JSON.stringify(t)),e(this).sbLoading(!1)})}),e(H).on("click",".sb-timetable > div > div > div",function(){let t=e(this).closest(".sb-timetable"),i=e(this).sbActive();if(e(t).find(".sb-active").sbActive(!1),i)e(this).sbActive(!1).find(".sb-custom-select").remove();else{let i=e(t).find("> .sb-custom-select").html();e(t).find(" > div .sb-custom-select").remove(),e(this).append(`<div class="sb-custom-select">${i}</div>`).sbActive(!0)}}),e(H).on("click",".sb-timetable .sb-custom-select span",function(){let t=[e(this).html(),e(this).attr("data-value")];e(this).closest(".sb-timetable").find("> div > div > .sb-active").html(t[0]).attr("data-value",t[1]),e(this).parent().sbActive(!1)}),e(H).on("click","#system-requirements a",function(e){let t="";return SBF.ajax({function:"system-requirements"},e=>{for(var i in e)t+=`<div class="sb-input"><span>${o(SBF.slugToString(i))}</span><div${e[i]?' class="sb-green"':""}>${o(e[i]?"Success":"Error")}</div></div>`;n(!1),at.genericPanel("requirements","System requirements",t)}),n(),e.preventDefault(),!1}),e(H).on("click","#sb-path a",function(e){return SBF.ajax({function:"path"},e=>{i(`<pre>${e}</pre>`)}),e.preventDefault(),!1}),e(H).on("click","#sb-url a",function(e){return i(`<pre>${SB_URL}</pre>`),e.preventDefault(),!1}),e(H).on("click","#delete-leads a",function(t){return e(this).sbLoading()||i("All leads, including all the linked conversations and messages, will be deleted permanently.","alert",()=>{e(this).sbLoading(!0),SBF.ajax({function:"delete-leads"},()=>{i("Leads and conversations successfully deleted."),e(this).sbLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#sb-export-settings a",function(t){if(t.preventDefault(),!s(this))return SBF.ajax({function:"export-settings"},t=>{S(t,"sb-export-settings-close","Settings exported"),e(this).sbLoading(!1)}),!1}),e(w).on("click","#sb-export-settings-close .sb-close, #sb-export-users-close .sb-close, #sb-export-report-close .sb-close",function(){SBF.ajax({function:"delete-file",path:w.find(".sb-dialog-box p pre").html()})}),SB_ADMIN_SETTINGS.cloud||(e(H).on("change","#push-notifications-provider select",function(){let t="pusher"==e(this).val();Ze.visibility(0,t),SBF.ajax({function:"update-sw",url:t?"https://js.pusher.com/beams/service-worker.js":"vendor/OneSignalSDK.sw.js"})}),e(H).on("click","#push-notifications-sw-path a",function(e){let t=b(location.href).replace(location.host,"").replace("admin.php","");return t.includes("?")&&(t=t.substring(0,t.indexOf("?"))),i("<pre>"+t+"</pre>"),e.preventDefault(),!1})),e(H).on("click","#push-notifications-btn a",function(e){return SB_ADMIN_SETTINGS.cloud||"onesignal"==H.find("#push-notifications-provider select").val()?typeof OneSignal!=We?OneSignal.Slidedown.promptPush({force:!0}):SBF.serviceWorker.initPushNotifications():Notification.requestPermission(),e.preventDefault(),!1}),e(H).on("input","#sb-search-settings",function(){let t=e(this).val().toLowerCase();SBF.search(t,()=>{let i="",a=H.find(".sb-search-dropdown-items");if(t.length>2){let a=H.find("> .sb-tab > .sb-nav li").map(function(){return e(this).text().trim()}).get();H.find(".sb-setting").each(function(){let s=e(this).attr("data-keywords");if(s&&s.includes(t)||e(this).attr("id").replaceAll("-","-").includes(t)||e(this).find(".sb-setting-content").text().toLowerCase().includes(t)){let t=e(this).parent().index();i+=`<div data-tab-index="${t}" data-setting="${e(this).attr("id")}">${a[t]} > ${e(this).find("h2").text()}</div>`}})}a.html(i),a.outerHeight()>e(window).height()-100?(a.css("max-height",e(window).height()-100),a.addClass("sb-scroll-area")):a.removeClass("sb-scroll-area")})}),e(H).on("click",".sb-search-dropdown-items div",function(){let t=e(this).attr("data-tab-index"),i=H.find("> .sb-tab > .sb-nav li").eq(t);i.click(),i.get(0).scrollIntoView(),H.find("#"+e(this).attr("data-setting"))[0].scrollIntoView()}),e(H).on("click","#slack-button a",()=>(window.open("https://board.support/synch/?service=slack&plugin_url="+SB_URL+g()),!1)),e(H).on("click","#slack-test a",function(t){if(!s(this))return SBF.ajax({function:"send-slack-message",user_id:!1,full_name:SB_ACTIVE_AGENT.full_name,profile_image:SB_ACTIVE_AGENT.profile_image,message:"Lorem ipsum dolor sit amete consectetur adipiscing elite incidido labore et dolore magna aliqua.",attachments:[["Example link",SB_URL+"/media/user.svg"],["Example link two",SB_URL+"/media/user.svg"]],channel:H.find("#slack-channel input").val()},t=>{i(SBF.errorValidation(t)?"slack-not-active"==t[1]?"Please first activate Slack, then save the settings and reload the admin area.":"Error. Response: "+JSON.stringify(t):"success"==t[0]?"Slack message successfully sent. Check your Slack app!":JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault(),!1}),e(H).on("click","#tab-slack",function(){let e=H.find("#slack-agents .input");e.html('<div class="sb-loading"></div>'),SBF.ajax({function:"slack-users"},t=>{let i="";if(SBF.errorValidation(t,"slack-token-not-found"))i=`<p>${o("Synchronize Slack and save changes before linking agents.")}</p>`;else{let e='<option value="-1"></option>';for(a=0;a<t.agents.length;a++)e+=`<option value="${t.agents[a].id}">${t.agents[a].name}</option>`;for(var a=0;a<t.slack_users.length;a++)i+=`<div data-id="${t.slack_users[a].id}"><label>${t.slack_users[a].name}</label><select>${e}</select></div>`}e.html(i),Ze.set("slack-agents",[t.saved,"double-select"])})}),e(H).on("click","#slack-archive-channels a",function(t){t.preventDefault(),s(this)||SBF.ajax({function:"archive-slack-channels"},t=>{!0===t&&i("Slack channels archived successfully!"),e(this).sbLoading(!1)})}),e(H).on("click","#slack-channel-ids a",function(t){t.preventDefault(),s(this)||SBF.ajax({function:"slack-channels",code:!0},t=>{i(t,"info",!1,"","",!0),e(this).sbLoading(!1)})}),e(H).on("click",'#whatsapp-twilio-btn a, #whatsapp-twilio-get-configuartion-btn a, #sms-btn a, #wechat-btn a, #twitter-callback a, #gbm-webhook a, #viber-webhook a, [data-id="line-webhook"], #messenger-path-btn a',function(t){let a=e(this).closest("[id]").attr("id"),s="";if(t.preventDefault(),"line"==a){if(!(s=e(this).closest(".repeater-item").find('[data-id="line-secret"]').val()))return;s="?line_secret="+s}return i(`<pre>${SB_URL+("sms-btn"==a?"/include/api.php":"/apps/"+(a.includes("-")?a.substring(0,a.indexOf("-")):a)+"/post.php")+s+g().replace("&",s?"&":"?")}</pre>`),!1}),e(H).on("click",'[data-id="telegram-numbers-button"], #viber-button a, #whatsapp-360-button a',function(t){let a,n={"telegram-button":["#telegram-token input","telegram-synchronization",["result",!0]],"viber-button":["#viber-token input","viber-synchronization",["status_message","ok"]],"whatsapp-360-button":["#whatsapp-360-key input","whatsapp-360-synchronization",["success",!0]]},o=e(this).parent().attr("id"),r=!1;if(o)a=H.find(n[o][0]).val().trim();else{o={"telegram-numbers-button":"telegram-button"}[e(this).attr("data-id")],a=e(this).closest(".repeater-item").find(`[data-id="${{"telegram-button":"telegram-numbers-token"}[o]}"]`).val().trim(),r=!0}return n=n[o],t.preventDefault(),!(!a||s(this))&&(SBF.ajax({function:n[1],token:a,cloud_token:g(),is_additional_number:r},t=>{i(n[2][0]in t&&t[n[2][0]]==n[2][1]?"Synchronization completed.":JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),e(H).on("click","#whatsapp-test-template a",function(t){t.preventDefault();let a=e(this).parent().find("input").val();if(a&&!s(this))return SBF.ajax({function:"whatsapp-send-template",to:a},t=>{i(t?"error"in t?t.error.message:"Message sent, check your WhatsApp!":t),e(this).sbLoading(!1)}),!1}),e(H).on("click","#twitter-subscribe a",function(t){return t.preventDefault(),!s(this)&&(SBF.ajax({function:"twitter-subscribe",cloud_token:g()},t=>{i(!0===t?"Synchronization completed.":JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),SB_ADMIN_SETTINGS.cloud||e(H).on("click","#messenger-sync-btn a",()=>(window.open("https://board.support/synch/?service=messenger&plugin_url="+SB_URL+g()),!1)),e(H).on("change","#messenger-sync-mode select",function(){Ze.visibility(1,"manual"!=e(this).val())}),e(H).on("change","#open-ai-mode select",function(){Ze.visibility(2,"assistant"!=e(this).val())}),e(H).on("click","#wp-sync a",function(t){return t.preventDefault(),!s(this)&&(Ye.wordpress.ajax("wp-sync",{},t=>{!0===t||"1"===t?(et.update(),i("WordPress users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),!1)}),e("body").on("click","#wp-admin-bar-logout",function(){SBF.logout(!1)}),e(H).on("click","#whatsapp-clear-flows a",function(e){e.preventDefault(),SBF.ajax({function:"whatsapp-clear-flows"})}),e(H).on("click","#tab-translations",function(){let e=H.find(".sb-translations > .sb-nav > ul");if(!e.html()){let i="";for(var t in SB_LANGUAGE_CODES)"en"!=t&&(i+=`<li data-code="${t}"><img src="${SB_URL}/media/flags/${t}.png" />${SB_LANGUAGE_CODES[t]}</li>`);e.html(i)}}),e(H).on("click",".sb-translations .sb-nav li",function(){Ze.translations.load(e(this).data("code"))}),e(H).on("click",".sb-translations .sb-menu-wide li",function(){H.find(`.sb-translations [data-area="${e(this).data("value")}"]`).sbActive(!0).siblings().sbActive(!1)}),e(H).on("click",".sb-add-translation",function(){H.find(".sb-translations-list > .sb-active").prepend(`<div class="sb-input-setting sb-type-text sb-new-translation"><input type="text" placeholder="${o("Enter original text...")}"><input type="text" placeholder="${o("Enter translation...")}"></div></div>`)}),e(H).on("input",".sb-search-translation input",function(){let t=e(this).val().toLowerCase();SBF.search(t,()=>{t.length>1&&H.find(".sb-translations .sb-content > .sb-active label").each(function(){let i=e(this).html().toLowerCase();if(i.includes(t)&&i!=ve){let t=H.find(".sb-scroll-area");return t[0].scrollTop=0,t[0].scrollTop=e(this).position().top-80,ve=i,!1}})})}),e(Y).on("click",".ul-articles li",function(t){i("The changes will be lost.","alert",()=>{Ke.show(e(this).attr("data-id")),Y.find(".sb-scroll-area").scrollTop(0)},!1,!1,!1,!Re,()=>{e(this).parent().find("li").sbActive(!1),e(this).parent().find(`[data-id="${Ke.activeID()}"]`).sbActive(!0)})}),e(Y).on("click",".ul-categories li",function(t){Ke.categories.show(e(this).attr("data-id"))}),e(Y).on("click",".sb-add-article",function(){Ke.add()}),e(Y).on("click",".sb-add-category",function(){Ke.categories.add()}),e(Y).on("click",".sb-nav i",function(t){let a=e(this).parent(),n=a.closest("ul"),o=n.hasClass("ul-categories");return i(`The ${o?"category":"article"} will be deleted permanently.`,"alert",()=>{let e=a.attr("data-id");if(o)Ke.categories.delete(e);else{if(!e)return a.remove();s(Z),Ke.delete(e,e=>{Z.sbLoading(!1),n.find("li").length>1&&(a.prev().length?a.prev().click():a.next().click(),a.remove()),p()})}}),t.preventDefault(),!1}),e(Y).on("click",".sb-menu-wide li",function(){let t=e(this).data("type");"settings"==t?Ze.open("articles"):"reports"==t?Qe.open("articles-searches"):(Y.attr("data-type",t),Ke.categories.update())}),e(Y).on("click",".sb-save-articles",function(){s(this)||("categories"==Y.attr("data-type")?Ke.categories.save(t=>{e(this).sbLoading(!1)}):Ke.save(t=>{e(this).sbLoading(!1)}))}),e(Y).on("change input","input, textarea, select",function(){Re=!0}),e(Q).on("change",function(){ee.val()||(t("Select a parent category first.","error"),e(this).val(""))}),e(H).on("click","#email-piping-sync a",function(t){s(this)||(SBF.ajax({function:"email-piping",force:!0},t=>{i(!0===t?"Syncronization completed.":t),e(this).sbLoading(!1)}),t.preventDefault())}),e(H).on("click","#tab-automations",function(){Ze.automations.get(()=>{Ze.automations.populate(),n(!1)},!0),n()}),e(W).on("click",".sb-add-condition",function(){Ze.automations.addCondition()}),e(W).on("change",".sb-condition-1 select",function(){Ze.automations.updateCondition(this)}),e(z).on("click","li",function(){Ze.automations.populate(e(this).data("value"))}),e(J).on("click","li",function(){Ze.automations.show(e(this).attr("data-id"))}),e(W).on("click",".sb-add-automation",function(){Ze.automations.add()}),e(J).on("click","li i",function(){i("The automation will be deleted permanently.","alert",()=>{Ze.automations.delete(this)})}),e(xe).on("click",".sb-nav [id]",function(){let t=e(this).attr("id");Qe.active_report=!1,xe.find("#sb-date-picker").val(""),xe.attr("class","sb-area-reports sb-active sb-report-"+t),Qe.initReport(e(this).attr("id")),SBF.getURL("report")!=t&&f("?report="+t)}),e(xe).on("change","#sb-date-picker",function(){Qe.initReport(!1,e(this).val())}),e(xe).on("click",".sb-report-export",function(){e(this).sbLoading()||Qe.export(t=>{e(this).sbLoading(!1),t&&S(t,"sb-export-report-close","Report exported")})}),SBF.getURL("report")&&(xe.sbActive()||x.find(".sb-admin-nav #sb-reports").click(),setTimeout(()=>{xe.find("#"+SBF.getURL("report")).click()},500)),e(A).on("click",".sb-panel-woocommerce > i",function(){Ye.woocommerce.conversationPanel()}),e(A).on("click",".sb-woocommerce-orders > div > span",function(t){let i=e(this).parent();e(t.target).is("span")&&(i.sbActive()||Ye.woocommerce.conversationPanelOrder(i.attr("data-id")))}),e(A).on("click",".sb-btn-woocommerce",function(){(Ie.sbLoading()||0!=a()&&a().language!=Ye.woocommerce.popupLanguage)&&Ye.woocommerce.popupPopulate(),$e.find(".sb-search-btn input").val(""),$e.sbTogglePopup(this)}),e($e).find(".sb-woocommerce-products-list").on("scroll",function(){d(this,!0)&&Ye.woocommerce.popupPagination(this)}),e($e).on("click",".sb-select li",function(){Ye.woocommerce.popupFilter(this)}),e($e).on("input",".sb-search-btn input",function(){Ye.woocommerce.popupSearch(this)}),e($e).on("click",".sb-search-btn i",function(){SBF.searchClear(this,()=>{Ye.woocommerce.popupSearch(e(this).next())})}),e($e).on("click",".sb-woocommerce-products-list li",function(){let t=$e.attr("data-action"),i=e(this).data("id");SBF.null(t)?SBChat.insertText(`{product_card id="${i}"}`):(Ie.sbLoading(!0),A.find(".sb-add-cart-btn").sbLoading(!0),SBChat.sendMessage(-1,"",[],e=>{e&&(Ye.woocommerce.conversationPanelUpdate(i),w.sbHideLightbox())},{event:"woocommerce-update-cart",action:"cart-add",id:i})),SBF.deactivateAll(),w.removeClass("sb-popup-active")}),e(A).on("click",".sb-panel-woocommerce .sb-add-cart-btn",function(){e(this).sbLoading()||(SBChat.user_online?(Ye.woocommerce.popupPopulate(),$e.sbShowLightbox(!0,"cart-add")):i("The user is offline. Only the carts of online users can be updated."))}),e(A).on("click",".sb-panel-woocommerce .sb-list-items > a > i",function(t){let i=e(this).parent().attr("data-id");return SBChat.sendMessage(-1,"",[],()=>{Ye.woocommerce.conversationPanelUpdate(i,"removed")},{event:"woocommerce-update-cart",action:"cart-remove",id:i}),e(this).sbLoading(!0),t.preventDefault(),!1}),e(H).on("click","#wc-dialogflow-synch a, #wc-dialogflow-create-intents a",function(t){if(Ye.is("dialogflow")){if(s(this))return;let t=e(this).parent().attr("id");SBF.ajax({function:"woocommerce-dialogflow-"+("wc-dialogflow-synch"==t?"entities":"intents")},t=>{e(this).sbLoading(!1),i(t?"Synchronization completed.":"Error. Something went wrong.")})}else i("This feature requires the Dialogflow App. Get it from the apps area.");return t.preventDefault(),!1}),e(A).on("click",".sb-panel-ump > i",function(){Ye.ump.conversationPanel()}),e(A).on("click",".sb-panel-armember > i",function(){Ye.armember.conversationPanel()}),e(A).on("click",".sb-panel-opencart > i",function(){Ye.opencart.conversationPanel()}),e(A).on("click",".sb-opencart-orders > a",function(){Ye.opencart.openOrder(e(this).attr("data-id"))}),e(H).on("click","#opencart-sync a",function(t){t.preventDefault(),s(this)||SBF.ajax({function:"opencart-sync"},t=>{e(this).sbLoading(!1),i(!0===t?"Users successfully imported.":t)})}),e(H).on("click","#perfex-sync a, #whmcs-sync a, #perfex-articles-sync a, #whmcs-articles-sync a, #aecommerce-sync a, #aecommerce-sync-admins a, #aecommerce-sync-sellers a, #martfury-sync a, #martfury-sync-sellers a",function(t){if(s(this))return;let a=e(this).closest("[id]").attr("id"),n=a.indexOf("article")>0;SBF.ajax({function:a},t=>{!0===t?(n||et.update(),i(n?"Articles successfully imported.":"Users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault()}),e(A).on("click","#sb-zendesk-btn",function(t){s(this)||(SBF.ajax({function:"zendesk-create-ticket",conversation_id:SBChat.conversation.id},t=>{!0===t?Ye.zendesk.conversationPanel():i("Error. Response: "+JSON.stringify(t)),e(this).sbLoading(!1)}),t.preventDefault())}),e(A).on("click","#sb-zendesk-update-ticket",function(t){if(!s(this))return SBF.ajax({function:"zendesk-update-ticket",conversation_id:SBChat.conversation.id,zendesk_ticket_id:e(this).closest("[data-id]").attr("data-id")},()=>{e(this).sbLoading(!1)}),t.preventDefault(),!1}),e(w).on("click",".sb-language-switcher > i",function(){let t=e(this).parent(),i=t.find("[data-language]").map(function(){return e(this).attr("data-language")}).get(),a="";i.push("en");for(var s in SB_LANGUAGE_CODES)i.includes(s)||(a+=`<div data-language="${s}"><img src="${SB_URL}/media/flags/${s}.png" />${o(SB_LANGUAGE_CODES[s])}</div>`);ue=t,at.genericPanel("languages","Choose a language",a,[],' data-source="'+t.attr("data-source")+'"',!0)}),e(w).on("click",".sb-language-switcher img",function(){let t=e(this).parent(),a=t.sbActive(),s=!a&&t.attr("data-language");switch(t.parent().attr("data-source")){case"article-categories":Ke.categories.show(Ke.categories.activeID(),s);break;case"articles":let e=Z.find(".sb-language-switcher .sb-active");i("The changes will be lost.","alert",()=>{let e=t.attr("data-id");e||a?Ke.show(e&&!a?e:Ke.activeID(!0)):Ke.clear()},!1,!1,!1,!Re,()=>{t.sbActive(!1),e.sbActive(!0)});break;case"automations":Ze.automations.show(!1,s);break;case"settings":let n=t.parent().find("[data-language].sb-active");Ze.translations.save(t,a?t.attr("data-language"):!!n.length&&n.attr("data-language")),Ze.translations.activate(t,s)}t.siblings().sbActive(!1),t.sbActive(!a)}),e(w).on("click",".sb-language-switcher span > i",function(){let t=e(this).parent(),a=t.attr("data-language");i(o("The {T} translation will be deleted.").replace("{T}",o(SB_LANGUAGE_CODES[a])),"alert",()=>{switch(t.parent().attr("data-source")){case"article-categories":Ke.categories.translations.delete(a);break;case"articles":Ke.translations.delete(a);break;case"automations":Ze.automations.deleteTranslation(!1,a),Ze.automations.show();break;case"settings":Ze.translations.delete(t,a)}t.remove()})}),e(w).on("click",".sb-languages-box [data-language]",function(){let t=e(this).parents().eq(1),a=e(this).attr("data-language"),s=!0;switch(t.attr("data-source")){case"article-categories":Ke.categories.translations.add(a);break;case"articles":i("The changes will be lost.","alert",()=>{Ke.translations.add(a),w.sbHideLightbox()},!1,!1,!1,!Re),s=!1;break;case"automations":Ze.automations.addTranslation(!1,!1,a),Ze.automations.show(!1,a);break;case"settings":Ze.translations.add(a)}s&&w.sbHideLightbox()}),e(w).on("click",".sb-lightbox .sb-top-bar .sb-close",function(){w.sbHideLightbox()}),e(w).on("click",".sb-lightbox .sb-info",function(){e(this).sbActive(!1)}),e(w).on("click",".sb-dialog-box a",function(){let t=e(this).closest(".sb-lightbox");e(this).hasClass("sb-confirm")&&he(),e(this).hasClass("sb-cancel")&&fe&&fe(),1==w.find(".sb-lightbox.sb-active").length&&me.sbActive(!1),at.open_popup=!1,t.sbActive(!1)}),e(w).on("click",".sb-menu-wide li, .sb-nav li",function(){e(this).siblings().sbActive(!1),e(this).sbActive(!0)}),e(w).on("click",".sb-nav:not(.sb-nav-only) li:not(.sb-tab-nav-title)",function(){let t=e(this).closest(".sb-tab"),i=e(t).find(" > .sb-content > div").sbActive(!1).eq(e(this).parent().find("li:not(.sb-tab-nav-title)").index(this));i.sbActive(!0),i.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),t.find(".sb-scroll-area").scrollTop(0)}),e(w).sbInitTooltips(),e(w).on("click",'[data-button="toggle"]',function(){let t=w.find("."+e(this).data("show")),i=w.find("."+e(this).data("hide"));t.addClass("sb-show-animation").show(),i.hide(),at.open_popup=!(!t.hasClass("sb-lightbox")&&!t.hasClass("sb-popup"))&&t}),e(w).on("click",".sb-info-card",function(){e(this).sbActive(!1)}),e(re).on("change",function(){ce?(ce(),ce=!1):(e(le).sbLoading(e(this).prop("files").length),e(this).sbUploadFiles(t=>{if(e(le).sbLoading(!1),"success"==(t=JSON.parse(t))[0]){"upload-image"==e(le).closest("[data-type]").data("type")&&(e(le).attr("data-value")&&SBF.ajax({function:"delete-file",path:e(le).attr("data-value")}),e(le).attr("data-value",t[1]).css("background-image",`url("${t[1]}")`)),de&&de(t[1])}else console.log(t[1])}))}),e(w).on("click",".sb-accordion > div > span",function(t){let i=e(this).parent(),a=e(i).sbActive();e(t.target).is("span")&&(i.siblings().sbActive(!1),i.sbActive(!a))}),e(w).on("mousedown",function(t){if(e(at.open_popup).length){let i=e(at.open_popup);i.is(t.target)||0!==i.has(t.target).length||["sb-btn-saved-replies","sb-btn-emoji","sb-btn-woocommerce","sb-btn-open-ai"].includes(e(t.target).attr("class"))||(i.hasClass("sb-popup")?i.sbTogglePopup():i.hasClass("sb-select")?i.find("ul").sbActive(!1):i.hasClass("sb-menu-mobile")?i.find("i").sbActive(!1):i.hasClass("sb-menu")?i.sbActive(!1):at.open_popup&&["sb-embeddings-box"].includes(at.open_popup.attr("id"))||w.sbHideLightbox(),at.open_popup=!1)}})})}(jQuery),function(e,t){"object"==typeof exports?module.exports=t(e):"function"==typeof define&&define.amd?define("colors",[],function(){return t(e)}):e.Colors=t(e)}(this,function(e,t){function i(e,i,s,n,o){if("string"==typeof i){s=(i=m.txt2color(i)).type,p[s]=i[s],o=o!==t?o:i.alpha}else if(i)for(var c in i)e[s][c]=r(i[c]/l[s][c][1],0,1);return o!==t&&(e.alpha=r(+o,0,1)),a(s,n?e:t)}function a(e,t){var i,a,r,h=t||p,f=m,g=u.options,b=l,v=h.RND,S="",_="",B={hsl:"hsv",rgb:e},y=v.rgb;if("alpha"!==e){for(var w in b)if(!b[w][w]){e!==w&&(_=B[w]||"rgb",h[w]=f[_+"2"+w](h[_])),v[w]||(v[w]={}),i=h[w];for(S in i)v[w][S]=d(i[S]*b[w][S][1])}y=v.rgb,h.HEX=f.RGB2HEX(y),h.equivalentGrey=g.grey.r*h.rgb.r+g.grey.g*h.rgb.g+g.grey.b*h.rgb.b,h.webSave=a=s(y,51),h.webSmart=r=s(y,17),h.saveColor=y.r===a.r&&y.g===a.g&&y.b===a.b?"web save":y.r===r.r&&y.g===r.g&&y.b===r.b?"web smart":"",h.hueRGB=m.hue2RGB(h.hsv.h),t&&(h.background=function(e,t,i){var a=u.options.grey,s={};return s.RGB={r:e.r,g:e.g,b:e.b},s.rgb={r:t.r,g:t.g,b:t.b},s.alpha=i,s.equivalentGrey=d(a.r*e.r+a.g*e.g+a.b*e.b),s.rgbaMixBlack=o(t,{r:0,g:0,b:0},i,1),s.rgbaMixWhite=o(t,{r:1,g:1,b:1},i,1),s.rgbaMixBlack.luminance=n(s.rgbaMixBlack,!0),s.rgbaMixWhite.luminance=n(s.rgbaMixWhite,!0),u.options.customBG&&(s.rgbaMixCustom=o(t,u.options.customBG,i,1),s.rgbaMixCustom.luminance=n(s.rgbaMixCustom,!0),u.options.customBG.luminance=n(u.options.customBG,!0)),s}(y,h.rgb,h.alpha))}var x,k,A,T=h.rgb,C=h.alpha,$="luminance",I=h.background;return x=o(T,{r:0,g:0,b:0},C,1),x[$]=n(x,!0),h.rgbaMixBlack=x,k=o(T,{r:1,g:1,b:1},C,1),k[$]=n(k,!0),h.rgbaMixWhite=k,g.customBG&&(A=o(T,I.rgbaMixCustom,C,1),A[$]=n(A,!0),A.WCAG2Ratio=function(e,t){var i=1;return i=e>=t?(e+.05)/(t+.05):(t+.05)/(e+.05),d(100*i)/100}(A[$],I.rgbaMixCustom[$]),h.rgbaMixBGMixCustom=A,A.luminanceDelta=c.abs(A[$]-I.rgbaMixCustom[$]),A.hueDelta=function(e,t,i){return(c.max(e.r-t.r,t.r-e.r)+c.max(e.g-t.g,t.g-e.g)+c.max(e.b-t.b,t.b-e.b))*(i?255:1)/765}(I.rgbaMixCustom,A,!0)),h.RGBLuminance=n(y),h.HUELuminance=n(h.hueRGB),g.convertCallback&&g.convertCallback(h,e),h}function s(e,t){var i={},a=0,s=t/2;for(var n in e)a=e[n]%t,i[n]=e[n]+(a>s?t-a:-a);return i}function n(e,t){for(var i=t?1:255,a=[e.r/i,e.g/i,e.b/i],s=u.options.luminance,n=a.length;n--;)a[n]=a[n]<=.03928?a[n]/12.92:c.pow((a[n]+.055)/1.055,2.4);return s.r*a[0]+s.g*a[1]+s.b*a[2]}function o(e,i,a,s){var n={},o=a!==t?a:1,r=s!==t?s:1,l=o+r*(1-o);for(var c in e)n[c]=(e[c]*o+i[c]*r*(1-o))/l;return n.a=l,n}function r(e,t,i){return e>i?i:t>e?t:e}var l={rgb:{r:[0,255],g:[0,255],b:[0,255]},hsv:{h:[0,360],s:[0,100],v:[0,100]},hsl:{h:[0,360],s:[0,100],l:[0,100]},alpha:{alpha:[0,1]},HEX:{HEX:[0,16777215]}},c=e.Math,d=c.round,u={},p={},h={r:.298954,g:.586434,b:.114612},f={r:.2126,g:.7152,b:.0722},g=function(e){this.colors={RND:{}},this.options={color:"rgba(0,0,0,0)",grey:h,luminance:f,valueRanges:l},b(this,e||{})},b=function(e,a){var s,n=e.options;v(e);for(var o in a)a[o]!==t&&(n[o]=a[o]);s=n.customBG,n.customBG="string"==typeof s?m.txt2color(s).rgb:s,p=i(e.colors,n.color,t,!0)},v=function(e){u!==e&&(u=e,p=e.colors)};g.prototype.setColor=function(e,s,n){return v(this),e?i(this.colors,e,s,t,n):(n!==t&&(this.colors.alpha=r(n,0,1)),a(s))},g.prototype.setCustomBackground=function(e){return v(this),this.options.customBG="string"==typeof e?m.txt2color(e).rgb:e,i(this.colors,t,"rgb")},g.prototype.saveAsBackground=function(){return v(this),i(this.colors,t,"rgb",!0)},g.prototype.toString=function(e,t){return m.color2text((e||"rgb").toLowerCase(),this.colors,t)};var m={txt2color:function(e){var t={},i=e.replace(/(?:#|\)|%)/g,"").split("("),a=(i[1]||"").split(/,\s*/),s=i[1]?i[0].substr(0,3):"rgb",n="";if(t.type=s,t[s]={},i[1])for(var o=3;o--;)n=s[o]||s.charAt(o),t[s][n]=+a[o]/l[s][n][1];else t.rgb=m.HEX2rgb(i[0]);return t.alpha=a[3]?+a[3]:1,t},color2text:function(e,t,i){var a=!1!==i&&d(100*t.alpha)/100,s="number"==typeof a&&!1!==i&&(i||1!==a),n=t.RND.rgb,o=t.RND.hsl,r="hex"===e&&s,l="hex"===e&&!r,c="rgb"===e||r?n.r+", "+n.g+", "+n.b:l?"#"+t.HEX:o.h+", "+o.s+"%, "+o.l+"%";return l?c:(r?"rgb":e)+(s?"a":"")+"("+c+(s?", "+a:"")+")"},RGB2HEX:function(e){return((e.r<16?"0":"")+e.r.toString(16)+(e.g<16?"0":"")+e.g.toString(16)+(e.b<16?"0":"")+e.b.toString(16)).toUpperCase()},HEX2rgb:function(e){return e=e.split(""),{r:+("0x"+e[0]+e[e[3]?1:0])/255,g:+("0x"+e[e[3]?2:1]+(e[3]||e[1]))/255,b:+("0x"+(e[4]||e[2])+(e[5]||e[2]))/255}},hue2RGB:function(e){var t=6*e,i=~~t%6,a=6===t?0:t-i;return{r:d(255*[1,1-a,0,0,a,1][i]),g:d(255*[a,1,1,1-a,0,0][i]),b:d(255*[0,0,a,1,1,1-a][i])}},rgb2hsv:function(e){var t,i,a,s=e.r,n=e.g,o=e.b,r=0;return o>n&&(n=o+(o=n,0),r=-1),i=o,n>s&&(s=n+(n=s,0),r=-2/6-r,i=c.min(n,o)),t=s-i,a=s?t/s:0,{h:1e-15>a?p&&p.hsl&&p.hsl.h||0:t?c.abs(r+(n-o)/(6*t)):0,s:s?t/s:p&&p.hsv&&p.hsv.s||0,v:s}},hsv2rgb:function(e){var t=6*e.h,i=e.s,a=e.v,s=~~t,n=t-s,o=a*(1-i),r=a*(1-n*i),l=a*(1-(1-n)*i),c=s%6;return{r:[a,r,o,o,l,a][c],g:[l,a,a,r,o,o][c],b:[o,o,l,a,a,r][c]}},hsv2hsl:function(e){var t=(2-e.s)*e.v,i=e.s*e.v;return i=e.s?1>t?t?i/t:0:i/(2-t):0,{h:e.h,s:e.v||i?i:p&&p.hsl&&p.hsl.s||0,l:t/2}},rgb2hsl:function(e,t){var i=m.rgb2hsv(e);return m.hsv2hsl(t?i:p.hsv=i)},hsl2rgb:function(e){var t=6*e.h,i=e.s,a=e.l,s=.5>a?a*(1+i):a+i-i*a,n=a+a-s,o=~~t,r=s*(s?(s-n)/s:0)*(t-o),l=n+r,c=s-r,d=o%6;return{r:[s,c,n,n,l,s][d],g:[l,s,s,c,n,n][d],b:[n,n,l,s,s,c][d]}}};return g}),function(e,t){"object"==typeof exports?module.exports=t(e,require("jquery"),require("colors")):"function"==typeof define&&define.amd?define(["jquery","colors"],function(i,a){return t(e,i,a)}):t(e,e.jQuery,e.Colors)}(this,function(e,t,i,a){function s(e){return e.value||e.getAttribute("value")||t(e).css("background-color")||"#FFF"}function n(e){return(e=e.originalEvent&&e.originalEvent.touches?e.originalEvent.touches[0]:e).originalEvent?e.originalEvent:e}function o(e){return t(e.find(f.doRender)[0]||e[0])}function r(i){var a=t(this),n=a.offset(),r=t(e),u=f.gap;i?(g=o(a),g._colorMode=g.data("colorMode"),p.$trigger=a,(b||l()).css(f.positionCallback.call(p,a)||{left:(b._left=n.left)-((b._left+=b._width-(r.scrollLeft()+r.width()))+u>0?b._left+u:0),top:(b._top=n.top+a.outerHeight())-((b._top+=b._height-(r.scrollTop()+r.height()))+u>0?b._top+u:0)}).show(f.animationSpeed,function(){!0!==i&&(B.toggle(!!f.opacity)._width=B.width(),m._width=m.width(),m._height=m.height(),v._height=v.height(),h.setColor(s(g[0])),d(!0))}).off(".tcp").on(A,".cp-xy-slider,.cp-z-slider,.cp-alpha",c)):p.$trigger&&t(b).hide(f.animationSpeed,function(){d(!1),p.$trigger=null}).off(".tcp")}function l(){return t("head")[f.cssPrepend?"prepend":"append"]('<style type="text/css" id="tinyColorPickerStyles">'+(f.css||F)+(f.cssAddon||"")+"</style>"),t(I).css({margin:f.margin}).appendTo("body").show(0,function(){p.$UI=b=t(this),C=f.GPU&&b.css("perspective")!==a,v=t(".cp-z-slider",this),m=t(".cp-xy-slider",this),S=t(".cp-xy-cursor",this),_=t(".cp-z-cursor",this),B=t(".cp-alpha",this),y=t(".cp-alpha-cursor",this),f.buildCallback.call(p,b),b.prepend("<div>").children().eq(0).css("width",b.children().eq(0).width()),b._width=this.offsetWidth,b._height=this.offsetHeight}).hide()}function c(e){var i=this.className.replace(/cp-(.*?)(?:\s*|$)/,"$1").replace("-","_");(e.button||e.which)>1||(e.preventDefault&&e.preventDefault(),e.returnValue=!1,g._offset=t(this).offset(),(i="xy_slider"===i?function(e){var t=n(e),i=t.pageX-g._offset.left,a=t.pageY-g._offset.top;h.setColor({s:i/m._width*100,v:100-a/m._height*100},"hsv")}:"z_slider"===i?function(e){var t=n(e).pageY-g._offset.top;h.setColor({h:360-t/v._height*360},"hsv")}:function(e){var t=(n(e).pageX-g._offset.left)/B._width;h.setColor({},"rgb",t)})(e),d(),w.on(T,function(){w.off(".tcp")}).on(k,function(e){i(e),d()}))}function d(e){var t=h.colors,i=t.hueRGB,s=(t.RND.rgb,t.RND.hsl,f.dark),n=f.light,o=h.toString(g._colorMode,f.forceAlpha),r=t.HUELuminance>.22?s:n,l=t.rgbaMixBlack.luminance>.22?s:n,c=(1-t.hsv.h)*v._height,d=t.hsv.s*m._width,p=(1-t.hsv.v)*m._height,b=t.alpha*B._width,w=C?"translate3d":"",x=g[0].value,k=g[0].hasAttribute("value")&&""===x&&e!==a;m._css={backgroundColor:"rgb("+i.r+","+i.g+","+i.b+")"},S._css={transform:w+"("+d+"px, "+p+"px, 0)",left:C?"":d,top:C?"":p,borderColor:t.RGBLuminance>.22?s:n},_._css={transform:w+"(0, "+c+"px, 0)",top:C?"":c,borderColor:"transparent "+r},B._css={backgroundColor:"#"+t.HEX},y._css={transform:w+"("+b+"px, 0, 0)",left:C?"":b,borderColor:l+" transparent"},g._css={backgroundColor:k?"":o,color:k?"":t.rgbaMixBGMixCustom.luminance>.22?s:n},g.text=k?"":x!==o?o:"",e!==a?u(e):$(u)}function u(e){m.css(m._css),S.css(S._css),_.css(_._css),B.css(B._css),y.css(y._css),f.doRender&&g.css(g._css),g.text&&g.val(g.text),f.renderCallback.call(p,g,"boolean"==typeof e?e:a)}var p,h,f,g,b,v,m,S,_,B,y,w=t(document),x=t(),k="touchmove.tcp mousemove.tcp pointermove.tcp",A="touchstart.tcp mousedown.tcp pointerdown.tcp",T="touchend.tcp mouseup.tcp pointerup.tcp",C=!1,$=e.requestAnimationFrame||e.webkitRequestAnimationFrame||function(e){e()},I='<div class="cp-color-picker"><div class="cp-z-slider"><div class="cp-z-cursor"></div></div><div class="cp-xy-slider"><div class="cp-white"></div><div class="cp-xy-cursor"></div></div><div class="cp-alpha"><div class="cp-alpha-cursor"></div></div></div>',F=".cp-color-picker{position:absolute;overflow:hidden;padding:6px 6px 0;background-color:#444;color:#bbb;font-family:Arial,Helvetica,sans-serif;font-size:12px;font-weight:400;cursor:default;border-radius:5px}.cp-color-picker>div{position:relative;overflow:hidden}.cp-xy-slider{float:left;height:128px;width:128px;margin-bottom:6px;background:linear-gradient(to right,#FFF,rgba(255,255,255,0))}.cp-white{height:100%;width:100%;background:linear-gradient(rgba(0,0,0,0),#000)}.cp-xy-cursor{position:absolute;top:0;width:10px;height:10px;margin:-5px;border:1px solid #fff;border-radius:100%;box-sizing:border-box}.cp-z-slider{float:right;margin-left:6px;height:128px;width:20px;background:linear-gradient(red 0,#f0f 17%,#00f 33%,#0ff 50%,#0f0 67%,#ff0 83%,red 100%)}.cp-z-cursor{position:absolute;margin-top:-4px;width:100%;border:4px solid #fff;border-color:transparent #fff;box-sizing:border-box}.cp-alpha{clear:both;width:100%;height:16px;margin:6px 0;background:linear-gradient(to right,#444,rgba(0,0,0,0))}.cp-alpha-cursor{position:absolute;margin-left:-4px;height:100%;border:4px solid #fff;border-color:#fff transparent;box-sizing:border-box}",N=function(e){h=this.color=new i(e),f=h.options,p=this};N.prototype={render:d,toggle:r},t.fn.colorPicker=function(i){var a=this,n=function(){};return i=t.extend({animationSpeed:150,GPU:!0,doRender:!0,customBG:"#FFF",opacity:!0,renderCallback:n,buildCallback:n,positionCallback:n,body:document.body,scrollResize:!0,gap:4,dark:"#222",light:"#DDD"},i),!p&&i.scrollResize&&t(e).on("resize.tcp scroll.tcp",function(){p.$trigger&&p.toggle.call(p.$trigger[0],!0)}),x=x.add(this),this.colorPicker=p||new N(i),this.options=i,t(i.body).off(".tcp").on(A,function(e){-1===x.add(b).add(t(b).find(e.target)).index(e.target)&&r()}),this.on("focusin.tcp click.tcp",function(e){p.color.options=t.extend(p.color.options,f=a.options),r.call(this,e)}).on("change.tcp",function(){h.setColor(this.value||"#FFF"),a.colorPicker.render(!0)}).each(function(){var e=s(this),a=e.split("("),n=o(t(this));n.data("colorMode",a[1]?a[0].substr(0,3):"HEX").attr("readonly",f.preventFocus),i.doRender&&n.css({"background-color":e,color:function(){return h.setColor(e).rgbaMixBGMixCustom.luminance>.22?i.dark:i.light}})})},t.fn.colorPicker.destroy=function(){t("*").off(".tcp"),p.toggle(!1),x=t()}});
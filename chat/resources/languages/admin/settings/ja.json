{"{product_name} has no {product_attribute_name} variants.": "％23％には％29％のバリアントはありません。", "360dialog settings": "360ダイアログ設定", "360dialog template": "360dialogテンプレート", "Abandoned cart notification": "放棄されたカートの通知", "Abandoned cart notification - Admin email": "放棄されたカートの通知-管理者の電子メール", "Abandoned cart notification - First email": "放棄されたカートの通知-最初のメール", "Abandoned cart notification - Second email": "放棄されたカートの通知-2番目の電子メール", "Accept button text": "承認ボタン名", "Account SID": "アカウントSID", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "管理領域の右から左（RTL）の読み取りレイアウトをアクティブ化します。", "Activate the Right-To-Left (RTL) reading layout.": "右から左（RTL）の読み取りレイアウトをアクティブにします。", "Activate the Slack integration.": "Slack統合をアクティブにします。", "Activate the Zendesk integration": "Zendesk統合をアクティブ化する", "Activate this option if you don't want to translate the settings area.": "設定領域を翻訳したくない場合は、このオプションを有効にします。", "Active": "アクティブ", "Active - admin": "アクティブ - 管理者", "Active eCommerce CMS URL. Ex. https://shop.com/": "％1％CMSURL。元。 https://shop.com/", "Active eCommerce URL": "％1％URL", "Active for agents": "エージェントに対してアクティブ", "Active for users": "ユーザーに対してアクティブ", "Active webhooks": "アクティブなWebhook", "Add a delay (ms) to the bot's responses. Default is 2000.": "チャットボットの応答に遅延 (ミリ秒) を設定します。デフォルトは 2000 です。", "Add and manage additional support departments.": "部門の追加をします。", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "エージェントがチャット エディターで使用できる保存済み返信を追加および管理します。 # に続いて返信名とスペースを入力すると、保存された返信を印刷できます。改行するには \\n を使用します。", "Add and manage tags.": "タグを追加して管理します。", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "カンマ区切りのWordPressユーザーロールを追加します。サポートボードの管理領域は、デフォルトの役割（編集者、管理者、作成者）に加えて、新しい役割で使用できるようになります。", "Add custom fields to the new ticket form.": "新しいチケットフォームにカスタムフィールドを追加します。", "Add custom fields to the user profile details.": "ユーザープロファイルの詳細にカスタムフィールドを追加します。", "Add Intents": "インテントを追加", "Add Intents to saved replies": "保存された返信”リストにインテントを追加", "Add WhatsApp phone number details here.": "WhatsApp の電話番号の詳細をここに追加します。", "Adjust the chat button position. Values are in px.": "チャットボタンの位置を調整します。値はピクセル単位です。", "Admin icon": "管理アイコン", "Admin IDs": "管理者ID", "Admin login logo": "管理者ログインロゴ", "Admin login message": "管理者ログインメッセージ", "Admin notifications": "管理者への通知", "Admin title": "管理者の役職", "Agent area": "エージェントエリア", "Agent details": "エージェントの詳細", "Agent email notifications": "エージェントへのメール通知", "Agent ID": "エージェントID", "Agent linking": "エージェントのリンク", "Agent message template": "エージェントメッセージテンプレート", "Agent notification email": "エージェントへのメール通知テンプレート", "Agent privileges": "エージェントの権限", "Agents": "エージェント", "Agents and admins tab": "[エージェントと管理者]タブ", "Agents menu": "エージェントメニュー", "Agents only": "エージェントのみ", "All": "全て", "All channels": "すべてのチャネル", "All messages": "全メッセージ（管理エリア閉じた・最小化時）", "All questions": "すべての質問", "Allow duplicate emails and phone numbers": "メールアドレスと電話番号の重複を許可する", "Allow only extended licenses": "拡張ライセンスのみを許可する", "Allow only one conversation": "会話スレッドを１つにまとめる", "Allow only one conversation per user.": "１ユーザーに対して１つの会話スレッドのみ許可します。", "Allow registration with an email and a phone number already registered.": "すでに登録されているメールアドレスと電話番号での登録を許可します。", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "回答がわかっていて、メールパイピングがアクティブな場合は、チャットボットがユーザーのメールに返信できるようにします。", "Allow the chatbot to reply to the user's text messages if the answer is known.": "回答がわかっている場合は、チャットボットがユーザーのテキストメッセージに返信できるようにします。", "Allow the user to archive a conversation and hide archived conversations.": "ユーザーが会話をアーカイブし、アーカイブされた会話を非表示にできるようにします。", "Allow users to contact you via their favorite messaging apps.": "ユーザーがお気に入りのメッセージ アプリからあなたに連絡できるようにします。", "Allow users to select a product on ticket creation.": "ユーザーがチケット作成時に製品を選択できるようにします。", "Always all messages": "全メッセージ", "Always incoming messages only": "受信メッセージのみ", "Always sort conversations by date in the admin area.": "管理エリアでは、会話を常に日付順に並べ替えます。", "API key": "APIキー", "Append the registration user details to the success message.": "登録ユーザーの詳細を成功メッセージに追加します。", "Apply a custom background image for the header area.": "ヘッダー領域にカスタム背景画像を適用します。", "Apply changes": "変更を適用する", "Apply to": "適用", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Slackアプリですべてのユーザーチャンネルをアーカイブします。この操作は、完了するまでに長い時間がかかる場合があります。重要：すべてのSlackチャネルがアーカイブされます。", "Archive automatically the conversations marked as read every 24h.": "24時間ごとに既読としてマークされた会話を自動的にアーカイブします。", "Archive channels": "アーカイブチャンネル", "Archive channels now": "今すぐチャンネルをアーカイブ", "Articles": "記事", "Articles button link": "記事ボタンリンク", "Artificial Intelligence": "人口知能", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Google ビジネス メッセージから開始されたすべての会話に設定した部門を割り当てます。", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Twitter から開始されたすべての会話に部門を割り当てます。部門IDを入力します。", "Assign a department to all conversations started from Viber. Enter the department ID.": "Viber から開始されたすべての会話に部門を割り当てます。部門IDを入力します。", "Assign a department to all conversations started from WeChat. Enter the department ID.": "WeChat から開始されたすべての会話に部門を割り当てます。部門IDを入力します。", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Google ビジネス メッセージのさまざまな場所から開始された会話にさまざまな部門を割り当てます。この設定は部門ID（初期設定）を上書きします。", "Assistant": "アシスタント", "Assistant ID": "アシスタントID", "Attachments list": "添付ファイル一覧", "Audio file URL - admin": "音声ファイルのURL（管理者）", "Automatic": "自動", "Automatic human takeover": "自動的に人間のエージェントに切り替え", "Automatic translation": "自動翻訳", "Automatic updates": "自動更新", "Automatically archive conversations": "会話を自動的にアーカイブする", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "ユーザーのアクティブな計画に基づいて部門を自動的に割り当てます。プランのないユーザーのプランIDとして-1を挿入します。", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "新しいアップデートを自動的にチェックしてインストールします。有効なEnvato購入コードと有効なアプリのライセンスキーが必要です。", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Webサイトごとに部門を自動的に作成し、各Webサイトの会話を適切な部門にルーティングします。この設定には、WordPressマルチサイトのインストールが必要です。", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "カートに商品が入っている顧客にカートのリマインダーを自動的に送信します。次のマージフィールドなどを使用できます：％22％、％25％、％26％、％24％、％18％。", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "{R} から離れることなく、Zendesk 顧客を {R} と自動的に同期したり、Zendesk チケットを表示したり、新しいチケットを作成したりできます。", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "商品、カテゴリ、タグなどをDialogflowと自動的に同期し、ボットがショップに関連する質問に自律的に回答できるようにします。", "Automatically translate admin area": "管理エリアを自動的に翻訳", "Automatically translate the admin area to match the agent profile language or browser language.": "管理領域をエージェントプロファイル言語またはブラウザ言語に一致するように自動的に翻訳します。", "Avatar image": "アバター画像", "Away mode": "アウェイモード", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "チャットを開始する前に、ユーザーはアクセスするためにプライバシー メッセージを受け入れる必要があります。", "Birthday": "誕生日", "Bot name": "ボット名", "Bot profile image": "ボットのプロフィール画像", "Bot response delay": "チャットボットの応答時間", "Bottom": "下", "Brand": "ブランド", "Built-in chat button icons": "チャットボタンのアイコン", "Business Account ID": "ビジネスアカウントID", "Button action": "ボタンアクション", "Button name": "ボタン", "Button text": "ボタンテキスト", "Cancel button text": "キャンセルボタンのテキスト", "Cart": "カート", "Cart follow up message": "カートフォローアップメッセージ", "Catalogue details": "カタログの詳細", "Catalogue ID": "カタログID", "Change the chat button image with a custom one.": "チャットボタンの画像をカスタム画像に変更します。", "Change the default field names.": "デフォルトのフィールド名を変更します。", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "チャットウィジェットのヘッダー領域のメッセージテキストを変更します。このテキストは、最初の返信が送信されると、エージェントの見出しに置き換えられます。", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "チャットウィジェットのヘッダー領域のタイトルテキストを変更します。このテキストは、最初の返信が送信されるとエージェントの名前に置き換えられます。", "Channel ID": "チャンネルID", "Channels": "チャネル", "Channels filter": "チャンネルフィルター", "Chat": "チャット", "Chat and admin": "チャットと管理エリア", "Chat background": "チャットの背景", "Chat button icon": "チャットボタンアイコン", "Chat button offset": "チャットボタンのオフセット", "Chat message": "チャットメッセージ", "Chat only": "チャットのみ", "Chat position": "チャットの位置", "Chatbot": "チャットボット", "Chatbot mode": "チャットボットモード", "Chatbot training": "チャットボットトレーニング", "Check Requirements": "要件を確認する", "Check the server configurations and make sure it has all the requirements.": "サーバー構成をチェックして、すべての要件が満たされていることを確認してください。", "Checkout": "チェックアウト", "Choose a background texture for the chat header and conversation area.": "チャットヘッダーと会話領域の背景テクスチャを選択します。", "Choose where to display the chat. Enter the values separated by commas.": "チャットを表示する場所を選択します。コンマで区切って値を入力します。", "Choose which fields to disable from the tickets area.": "チケット領域から無効にするフィールドを選択します。", "Choose which fields to include in the new ticket form.": "新しいチケットフォームに含めるフィールドを選択します。", "Choose which user system the front-end chat will use to register and log in users.": "フロントエンドチャットがユーザーの登録とログインに使用するユーザーシステムを選択します。", "City": "市町村区", "Click the button to start the Dialogflow synchronization.": "ボタンをクリックして、Dialogflowの同期を開始します。", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "ボタンをクリックして、Slack同期を開始します。ローカルホストはメッセージを受信できず、受信しません。別のアカウントでログインするか、訪問者としてログインしてテストを実行します。", "Client email": "クライアントの電子メール", "Client ID": "クライアントID", "Client token": "クライアントトークン", "Close chat": "チャットを閉じる", "Close message": "メッセージを閉じる", "Cloud API numbers": "クラウド API 番号", "Cloud API settings": "クラウド API 設定", "Cloud API template fallback": "クラウド API テンプレートのフォールバック", "Collapse panels": "パネルを折りたたむ", "Color": "色", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Slackから直接ユーザーと通信します。メッセージや添付ファイルの送受信、絵文字の使用など。", "Company": "会社", "Concurrent chats": "同時チャット", "Configuration URL": "構成URL", "Confirm button text": "確認ボタンのテキスト", "Confirmation message": "確認メッセージ", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "世界で最も先進的な人工知能の1つを使用して、スマートチャットボットを接続し、会話を自動化します。", "Connect stores to agents.": "ストアをエージェントに接続します。", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Telegram ボットを {R} に接続すると、Telegram ボットに送信されたすべてのメッセージを {R} で直接読んで返信できます。", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Viber ボットを {R} に接続すると、Viber ボットに直接送信されたすべてのメッセージを {R} で読んで返信できます。", "Content": "コンテンツ", "Content template SID": "コンテンツ テンプレート SID", "Conversation profile": "会話プロファイル", "Conversations data": "会話データ", "Convert all emails": "すべてのメールを変換する", "Cookie domain": "クッキードメイン", "Country": "国", "Coupon discount (%)": "クーポン割引（％）", "Coupon expiration (days)": "クーポンの有効期限（日）", "Coupon expiration (seconds)": "クーポンの有効期限（秒）", "Create a WordPress user upon registration.": "登録時にWordPressユーザーを作成します。", "Create Intents now": "今すぐ％6％を作成", "Currency symbol": "通貨記号", "Custom CSS": "カスタムCSS", "Custom fields": "カスタムフィールド", "Custom JS": "カスタムJS", "Custom model ID": "カスタム モデル ID", "Custom parameters": "カスタムパラメーター", "Dashboard display": "ダッシュボードを初期表示", "Dashboard title": "ダッシュボードのタイトル", "Database details": "データベースの詳細", "Database host": "データベースホスト", "Database name": "データベース名", "Database password": "データベースパスワード", "Database prefix": "データベースプレフィックス", "Database user": "データベースユーザー", "Decline button text": "拒否ボタン名", "Declined message": "拒否されたメッセージ", "Default": "初期設定", "Default body text": "ボディテキスト", "Default conversation name": "デフォルトの会話名", "Default department": "デフォルトの部門", "Default department ID": "部門ID（初期設定）", "Default form": "デフォルトのフォーム", "Default header text": "ヘッダーテキスト", "Delay (ms)": "遅延（ミリ秒）", "Delete all leads and all messages and conversations linked to them.": "すべてのリードと、それらにリンクされているすべてのメッセージと会話を削除します。", "Delete conversation": "会話を削除する", "Delete leads": "リードを削除する", "Delete message": "メッセージの削除", "Delete training": "トレーニング削除", "Delimiter": "デリミタ（区切り）", "Department": "部門", "Department ID": "部門ID", "Departments": "部門", "Departments settings": "部門の設定", "Desktop notifications": "デスクトップ通知", "Dialogflow - Department linking": "Dialogflow - 部門リンク", "Dialogflow chatbot": "Dialogflow チャットボット", "Dialogflow edition": "Dialogflowエディション", "Dialogflow Intent detection confidence": "Dialogflow インテント検出の信頼度", "Dialogflow location": "Dialogflowロケーション", "Dialogflow spelling correction": "Dialogflow のスペル修正", "Dialogflow welcome Intent": "Dialogflow のウェルカムインテント", "Disable agents check": "エージェントチェックを無効にする", "Disable and hide the chat widget if all agents are offline.": "エージェントが全員、オフラインの場合にチャットウィジェットを非表示にします。", "Disable and hide the chat widget outside of scheduled office hours.": "「その他＞予定営業時間」で設定された営業時間外ではチャットウィジェットを非表示にします。", "Disable any features that you don't need.": "必要のない機能を無効にします。", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "チャットウィジェットの自動初期化を無効にします。この設定がアクティブな場合は、自分で作成したカスタムJavaScriptAPIコードを使用してチャットウィジェットを初期化する必要があります。チャットが表示されず、この設定が有効になっている場合は、無効にします。", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "チケットエリアの自動初期化を無効にします。この設定がアクティブな場合は、自分で作成したカスタムJavaScriptAPIコードを使用してチケット領域を初期化する必要があります。チケットエリアが表示されず、この設定が有効になっている場合は、無効にします。", "Disable chatbot": "チャットボット無効", "Disable cron job": "Cronジョブを無効にする", "Disable dashboard": "ダッシュボードを無効化", "Disable during office hours": "営業時間中に無効にする", "Disable email": "Emailフィールド非表示", "Disable features": "機能を無効にする", "Disable features you don't use and improve the chat performance.": "使用しない機能を無効にして、チャットのパフォーマンスを向上させます。", "Disable file uploading capabilities within the chat.": "チャット内のファイルアップロード機能を無効にします。", "Disable for messaging channels": "メッセージングチャネルを無効にする", "Disable for the tickets area": "チケットエリアを無効にする", "Disable invitation": "招待を無効にする", "Disable outside of office hours": "営業時間外に無効にする", "Disable password": "パスワードを無効にする", "Disable registration during office hours": "営業時間中の登録を無効にする", "Disable registration if agents online": "エージェントがオンラインの場合は登録を無効にする", "Disable the automatic invitation of agents to the channels.": "チャネルへのエージェントの自動招待を無効にします。", "Disable the channels filter.": "チャンネルフィルターを無効にします。", "Disable the chatbot for the tickets area.": "チケットエリアのチャットボットを無効にします。", "Disable the chatbot for this channel only.": "このチャネルに対してのみチャットボットを無効にします。", "Disable the dashboard, and allow only one conversation per user.": "チャットウィジェット内でダッシュボードを無効化します。", "Disable the login and remove the password field from the registration form.": "ログインを無効にし、登録フォームからパスワードフィールドを削除します。", "Disable uploads": "アップロードを無効にする", "Disable voice message capabilities within the chat.": "チャット内の音声メッセージ機能を無効にします。", "Disable voice messages": "音声メッセージを無効にする", "Disabled": "無効", "Display a brand image in the header area. This only applies for the 'brand' header type.": "ヘッダー領域にブランドイメージを表示します。これは、「ブランド」ヘッダータイプにのみ適用されます。", "Display images": "画像を表示する", "Display in conversation list": "会話リストに表示する", "Display in dashboard": "ダッシュボードに表示", "Display the articles section in the right area.": "右側の領域に記事セクションを表示します。", "Display the dashboard instead of the chat area on initialization.": "この設定を有効化するとチャットウィジェットを開いた際、チャットの会話画面の代わりにダッシュボードが表示されます。", "Display the user full name in the left panel instead of the conversation title.": "左側のパネルに会話タイトルの代わりにユーザーのフルネームを表示します。", "Display the user's profile image within the chat.": "チャット内にユーザーのプロフィール画像を表示します。", "Display user name in header": "ヘッダーにユーザー名を表示する", "Display user's profile image": "ユーザーのプロフィール画像を表示する", "Displays additional columns in the user table. Enter the name of the fields to add.": "ユーザー テーブルに追加の列を表示します。追加するフィールドの名前を入力します。", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "エージェント間で会話を均等に分配し、訪問者にキュー内の自身のポジションを通知します。レスポンス時間は分単位です。メッセージ内で以下のマージフィールドを使用できます： {position}、{minutes}。これらはリアルタイムで実際の値に置き換えられます。", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "エージェント間で会話を均等に分配し、エージェントが他のエージェントの会話を閲覧できないようにブロックします。", "Do not send email notifications to admins": "管理者に電子メール通知を送信しない", "Do not show tickets in chat": "チャットにチケットを表示しない", "Do not translate settings area": "設定領域を翻訳しない", "Download": "ダウンロード", "Edit profile": "プロファイル編集", "Edit user": "ユーザーの編集", "Email address": "電子メールアドレス", "Email and ticket": "メールとチケット", "Email header": "メールヘッダー", "Email notification delay (hours)": "電子メール通知の遅延（時間）", "Email notifications via cron job": "Cronジョブによるメール通知", "Email only": "メールのみ", "Email piping": "メールパイピング", "Email piping server information and more settings.": "メールパイピングサーバー情報等の設定。", "Email request message": "メールリクエストメッセージ", "Email signature": "メールの署名", "Email template for the notification email that is sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "エージェントが返信したときにユーザーに送信される通知メールのためのメールテンプレートです。テキスト、HTML、および次のマージフィールドを使用できます：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}", "Enable logging of agent activity": "エージェントアクティビティのログを有効にする", "Enable logs": "ログを有効にする", "Enable the chatbot outside of scheduled office hours only.": "営業時間外のみチャットボットを有効にします。", "Enable the registration only if all agents are offline.": "すべてのエージェントがオフラインの場合にのみ登録を有効にします。", "Enable the registration outside of scheduled office hours only.": "営業時間外のみ登録を有効にする。", "Enable this option if email notifications are sent via cron job.": "メール通知がCronジョブ経由で送信する場合は、有効にします。", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "サブスクライバーのみのチケットとチャットのサポートを有効にし、管理領域でメンバープロファイルの詳細とサブスクリプションの詳細を表示します。", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "ボット トークンを入力し、ボタンをクリックして Telegram ボットを同期します。", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "ボット トークンを入力し、ボタンをクリックして Viber ボットを同期します。ローカルホストはメッセージを受信できません。", "Enter the database details of the Active eCommerce CMS database.": "Active eCommerce CMS データベースのデータベース詳細を入力してください。", "Enter the database details of the Martfury database.": "Martfury データベースのデータベースの詳細を入力します。", "Enter the database details of the Perfex database.": "Perfex データベースのデータベースの詳細を入力します。", "Enter the database details of the WHMCS database.": "WHMCS データベースのデータベースの詳細を入力します。", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "ユーザーの質問に動的な回答が必要な場合に、チャットボットが使用するデフォルト メッセージを入力します。", "Enter the details of your Google Business Messages.": "Googleビジネスメッセージの詳細を入力します。", "Enter the details of your Twitter app.": "Twitter アプリの詳細を入力します。", "Enter the LINE details to start using it. Localhost cannot receive messages.": "LINEの詳細を入力します。", "Enter the URL of a .css file, to load it automatically in the admin area.": ".cssファイルのURLを入力して、管理領域に自動的にロードします。", "Enter the URL of a .js file, to load it automatically in the admin area.": ".jsファイルのURLを入力して、管理領域に自動的にロードします。", "Enter the URL of the sources you want to train the OpenAI chatbot from.": "OpenAI チャットボットのトレーニング元となるソースの URL を入力します。", "Enter the URLs of your shop": "お店のURLを入力してください", "Enter the WeChat official account token. See the docs for more details.": "WeChat 公式アカウントのトークンを入力します。詳細については、ドキュメントを参照してください。", "Enter your 360dialog account settings information.": "360dialogアカウント設定情報を入力します。", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Envato購入コードを入力して、自動更新をアクティブにし、すべての機能のロックを解除します。", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Twi<PERSON>アカウントの詳細を入力します。テキストと次のマージフィールドを使用できます：％15％、％12％、％13％、％21％、{sender_email}、％11％。", "Enter your Twilio account settings information.": "<PERSON><PERSON><PERSON>アカウント設定情報を入力します。", "Enter your WeChat Official Account information.": "WeChatアカウントの情報を入力します。", "Enter your Zendesk information.": "Zendeskの情報を入力します。", "Entities": "エントリー", "Envato Purchase Code": "Envato購入コード", "Envato purchase code validation": "Envato 購入コードの検証", "Exclude products": "製品を除外する", "Export all settings.": "すべての設定をエクスポートします。", "Export settings": "設定のエクスポート", "Facebook pages": "Facebookページ", "Fallback message": "フォールバックメッセージ", "Filters": "フィルター", "First chat message": "最初のチャットメッセージ", "First reminder delay (hours)": "最初のリマインダー遅延（時間）", "First ticket form": "最初のチケットフォーム", "Flash notifications": "フラッシュ通知", "Follow up - Email": "フォローアップ-メール", "Follow up message": "フォローアップメッセージ", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "エージェント（人間）とユーザーの会話を監視してリアルタイムに返答の内容の提案を行います。", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "フォローアップメールテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：％22％、％24％、％18％。", "Force language": "言語を強制する", "Force log out": "強制ログアウト", "Force the chat to ignore the language preferences, and to use always the same language.": "チャットで言語設定を無視し、常に同じ言語を使用するように強制します。", "Force the loggout of Support Board agents if they are not logged in WordPress.": "WordPressにログインしていない場合は、Support Boardエージェントのログアウトを強制します。", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "ユーザーがストアごとに異なる会話を使用するように強制し、他のストアからの会話をストア管理者から非表示にします。", "Force users to use only one phone country code.": "電話番号登録時の国コードを指定した１つに制限します。", "Form message": "フォームメッセージ", "Form title": "フォームのタイトル", "Frequency penalty": "周波数ペナルティ", "Full visitor details": "訪問者の詳細データ", "Generate conversations data": "会話データを生成する", "Generate user expressions": "ユーザー表現の生成", "Get configuration URL": "構成URLを取得する", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "％1％のルートディレクトリにあるファイル.envのAPP_KEY値から取得します。", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Martfury のルート ディレクトリにある .env ファイルの APP_KEY 値から取得します。", "Get Path": "パスを取得", "Get Service Worker path": "Service Worker パスを取得", "Get URL": "URLを取得", "Google and Dialogflow settings.": "Google と Dialogflow の設定。", "Google search": "Google検索", "Header background image": "ヘッダーの背景画像", "Header brand image": "ヘッダーブランドイメージ", "Header message": "ヘッダーメッセージ", "Header title": "ヘッダータイトル", "Header type": "ヘッダータイプ", "Hide": "隠れる", "Hide agent's profile image": "エージェントのプロフィール画像を非表示にする", "Hide archived tickets": "アーカイブされたチケットを非表示にする", "Hide archived tickets from users.": "アーカイブされたチケットをユーザーから非表示にします。", "Hide chat if no agents online": "エージェント不在時のチャット非表示", "Hide chat outside of office hours": "営業時間外のチャット非表示", "Hide conversations of other agents": "他のエージェントの会話を非表示にする", "Hide on mobile": "モバイルで非表示", "Hide the agent's profile image within the chat.": "チャット内でエージェントのプロフィール画像を非表示にします。", "Hide tickets from the chat widget and chats from the ticket area.": "チャット ウィジェットからチケットを非表示にし、チケット エリアからチャットを非表示にします。", "Hide timetable": "営業時間を非表示にする", "Host": "ホスト", "Human takeover": "人間へ切り替え", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "指定された時間間隔内にエージェントが応答しない場合、電子メールなどのユーザーの詳細を要求するメッセージが送信されます。", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "チャットボットがユーザーの質問を理解できない場合は、会話を人間のエージェントに転送します。", "Image": "画像", "Import admins": "管理者のインポート", "Import all settings.": "すべての設定をインポートします。", "Import articles": "記事をインポートする", "Import contacts": "連絡先をインポートする", "Import customers": "顧客の輸入", "Import customers into Support Board. Only new customers will be imported.": "顧客をSupport Boardにインポートします。新規顧客のみがインポートされます。", "Import settings": "設定のインポート", "Import users": "ユーザーのインポート", "Import vendors": "輸入業者", "Import vendors into Support Board as agents. Only new vendors will be imported.": "ベンダーをエージェントとして Support Board にインポートします。新しいベンダーのみがインポートされます。", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "PusherとWebSocketを使用してチャットのパフォーマンスを向上させます。この設定は、サーバーの速度を低下させ、代わりにWebSocketを使用するすべてのAJAX / HTTPリアルタイムリクエストを停止します。", "Include custom fields": "カスタムフィールドを含める", "Include custom fields in the registration form.": "登録フォームにカスタムフィールドを含めます。", "Include the password field in the registration form.": "チャットの登録フォームに”パスワード”フィールドを表示します。", "Incoming conversations and messages": "着信会話とメッセージ", "Incoming conversations only": "着信会話のみ", "Incoming messages only": "受信メッセージのみ（管理エリア閉じた・最小化時）", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "％1％をサポートボードと統合することで、売り上げを増やし、あなたと売り手を顧客とリアルタイムで結び付けます。", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "WooCommerceをSupportBoardと統合することで、売り上げを伸ばし、より良いサポートを提供し、より迅速なソリューションを提供します。", "Info message": "情報メッセージ", "Initialize and display the chat widget and tickets only for members.": "メンバー専用のチャットウィジェットとチケットを初期化して表示します。", "Initialize and display the chat widget only when the user is logged in.": "ログインしているユーザーにのみチャットを表示します。", "Instance ID": "インスタンスID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "OpenCart を {R} と統合して、顧客のリアルタイム同期、注文履歴へのアクセス、顧客カートの可視化を実現します。", "Interval (sec)": "間隔（秒）", "Label": "ラベル", "Language": "言語", "Language detection": "言語検出", "Language detection message": "言語検出メッセージ", "Last name": "名", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "この設定がわからない場合は、空白のままにしてください。間違った値を入力すると、チャットが中断されます。メインドメインとサブドメイン間でログインと会話を共有できるようにするためにチャットが使用されるメインドメインを設定します。", "Left": "左", "Left panel": "左パネル", "Left profile image": "左のプロフィール画像", "Let the bot search on Google to find answers to user questions.": "ボットが Google で検索して、ユーザーの質問に対する回答を見つけられるようにします。", "Let the bot to search on Google to find answers to user questions.": "ボットがGoogleで検索して、ユーザーの質問に対する回答を見つけられるようにします。", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "ユーザーが Twitter 経由で連絡できるようにします。 {R} から Twitter アカウントに直接送信されたメッセージを読んで返信します。", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "ユーザーが WeChat 経由であなたに連絡できるようにします。{R} から直接 WeChat 公式アカウントに送信されたすべてのメッセージを読んで返信します。", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "ユーザーが WhatsApp 経由で連絡できるようにします。 {R} から WhatsApp Business アカウントに直接送信されたすべてのメッセージを読んで返信します。", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "各エージェントを対応するSlackユーザーにリンクすると、エージェントがSlackを介して応答すると、割り当てられたエージェントとして表示されます。", "Link name": "リンク名", "Login form": "ログインフォーム", "Login initialization": "ログイン時のみチャットを許可", "Login verification URL": "ログイン認証URL", "Logit bias": "ロジックバイアス", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "はじめにDialgflowのバックアップを行って下さい。この処理には数分掛かります。", "Make the registration phone field mandatory.": "登録フォームの電話番号フィールドを必須にします。", "Manage": "管理", "Manage here the departments settings.": "ここで部門の設定を管理します。", "Manage personalized questions and answers.": "パーソナライズされた質問と回答を管理します。", "Manage the tags settings.": "タグ設定を管理します。", "Manifest file URL": "ManifestファイルのURL", "Manual": "マニュアル", "Manual initialization": "手動初期化", "Martfury root directory path, e.g. /var/www/": "Mart<PERSON>ry のルート ディレクトリ パス (例: /var/www/)", "Martfury shop URL, e.g. https://shop.com": "Martfury ショップの URL、例: https://shop.com", "Max message limit": "最大メッセージ数", "Max tokens": "最大トークン", "Members only": "メンバーのみ", "Members with an active paid plan only": "有効な有料プランのメンバーのみ", "Message": "メッセージ", "Message rewrite button": "メッセージ書き換えボタン", "Message template": "メッセージテンプレート", "Message type": "メッセージタイプ", "Messaging channels": "メッセージング チャネル", "Messenger and Instagram settings": "Messenger と Instagram の設定", "Minify JS": "JSを縮小する", "Minimal": "最小限", "Model": "モデル", "Multilingual": "多言語", "Multilingual plugin": "多言語プラグイン", "Multilingual via translation": "翻訳による多言語化", "Multlilingual training sources": "多言語のトレーニング ソース", "Name": "姓", "Namespace": "ネームスペース", "New conversation email": "新しい会話のお知らせコンテンツ", "New conversation notification": "新しい会話のお知らせ", "New ticket button": "新しいチケットボタン", "Newsletter": "ニュースレター", "No delay": "遅延なし", "No results found.": "結果が見つかりません。", "No, we don't ship in": "いいえ、発送しません", "None": "なし", "Note data scraping": "ノートデータのスクレイピング", "Notes": "ノート", "Notifications icon": "通知アイコン", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "メッセージがスケジュールされた営業時間外に送信された場合、またはすべてのエージェントがオフラインになった場合に、ユーザーに通知します。", "Offline message": "オフラインメッセージ", "Offset": "オフセット", "Omit previous messages": "以前のメッセージを省略する", "On chat open": "チャットオープン時", "On page load": "ページの読み込み時", "One conversation per agent": "エージェントごとに 1 つの会話", "One conversation per department": "部門ごとに 1 つの会話", "Online users notification": "オンラインユーザーへの通知", "Only desktop": "デスクトップのみ", "Only general questions": "一般的な質問のみ", "Only mobile devices": "モバイルデバイスのみ", "Only questions related to your sources": "情報源に関連する質問のみ", "Open automatically": "自動的に開く", "Open chat": "チャットを開く", "Open the chat window automatically when a new message is received.": "新しいメッセージを受信すると、チャットウィンドウが自動的に開きます。", "OpenAI - Questions and answers": "OpenAI - 質問と回答", "OpenAI - Training sources": "OpenAI - トレーニング ソース", "OpenAI - Training sources - PDF and Text Files": "OpenAI - トレーニング ソース - PDF およびテキスト ファイル", "OpenAI - Training sources options": "OpenAI - トレーニング ソースのオプション", "OpenAI Assistants - Department linking": "OpenAI Assistants - 部門リンク", "OpenAI settings.": "OpenAI の設定。", "Optional link": "オプションのリンク", "Optional registration fields": "登録フォームのオプション", "Options associated with the training procedure.": "トレーニング手順に関連するオプション。", "Order webhook": "Webhookを注文する", "Other": "他の", "Outgoing SMTP server information.": "送信SMTPサーバー情報。", "Page ID": "ページID", "Page IDs": "ページID", "Page name": "ページ名", "Page token": "ページトークン", "Panel height": "パネルの高さ", "Panel name": "パネル", "Panel title": "パネルタイトル", "Panels arrows": "パネルの矢印", "Password": "パスワード", "Perfex URL": "PerfexURL", "Performance optimization": "パフォーマンスの最適化", "Phone": "電話", "Phone codes": "電話コード", "Phone number ID": "電話番号ID", "Phone required": "電話番号の登録を必須にする", "Place ID": "場所ID", "Placeholder text": "プレースホルダーテキスト", "Play a sound for new messages and conversations.": "新しいメッセージや会話があったときにサウンドを再生します。", "Popup message": "ポップアップメッセージ", "Port": "ポート", "Post Type slugs": "％10％スラッグ", "Presence penalty": "プレゼンスペナルティ", "Prevent admins from receiving email notifications.": "管理者が電子メール通知を受信できないようにします。", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "エージェントが他のエージェントに割り当てられた会話を表示できないようにします。この設定は、ルーティングまたはキューがアクティブな場合に自動的に有効になります。", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "1 つのデバイスからチャットボットに送信出来るメッセージ数を制限することで悪用を防ぎます。", "Primary color": "原色", "Priority": "優先順位", "Privacy link": "プライバシーリンク", "Privacy message": "プライバシーメッセージ", "Private chat": "プライベートチャット", "Private chat linking": "プライベートチャットリンク", "Private key": "秘密鍵", "Product IDs": "プロダクトID", "Product removed notification": "製品削除通知", "Product removed notification - Email": "製品削除通知-Eメール", "Profile image": "プロフィール画像", "Project ID": "プロジェクトID", "Project ID or Agent Name": "Project ID または Agent Name", "Prompt": "プロンプト", "Prompt - Message rewriting": "プロンプト - メッセージの書き換え", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Google reCAPTCHA を使用して、チケット エリアをスパムや悪用から保護してください。", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "すべてのチャット機能が含まれているチケットエリアを任意のWebページに数秒で含めることにより、顧客にヘルプデスクサポートを提供します。", "Provider": "プロバイダー", "Purchase button text": "購入ボタンのテキスト", "Push notifications": "プッシュ通知", "Push notifications settings.": "プッシュ通知の設定。", "Questions and answers": "質問と回答", "Queue": "キュー", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Google 検索、マップ、ブランド所有チャネルから送信されたメッセージを {R} で直接読んで返信します。", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "{R} から Facebook ページや Instagram アカウントに直接送信されたすべてのメッセージを読んで管理し、返信します。", "Reconnect": "再接続", "Redirect the user to the registration link instead of showing the registration form.": "登録フォームを表示する代わりに、ユーザーを登録リンクにリダイレクトします。", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "登録が必要で、ユーザーがログインしていない場合は、指定したURLにユーザーをリダイレクトします。デフォルトの登録フォームを使用するには、空白のままにします。", "Register all visitors": "訪問者として登録", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "サイト訪問者を”訪問者”として自動で登録します。このオプションが無効の場合は、チャットしたサイト訪問者のみ”訪問者”として登録されます。", "Registration / Login": "登録/ログイン", "Registration and login form": "登録およびログインフォーム", "Registration form": "登録フォーム", "Registration link": "登録リンク", "Registration redirect": "登録リダイレクト", "Remove the email field from the registration form.": "登録フォームの”Email”フィールドを非表示にします。", "Rename the chat bot. Default is 'Bot'.": "チャットボットの名前を変更します。デフォルトは「ボット」です。", "Rename the visitor name prefix. Default is 'User'.": "訪問者名のプレフィックスの名前を変更します。デフォルトは「ユーザー」です。", "Repeat": "繰り返し（ユーザー）", "Repeat - admin": "繰り返し（管理者）", "Replace the admin login page message.": "管理者ログインページのメッセージを置き換えます。", "Replace the brand logo on the admin login page.": "管理者ログインページのブランドロゴを置き換えます。", "Replace the header title with the user's first name and last name when available.": "ヘッダー タイトルをユーザーの姓名 (利用可能な場合) に置き換えます。", "Replace the top-left brand icon on the admin area and the browser favicon.": "管理領域の左上のブランドアイコンとブラウザのファビコンを置き換えます。", "Reply to user emails": "ユーザーのメールに返信する", "Reply to user text messages": "ユーザーのテキストメッセージに返信する", "Reports": "レポート", "Reports area": "レポートエリア", "Request a valid Envato purchase code for registration.": "登録のために有効な Envato 購入コードをリクエストします。", "Request the user to provide their email address and then send a confirmation email to the user.": "ユーザーに電子メール アドレスの入力を要求し、確認電子メールをユーザーに送信します。", "Require phone": "電話番号の登録", "Require registration": "登録を必須にする", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "チャットを開始する前に、ユーザー登録またはログインを要求します。ログインエリアを有効にするには、パスワードフィールドを含める必要があります。", "Require the user registration or login in order to use the tickets area.": "チケットエリアの利用に際して、登録またはログインを必須にします。", "Required": "必須", "Response time": "反応時間", "Returning visitor message": "訪問者のメッセージを返す", "Rich messages": "リッチメッセージ", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "リッチメッセージは、チャットメッセージ内で利用できるコードスニペットです。HTMLコードを含むことができ、チャット内で自動的にレンダリングされます。リッチメッセージは次の構文で使用できます：[rich-message-name]。選択できる内蔵リッチメッセージがたくさんあります。", "Right": "右", "Right panel": "右パネル", "Routing": "ルーティング", "Routing if offline": "オフラインの場合のルーティング", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "言語や国といった詳細なユーザー情報を取得・保存します。", "Saved replies": "保存された返信", "Scheduled office hours": "予定営業時間", "Search engine ID": "検索エンジンID", "Second chat message": "2番目のチャットメッセージ", "Second reminder delay (hours)": "2回目のリマインダー遅延（時間）", "Secondary color": "二次色", "Secret key": "シークレットキー", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "メッセージを送信して、関心のある製品を購入できるが、現在在庫がない場合に顧客に通知できるようにします。次のマージフィールドを使用できます：％18％、％23％。", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "新しいユーザーが最初のチケットを作成するときにメッセージを送信します。テキストの書式設定とマージフィールドがサポートされています。", "Send a message to new users when they visit the website for the first time.": "新しいユーザーが初めて Web サイトにアクセスしたときにメッセージを送信します。", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "商品がカートから取り出された後、顧客にメッセージを送信します。次のマージフィールドなどを使用できます：％22％、％25％、％26％、％24％、％18％、％27％。", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "購入を完了した顧客に、購入したばかりの製品を共有するように求めるメッセージを送信します。次のマージフィールドなどを使用できます：％23％、％18％。", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "購入を完了した顧客にメッセージを送信します。次のマージフィールドなどを使用できます：％22％、％24％、％18％。", "Send a message to the user when the agent archive the conversation.": "エージェントが会話をアーカイブするときに、ユーザーにメッセージを送信します。", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "少なくとも24時間後にWebサイトに再度アクセスするユーザーにメッセージを送信します。次のマージフィールドなどを使用できます：％22％、％18％。詳細については、ドキュメントを参照してください。", "Send a test agent notification email to verify email settings.": "テストエージェントに通知メールを送信して、メール設定を確認します。", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Slackチャンネルにテストメッセージを送信します。これは、送信メッセージの送信機能のみをテストします。", "Send a test user notification email to verify email settings.": "テストユーザー通知メールを送信して、メール設定を確認します。", "Send a text message to the provided phone number.": "指定された電話番号にテキスト メッセージを送信します。", "Send a user email notification": "ユーザーに電子メール通知を送信する", "Send a user text message notifcation": "ユーザーにテキストメッセージ通知を送信する", "Send a user text message notification": "ユーザーにテキストメッセージ通知を送信する", "Send an agent email notification": "エージェントの電子メール通知を送信する", "Send an agent text message notification": "エージェントのテキスト メッセージ通知を送信する", "Send an agent user text notification": "エージェントユーザーにテキスト通知を送信する", "Send an email notification to the provided email address.": "指定された電子メール アドレスに電子メール通知を送信します。", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "エージェントのオフライン時にユーザーがメッセージを送信した際、エージェントにメールを送信します。", "Send an email to the user when a new conversation is created.": "新しい会話が作成されたときにユーザーにメール通知します。", "Send an email to the user when a new conversation or ticket is created": "新しい会話またはチケットが作成されたときにユーザーにメールを送信します", "Send an email to the user when an agent replies and the user is offline.": "ユーザーのオフライン時にエージェントがメッセージを送信した際、ユーザーにメールを送信します。", "Send email": "メールを送る", "Send login details to the specified URL and allow access only if the response is positive.": "ログインの詳細を指定された URL に送信し、応答が肯定的な場合にのみアクセスを許可します。", "Send message": "メッセージ送信", "Send message to Slack": "Slackにメッセージを送信する", "Send message via enter button": "エンターボタンでメッセージを送信", "Send text message": "テキストメッセージを送信する", "Send the message template to a WhatsApp number.": "メッセージ テンプレートを WhatsApp 番号に送信します。", "Send the message via the ENTER keyboard button.": "ENTERキーボードボタンを介してメッセージを送信します。", "Send the user details of the registration form and email rich messages to Dialogflow.": "登録フォームのユーザー詳細を送信し、豊富なメッセージをDialogflowに電子メールで送信します。", "Send the WhatsApp order details to the URL provided.": "提供された URL に WhatsApp 注文の詳細を送信します。", "Send to user's email": "ユーザーのメールに送信", "Send transcript to user's email": "会話内容をユーザーにメール送信する", "Send user details": "ユーザーの詳細を送信する", "Sender": "送信者", "Sender email": "送信用メールアドレス", "Sender name": "送信者名", "Sender number": "送信者番号", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "WhatsApp メッセージの送信が失敗した場合にテキスト メッセージを送信します。 テキストと次の差し込みフィールドを使用できます: {conversation_url_parameter}、{message}、{recipient_name}、{recipient_email}。", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "WhatsApp メッセージの送信が失敗した場合は、WhatsApp テンプレート通知を送信します。テキストと次の差し込みフィールドを使用できます: {conversation_url_parameter}、{recipient_name}、{recipient_email}。", "Service": "サービス", "Service Worker path": "Service Worker パス", "Service Worker URL": "サービスワーカーURL", "Set a dedicated Dialogflow agent for each department.": "部門ごとに専用のDialogflowエージェントを設定します。", "Set a dedicated OpenAI Assistants for each department.": "各部門に専用の OpenAI アシスタントを設定します。", "Set a dedicated Slack channel for each department.": "部門ごとに専用のSlackチャネルを設定します。", "Set a profile image for the chat bot.": "チャットボットのプロフィール画像を設定します。", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "メッセージの横に表示されるアバター画像を設定します。最大サイズが 50KB以下 かつ1024x1024px以下 の JPG 画像である必要があります。", "Set the currency symbol of the membership prices.": "会員価格の通貨記号を設定します。", "Set the currency symbol used by your system.": "システムで使用される通貨記号を設定します。", "Set the default departments for all tickets. Enter the department ID.": "すべてのチケットにデフォルトの部門を設定します。部門IDを入力します。", "Set the default email header that will be prepended to automated emails and direct emails.": "自動メールとダイレクトメールの前に追加されるデフォルトのメールヘッダーを設定します。", "Set the default email signature that will be appended to automated emails and direct emails.": "自動メールとダイレクトメールに追加されるデフォルトのメール署名を設定します。", "Set the default form to display if the registraion is required.": "登録が必要な場合に表示するデフォルトのフォームを設定します。", "Set the default name to use for conversations without a name.": "会話名のない会話に使用するデフォルトの会話名を設定します。", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "デフォルトの通知アイコンを設定します。ユーザーがプロフィール画像を持っていない場合、アイコンはプロフィール画像として使用されます。", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "エージェントが対応可能として表示される営業時間を設定します。 この設定は、営業時間に依存する他の全ての設定にも使用されます。", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "ユーザーに名前がない場合にボットメッセージやメールで使用するデフォルトのユーザー名を設定します。", "Set the header appearance.": "チャットウィジェットのヘッダースタイルを設定します。", "Set the maximum height of the tickets panel.": "チケットパネルの最大の高さを設定します。", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "使用している多言語プラグインを設定するか、サイトで1つの言語しか使用していない場合は無効のままにします。", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "エージェントまたは管理者が管理領域で非アクティブな状態が少なくとも 10 分間続くと、オフライン ステータスが自動的に設定されます。", "Set the position of the chat widget.": "チャットウィジェットの位置を設定します。", "Set the primary color of the admin area.": "管理領域の基本色を設定します。", "Set the primary color of the chat widget.": "チャットウィジェットの原色を設定します。", "Set the secondary color of the admin area.": "管理領域の二次色を設定します。", "Set the secondary color of the chat widget.": "チャットウィジェットの二次色を設定します。", "Set the tertiary color of the chat widget.": "チャットウィジェットの三次色を設定します。", "Set the title of the administration area.": "管理領域のタイトルを設定します。", "Set the title of the conversations panel.": "会話パネルのタイトルを設定します。", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "タイムゾーンを UTC で設定します。入力フィールドを空の状態にすると自動的に値が生成されます。", "Set which actions to allow agents.": "エージェントに許可するアクションを設定します。", "Set which actions to allow supervisors.": "スーパーバイザーに許可するアクションを設定します。", "Set which user details to send to the main channel. Add comma separated values.": "メインチャネルに送信するユーザーの詳細を設定します。カンマ区切りの値を追加します。", "Settings area": "設定エリア", "settings information": "設定情報", "Shop": "店", "Show": "見せる", "Show a browser tab notification when a new message is received.": "新しいメッセージを受信したときにブラウザタブの通知を表示します。", "Show a desktop notification when a new message is received.": "新しいメッセージを受信したときにデスクトップ通知を表示します。", "Show a notification and play a sound when a new user is online.": "ユーザー（全ユーザータイプ）がオンラインになったときに通知を表示し、音を鳴らします。", "Show a pop-up notification to all users.": "すべてのユーザーにポップアップ通知を表示します。", "Show profile images": "プロフィール画像を表示する", "Show sender's name": "送信者名の表示", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "ダッシュボードにエージェントメニューを表示し、会話を開始するエージェントをユーザーに選択させます。", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "訪問者がカートにアイテムを追加したときにフォローアップメッセージを表示します。メッセージは、ユーザーがまだ電子メールを提供していない場合にのみ送信されます。", "Show the list of all Slack channels.": "すべてのSlackチャンネルのリストを表示します。", "Show the profile image of agents and users within the conversation.": "会話内のエージェントとユーザーのプロフィール画像を表示します。", "Show the sender's name in every message.": "チャット”はユーザー側チャット、”管理エリア”は管理エリアに送信者名を表示します。", "Single label": "シングルラベル", "Single phone country code": "単一の電話の国コード", "Site key": "サイトキー", "Slug": "スラッグ", "Smart Reply": "スマート リプライ", "Social share message": "ソーシャルシェアメッセージ", "Sort conversations by date": "会話を日付順に並べ替える", "Sound": "音", "Sound settings": "通知音設定", "Sounds": "音", "Sounds - admin": "通知音 （管理者）", "Source links": "ソースリンク", "Speech recognition": "音声認識", "Spelling correction": "スペル修正", "Start importing": "インポートを開始します", "Stop crawler from scanning other pages": "クローラーによる他のページのスキャンを停止する", "Store name": "店舗名", "Subject": "件名", "Subscribe": "申し込む", "Subscribe message": "購読メッセージ", "Subscribe message - Email": "メッセージの購読-Eメール", "Subscribe message email template sent to the user when the user communicate the email via the subscribe message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "ユーザーがメール購読フォームを介してメール購読する際にユーザーに送信されるメールテンプレート。テキスト、HTML、および次のマージフィールドを使用できます：{user_name}, {user_email}", "Subscribe users to your preferred newsletter service when they provide an email.": "ユーザーがメールアドレスを登録する際にメールマガジンに登録します。", "Subtract the offset value from the height value.": "高さの値からオフセット値を引きます。", "Success message": "成功メッセージ", "Supervisor": "スーパーバイザーの権限", "Support Board path": "サポートボードパス", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "管理者アカウントとスタッフアカウントをサポートボードと同期します。スタッフユーザーはエージェントとして登録され、管理者は管理者として登録されます。新しいユーザーのみがインポートされます。", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "すべてのクライアントのすべての連絡先をサポートボードと同期します。新しい連絡先のみがインポートされます。", "Sync all users with Support Board. Only new users will be imported.": "すべてのユーザーをサポートボードと同期します。新しいユーザーのみがインポートされます。", "Sync all WordPress users with Support Board. Only new users will be imported.": "すべてのWordPressユーザーをサポートボードと同期します。新しいユーザーのみがインポートされます。", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "ナレッジベースの記事をサポートボードと同期します。新しい記事のみがインポートされます。", "Sync mode": "同期モード", "Synchronization": "同期", "Synchronize": "同期する", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "顧客を同期し、サブスクライバーのみのチケットとチャットのサポートを有効にし、管理エリアでサブスクリプションプランを表示します。", "Synchronize emails": "メールを同期する", "Synchronize Entities": "％9％を同期します", "Synchronize Entities now": "今すぐ％9％を同期する", "Synchronize now": "今すぐ同期", "Synchronize users": "ユーザーを同期する", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "顧客をリアルタイムで同期したり、顧客とチャットしてエンゲージメントを高めたり、より優れた迅速なサポートを提供したりできます。", "Synchronize your Messenger and Instagram accounts.": "Messenger と Instagram アカウントを同期します。", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Perfexの顧客をリアルタイムで同期し、チャットで連絡できるようにします。プロフィールの詳細を表示したり、積極的に関与したりできます。", "Synchronize your WhatsApp Cloud API account.": "WhatsApp クラウド API アカウントを同期します。", "System requirements": "システム要求", "Tags": "タグ", "Tags settings": "タグ設定", "Template default language": "テンプレートのデフォルト言語", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "エージェントが返信したときにユーザーに送信されるメールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "ユーザーが新しいメッセージを送信したときにエージェントに送信されるメールのテンプレートです。テキスト、HTML、および次のマージフィールドを使用できます：{conversation_link}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "新しい会話が作成されたときにユーザーに送信される電子メールのテンプレート。テキスト、HTML、および次の差し込み項目を使用できます: {conversation_url_parameter}、{user_name}、{message}、{attachments}、{conversation_id}。", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "新しい会話またはチケットが作成されたときにユーザーに送信される電子メールのテンプレート。テキスト、HTML、および次のマージフィールドを使用できます：{conversation_url_parameter}、{user_name}、{message}、{attachments}。", "Template languages": "テンプレート言語", "Template name": "テンプレート名", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "管理者通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：{carts}。通知を送信する電子メールを[電子メールアドレス]フィールドに入力します。", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "商品がカートから削除された後に顧客に送信される電子メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "最初の通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "2番目の通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "順番待ちリスト通知メールのテンプレート。テキスト、HTML、および次のマージフィールドなどを使用できます：{html_product_card}、{product_description}、{product_image}、{product_name}、{product_link}。", "Terms link": "規約リンク", "Tertiary color": "三次色", "Test Slack": "Slackをテストする", "Test template": "テンプレートテスト", "Text": "文章", "Text message fallback": "テキストメッセージのフォールバック", "Text message notifications": "テキストメッセージ通知", "Text messages": "テキストメッセージ", "The product is not in the cart.": "商品がカートに入っていません。", "The workspace name you are using to synchronize Slack.": "Slackの同期に使用している％4％の名前。", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "これはメインのSlackチャネルIDであり、通常は#generalチャネルです。このコードは、Slackの同期を完了することで取得できます。", "This returns the Support Board path of your server.": "これにより、サーバーの Support Board パスが返されます。", "This returns your Support Board URL.": "これにより、Support Board の URL が返されます。", "Ticket custom fields": "チケットカスタムフィールド", "Ticket email": "チケットメール", "Ticket field names": "チケットフィールド名", "Ticket fields": "チケットフィールド", "Ticket only": "チケットのみ", "Ticket products selector": "チケット商品セレクター", "Title": "タイトル", "Top": "上", "Train your chatbot": "チャットボットをトレーニングする", "Transcript": "トランスクリプト", "Transcript settings.": "トランスクリプトの設定。", "Translate automatically": "チャットウィジェットの自動翻訳", "Translate the chat widget's text elements automatically to match the user language.": "チャット ウィジェットのテキスト要素をユーザーの言語に合わせて自動的に翻訳します。", "Trigger": "トリガー", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "ウェルカムメッセージがアクティブなときに、新規訪問者に対してDialogflow％3％をトリガーします。", "Twilio settings": "<PERSON><PERSON><PERSON>設定", "Twilio template": "Twilioテンプレート", "Upload attachments to Amazon S3.": "添付ファイルを Amazon S3 にアップロードします。", "Upload the PDF and text files you want to train the OpenAI chatbot from.": "OpenAI チャットボットのトレーニングに使用する PDF ファイルとテキスト ファイルをアップロードします。", "Usage Limit": "利用制限", "Use this option to change the PWA icon. See the docs for more details.": "PWA アイコンを変更するには、このオプションを使用します。詳細についてはドキュメントを参照してください。", "User details": "ユーザーの詳細", "User details in success message": "成功メッセージに登録ユーザーの詳細を含める。", "User email notifications": "ユーザーへのメール通知", "User login form information.": "ユーザーログインフォーム情報。", "User message template": "ユーザーメッセージテンプレート", "User name as title": "タイトルをユーザー名に置き換える", "User notification email": "ユーザーへのメール通知テンプレート", "User registration form information.": "ユーザー登録フォーム情報。", "User roles": "ユーザーの役割", "User system": "ユーザーシステム", "Username": "ユーザー名", "Users and agents": "ユーザーとエージェント", "Users area": "ユーザーエリア", "Users only": "ユーザーのみ", "Users table additional columns": "ユーザーテーブルの追加の列", "UTC offset": "UTC オフセット", "Variables": "変数", "View channels": "チャンネルを見る", "View unassigned conversations": "割り当てられていない会話を表示する", "Visibility": "可視性", "Visitor default name": "訪問者のデフォルト名", "Visitor name prefix": "訪問者名のプレフィックス", "Volume": "音量（ユーザー）", "Volume - admin": "音量（管理者）", "Waiting list": "順番待ちリスト", "Waiting list - Email": "順番待ちリスト-メール", "Webhook URL": "WebhookのURL", "Webhooks": "Webhook", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhookは、何かが起こったときにユーザーが定義した一意のURLにバックグラウンドで送信される情報です。", "Website": "Webサイト", "WeChat settings": "WeChatの設定", "Welcome message": "ウェルカムメッセージ", "Whmcs admin URL": "Whmcs管理者URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs管理URL。元。 https://example.com/whmcs/admin/", "WordPress registration": "WordPress登録", "Yes, we ship in": "はい、発送します", "You haven't placed an order yet.": "まだ注文していません。", "You will get this code by completing the Dialogflow synchronization.": "このコードは、Dialogflowの同期を完了することで取得できます。", "You will get this code by completing the Slack synchronization.": "このコードは、Slackの同期を完了することで取得できます。", "You will get this information by completing the synchronization.": "同期を完了すると、この情報を取得できます。", "Your cart is empty.": "あなたのカートは空です。", "Your turn message": "あなたの番メッセージ", "Your username": "ユーザー名", "Your WhatsApp catalogue details.": "WhatsApp カタログの詳細。", "Zendesk settings": "Zendeskの設定", "Articles area": "記事エリア", "Articles page URL": "記事ページのURL", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "管理領域の会話詳細パネルやその他のパネルを自動的に折りたたみます。", "Automatically hide the conversation details panel.": "会話の詳細パネルを自動的に非表示にします。", "Body variables": "ボディ変数", "Button variables": "ボタン変数", "Choose which optional fields to include in the registration form. Name and email fields are all included by default.": "登録フォームに含めるオプション フィールドを選択します。名前とメール フィールドはすべてデフォルトで含まれています。", "Clear flows": "明確な流れ", "Customize the link for the 'All articles' button.": "「すべての記事」ボタンのリンクをカスタマイズします。", "Delete the built-in flows.": "組み込みフローを削除します。", "Display categories": "表示カテゴリ", "Display online agents only": "オンラインエージェントのみ表示", "Enter the URL of the articles page.": "記事ページのURLを入力します。", "Header variables": "ヘッダー変数", "Hide conversation details panel": "会話の詳細パネルを非表示にする", "IP banning": "IP禁止", "Restrict chat access by blocking IPs. List IPs with commas.": "IP をブロックしてチャット アクセスを制限します。IP をコンマで区切ってリストします。", "Set the articles panel title. Default is 'Help Center'.": "記事パネルのタイトルを設定します。デフォルトは「ヘルプセンター」です。", "Show the articles panel on the chat dashboard.": "チャットダッシュボードに記事パネルを表示します。", "Show the categories instead of the articles list.": "記事リストの代わりにカテゴリを表示します。", "Starred tag": "スター付きタグ", "Top bar": "トップバー", "Troubleshoot": "トラブルシューティング", "Troubleshoot problems": "問題のトラブルシューティング"}
{"{product_name} has no {product_attribute_name} variants.": "{product_name}, {product_attribute_name} varyanta sahip değil.", "360dialog settings": "360 iletişim a<PERSON>ı", "360dialog template": "360 diyalog şablonu", "Abandoned cart notification": "Terk edilmiş sepet bildirimi", "Abandoned cart notification - Admin email": "Terk edilmiş sepet bildirimi - Yönetici e-postası", "Abandoned cart notification - First email": "Terk edilmiş sepet bildirimi - İlk e-posta", "Abandoned cart notification - Second email": "Terk edilmiş sepet bildirimi - İkinci e-posta", "Accept button text": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> et", "Account SID": "Hesap SID&#39;si", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Yönetici alanı için <PERSON>a (RTL) okuma düzenini etkinleştirin.", "Activate the Right-To-Left (RTL) reading layout.": "Sağdan <PERSON> (RTL) okuma düzenini etkinleştirin.", "Activate the Slack integration.": "Slack entegra<PERSON><PERSON><PERSON><PERSON> et<PERSON>ş<PERSON>rin.", "Activate the Zendesk integration": "Zendesk entegrasyonunu etkinleştirin", "Activate this option if you don't want to translate the settings area.": "Ayarlar alanını çevirmek istemiyorsanız bu seçeneği etkinleştirin.", "Active": "Aktif", "Active - admin": "Etkin - yönetici", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce İYS URL&#39;si. <PERSON><PERSON>. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL&#39;si", "Active for agents": "Temsilciler için aktif", "Active for users": "Kullanıcılar için aktif", "Active webhooks": "Etkin web kancaları", "Add a delay (ms) to the bot's responses. Default is 2000.": "<PERSON><PERSON> yanı<PERSON><PERSON><PERSON>na bir gec<PERSON> (ms) ekleyin. Varsayılan 2000&#39;dir.", "Add and manage additional support departments.": "Ek destek departmanları ekleyin ve yönetin.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Temsilciler tarafından sohbet düzenleyicide kullanılabilecek kayıtlı yanıtları ekleyin ve yönetin. <PERSON><PERSON>tl<PERSON> yanıtlar, # ve ardından yanıt adı artı boşluk yazılarak yazdırılabilir. Satır sonu yapmak için \\n&#39;yi kullan<PERSON>n.", "Add and manage tags.": "Etiketleri ekleyin ve y<PERSON>.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Virgülle ayrılmış WordPress kullanıcı rolleri ekleyin. Destek Kurulu yönetim alanı, var<PERSON><PERSON>lana ek olarak yeni roller için kullanılabilir olacaktır: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ya<PERSON>.", "Add custom fields to the new ticket form.": "Yeni bilet formuna özel alanlar e<PERSON>in.", "Add custom fields to the user profile details.": "Kullanıcı profili ayrıntılarına özel alanlar ekleyin.", "Add Intents": "Intents ekle", "Add Intents to saved replies": "Kayıtlı yanıtlara Intents ekleyin", "Add WhatsApp phone number details here.": "WhatsApp telefon numarası ayrıntılarını buraya ekleyin.", "Adjust the chat button position. Values are in px.": "Sohbet düğmesi konumunu ayarlayın. Değerler px cinsindendir.", "Admin icon": "Yönetici simgesi", "Admin IDs": "Yönetici kimlikleri", "Admin login logo": "Yönetici oturum açma logosu", "Admin login message": "Yönetici giriş mesajı", "Admin notifications": "Yönetici bildirimleri", "Admin title": "Yönetici unvanı", "Agent area": "<PERSON><PERSON>", "Agent details": "Temsilci ayrıntıları", "Agent email notifications": "Temsilci e-posta bildirimleri", "Agent ID": "Aracı Kimliği", "Agent linking": "Aracı bağlantısı", "Agent message template": "Temsilci mesajı şablonu", "Agent notification email": "Temsilci bildirim e-postası", "Agent privileges": "Aracı ayrıcalıkları", "Agents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Agents and admins tab": "Aracılar ve yöneticiler sekmesi", "Agents menu": "<PERSON>ms<PERSON><PERSON><PERSON> menü<PERSON>ü", "Agents only": "Yalnızca aracılar", "All": "<PERSON><PERSON><PERSON>", "All channels": "<PERSON><PERSON><PERSON> kanal<PERSON>", "All messages": "<PERSON><PERSON><PERSON>", "All questions": "<PERSON><PERSON><PERSON>", "Allow duplicate emails and phone numbers": "Yinelenen e-postalara ve telefon numaralarına izin ver", "Allow only extended licenses": "Yalnızca genişletilmiş lisanslara izin ver", "Allow only one conversation": "Yalnızca bir görüşmeye izin ver", "Allow only one conversation per user.": "Kullanıcı başına yalnızca bir görüşmeye izin verin.", "Allow registration with an email and a phone number already registered.": "Bir e-posta ve önceden kaydedilmiş bir telefon numarası ile kayıt yapılmasına izin verin.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Yanıt biliniyorsa ve e-posta iletimi etkinse, sohbet robotunun kullanıcının e-postalarına yanıt vermesine izin verin.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "<PERSON><PERSON><PERSON>, sohbet <PERSON><PERSON>un kull<PERSON>ı<PERSON>ın metin mesajlarını yanıtlamasına izin verin.", "Allow the user to archive a conversation and hide archived conversations.": "Kullanıcının bir konuşmayı arşivlemesine ve arşivlenmiş konuşmaları gizlemesine izin verin.", "Allow users to contact you via their favorite messaging apps.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, favori mesajlaşma uygulamaları aracılığıyla sizinle iletişim kurmasına izin verin.", "Allow users to select a product on ticket creation.": "Kullanıcıların bilet oluştururken bir ürün seçmesine izin verin.", "Always all messages": "Her zaman tüm mesajlar", "Always incoming messages only": "Her zaman yalnızca gelen iletiler", "Always sort conversations by date in the admin area.": "Yönetici alanında konuşmaları her zaman tarihe göre sı<PERSON>ın.", "API key": "API anahtarı", "Append the registration user details to the success message.": "Başarı mesajına kayıt kullanıcı ayrıntılarını ekleyin.", "Apply a custom background image for the header area.": "Başlık alanı için özel bir arka plan resmi uygulayın.", "Apply changes": "Değişiklikleri uygula", "Apply to": "Başvurmak", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Slack uygulamasında tüm kullanıcı kanallarını arşivleyin. Bu işlemin tamamlanması uzun sürebilir. Önemli: Tüm gevşek kanallarınız arşivlenecektir.", "Archive automatically the conversations marked as read every 24h.": "Her 24 saatte bir okundu olarak işaretlenen konuşmaları otomatik olarak arşivleyin.", "Archive channels": "Arşiv kanalları", "Archive channels now": "Kanalları şimdi arşivle", "Articles": "Nesne", "Articles button link": "Makaleler düğ<PERSON>i bağlantısı", "Artificial Intelligence": "<PERSON><PERSON><PERSON> z<PERSON>", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Google Business Messages&#39;tan başlatılan tüm görüşmelere bir departman atayın. Departman kimliğini girin.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Twitter&#39;dan ba<PERSON><PERSON><PERSON>lan tüm konuşmalara bir departman atayın. Depar<PERSON> kimliğini girin.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Viber&#39;den başlatılan tüm konuşmalara bir departman atayın. <PERSON><PERSON><PERSON> kimliğini girin.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "WeChat&#39;ten başlatılan tüm konuşmalara bir departman atayın. <PERSON><PERSON><PERSON> kimli<PERSON><PERSON> girin.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Farklı Google Business Messages konumlarından başlatılan görüşmelere farklı departmanlar atayın. Bu ayar, varsayılan departmanı geçersiz kılar.", "Assistant": "Asistan", "Assistant ID": "Asistan Kimliği", "Attachments list": "Ekler listesi", "Audio file URL - admin": "Ses dosyası URL&#39;si - yönetici", "Automatic": "Otomatik", "Automatic human takeover": "Otomatik insan dev<PERSON>ma", "Automatic translation": "Otomatik çeviri", "Automatic updates": "Otomatik güncellemeler", "Automatically archive conversations": "Görüşmeleri otomatik olarak arşivle", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Kullanıcının aktif planlarına göre otomatik olarak bir departman atar. Herhangi bir planı olmayan kullanıcılar için -1&#39;i plan kimliği olarak ekleyin.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Yeni güncellemeleri otomatik olarak kontrol edin ve yükleyin. Geçerli bir Envato Satın Alma Kodu ve geçerli uygulamaların lisans anahtarları gereklidir.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Her web sitesi i<PERSON>in otomatik olarak bir departman oluşturun ve her web sitesinin konuşmalarını doğru departmana yönlendirin. Bu ayar bir WordPress Multisite kurulumu gerektirir.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Sepetlerinde ürünler bulunan müşterilere otomatik olarak sepet hatırlatıcıları gönderin. Aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Zendesk müşterilerini {R} ile otomatik olarak senkronize edin, Zendesk biletlerini görüntüleyin veya {R}&#39;dan a<PERSON><PERSON><PERSON><PERSON> yeni biletler oluşturun.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, etiketleri ve daha fazlasını Dialogflow ile otomatik olarak senkronize edin ve botun mağazanızla ilgili soruları bağımsız olarak yanıtlamasını sağlayın.", "Automatically translate admin area": "Yönetici alanını otomatik olarak çevir", "Automatically translate the admin area to match the agent profile language or browser language.": "Yönetici alanını, aracı profili diline veya tarayıcı diline uyacak şekilde otomatik olarak çevirin.", "Avatar image": "Avatar resmi", "Away mode": "Dışarıda modu", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "Sohbeti başlatmadan önce kullanıcının eri<PERSON>im ka<PERSON>abilm<PERSON> için bir gizlilik mesajını kabul etmesi gerekir.", "Birthday": "Doğum günü", "Bot name": "Bot adı", "Bot profile image": "<PERSON><PERSON> profil resmi", "Bot response delay": "<PERSON><PERSON> yanıt gecikmesi", "Bottom": "Alt", "Brand": "<PERSON><PERSON>", "Built-in chat button icons": "Yerleşik sohbet düğmesi simgeleri", "Business Account ID": "İşletme Hesabı Kimliği", "Button action": "<PERSON><PERSON><PERSON><PERSON>", "Button name": "<PERSON><PERSON><PERSON><PERSON> adı", "Button text": "<PERSON><PERSON><PERSON><PERSON>", "Cancel button text": "İptal düğ<PERSON>i metni", "Cart": "<PERSON><PERSON>", "Cart follow up message": "Sepet takip mesajı", "Catalogue details": "Katalog detayları", "Catalogue ID": "Katalog kimliği", "Change the chat button image with a custom one.": "So<PERSON>bet düğmesi resmini özel bir resimle değiştirin.", "Change the default field names.": "Varsayılan alan <PERSON>ğiştirin.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Sohbet widget&#39;ının başlık alanındaki mesaj metnini değiştirin. <PERSON><PERSON> metin, ilk yanıt gönderildikten sonra temsilci başlığıyla değiştirilecektir.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Sohbet widget&#39;ının başlık alanındaki başlık metnini değiştirin. <PERSON><PERSON> metin, ilk yanıt gönderildikten sonra temsilcinin adıyla değiştirilecektir.", "Channel ID": "Kanal kimliği", "Channels": "<PERSON><PERSON><PERSON>", "Channels filter": "Kanal filtresi", "Chat": "<PERSON><PERSON><PERSON>", "Chat and admin": "<PERSON><PERSON><PERSON> ve yönetici", "Chat background": "Sohbet arka planı", "Chat button icon": "<PERSON><PERSON><PERSON> düğ<PERSON> simgesi", "Chat button offset": "<PERSON><PERSON><PERSON>", "Chat message": "<PERSON><PERSON><PERSON> mesajı", "Chat only": "<PERSON><PERSON><PERSON>", "Chat position": "<PERSON><PERSON><PERSON> konumu", "Chatbot": "<PERSON><PERSON><PERSON>u", "Chatbot mode": "<PERSON><PERSON><PERSON> modu", "Chatbot training": "<PERSON><PERSON><PERSON>", "Check Requirements": "Gereksinimleri Kontrol Et", "Check the server configurations and make sure it has all the requirements.": "<PERSON><PERSON>u yapılandırmalarını kontrol edin ve tüm gereksinimleri karşıladığından emin olun.", "Checkout": "Çık<PERSON>ş yapmak", "Choose a background texture for the chat header and conversation area.": "So<PERSON>bet başlığı ve konuşma alanı için bir arka plan dokusu seçin.", "Choose where to display the chat. Enter the values separated by commas.": "<PERSON><PERSON><PERSON><PERSON> nerede görünt<PERSON>lene<PERSON>ğini seçin. Değerleri virgülle ayırarak girin.", "Choose which fields to disable from the tickets area.": "Biletler alanından hangi alanların devre dışı bırakılacağını seçin.", "Choose which fields to include in the new ticket form.": "Yeni bilet formuna dahil edilecek alanları seçin.", "Choose which user system the front-end chat will use to register and log in users.": "Kullanıcıları kaydetmek ve oturum açmak için ön uç sohbetin hangi kullanıcı sistemini kullanacağını seçin.", "City": "Şehir", "Click the button to start the Dialogflow synchronization.": "Dialogflow senkronizasyonunu başlatmak için düğmeye tıklayın.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Slack senkronizasyonunu başlatmak için düğmeye tıklayın. Localhost mesaj alamaz ve almaz. Testlerinizi gerçekleştirmek için başka bir hesapla veya ziyaretçi olarak oturum açın.", "Client email": "Müşteri e-postası", "Client ID": "Müşteri Kimliği", "Client token": "İstemci jetonu", "Close chat": "<PERSON><PERSON><PERSON><PERSON> kapat", "Close message": "Mesajı kapat", "Cloud API numbers": "Bulut API numaraları", "Cloud API settings": "Bulut API ayarları", "Cloud API template fallback": "Cloud API şablonu yedeği", "Collapse panels": "<PERSON><PERSON><PERSON>", "Color": "Renk", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Kullanıcılarını<PERSON><PERSON> doğrudan Slack üzerinden iletişim kurun. Mesajlar ve ekler gönderin ve alın, emoji kullanın ve çok daha fazlasını yapın.", "Company": "Şirket", "Concurrent chats": "<PERSON><PERSON><PERSON>", "Configuration URL": "Yapılandırma URL&#39;si", "Confirm button text": "<PERSON><PERSON><PERSON><PERSON>", "Confirmation message": "<PERSON>ay mesajı", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Akıllı sohbet robotlarını bağlayın ve dünyadaki en gelişmiş yapay zeka biçimlerinden birini kullanarak konuşmaları otomatikleştirin.", "Connect stores to agents.": "Mağazaları acentelere bağlayın.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Telegram botunuza gönderilen tüm mesajları doğrudan {R} üzerinden okumak ve yanıtlamak için Telegram botunuzu {R}&#39;ye bağlayın.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Viber botunuza gönderilen tüm mesajları doğrudan {R} üzerinden okumak ve yanıtlamak için Viber botunuzu {R} cihazına bağlayın.", "Content": "İçerik", "Content template SID": "İçerik şablonu SID&#39;si", "Conversation profile": "<PERSON><PERSON><PERSON><PERSON> profili", "Conversations data": "<PERSON><PERSON><PERSON><PERSON> veril<PERSON>", "Convert all emails": "Tüm e-postaları dönüştür", "Cookie domain": "Çerez alanı", "Country": "<PERSON><PERSON><PERSON>", "Coupon discount (%)": "<PERSON><PERSON><PERSON> indirimi (%)", "Coupon expiration (days)": "<PERSON><PERSON><PERSON> son kull<PERSON><PERSON> ta<PERSON>hi (gün)", "Coupon expiration (seconds)": "<PERSON><PERSON><PERSON><PERSON> sona ermesi (saniye)", "Create a WordPress user upon registration.": "<PERSON><PERSON>t sırasında bir WordPress kullanıcısı oluşturun.", "Create Intents now": "Şimdi Intents oluşturun", "Currency symbol": "Para birimi simgesi", "Custom CSS": "Özel CSS", "Custom fields": "<PERSON><PERSON>", "Custom JS": "Özel JS", "Custom model ID": "Özel model k<PERSON><PERSON><PERSON><PERSON>", "Custom parameters": "<PERSON><PERSON> parametreler", "Dashboard display": "Pano ekranı", "Dashboard title": "<PERSON><PERSON> b<PERSON>ı", "Database details": "Veritabanı ayrıntıları", "Database host": "Veritabanı ana bilgisayarı", "Database name": "<PERSON><PERSON> tabanı ismi", "Database password": "Veritabanı şifresi", "Database prefix": "Veritabanı öneki", "Database user": "Veritabanı kullanıcısı", "Decline button text": "<PERSON><PERSON> düğ<PERSON>", "Declined message": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>j", "Default": "Varsayılan", "Default body text": "Varsayılan gövde metni", "Default conversation name": "Varsayılan görüşme adı", "Default department": "Varsayılan departman", "Default department ID": "Varsayılan departman kimliği", "Default form": "Varsayılan form", "Default header text": "Varsayılan ba<PERSON><PERSON><PERSON>k metni", "Delay (ms)": "<PERSON><PERSON><PERSON><PERSON> (ms)", "Delete all leads and all messages and conversations linked to them.": "Tüm müşteri adaylarını ve bunlara bağlı tüm mesajları ve konuşmaları silin.", "Delete conversation": "Konuşmayı sil", "Delete leads": "Müşteri adaylarını sil", "Delete message": "Mesajı sil", "Delete training": "Eğitimi sil", "Delimiter": "Sınırlayıcı", "Department": "<PERSON><PERSON><PERSON>", "Department ID": "<PERSON><PERSON><PERSON>", "Departments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Departments settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Desktop notifications": "Masaüstü bildiri<PERSON>i", "Dialogflow - Department linking": "Dialogflow - <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "Dialogflow chatbot": "Diyalog akışı sohbet robotu", "Dialogflow edition": "Diyalog akışı sürümü", "Dialogflow Intent detection confidence": "Dialogflow Amaç algılama güveni", "Dialogflow location": "Diyalog akışı konumu", "Dialogflow spelling correction": "Dialogflow yazım düzeltmesi", "Dialogflow welcome Intent": "Dialogflow Hoş Geldiniz Amacı", "Disable agents check": "Aracıların kontrolünü devre dışı bırak", "Disable and hide the chat widget if all agents are offline.": "Tüm temsilciler çevrimdışıysa sohbet pencere öğesini devre dışı bırakın ve gizleyin.", "Disable and hide the chat widget outside of scheduled office hours.": "Sohbet widget&#39;ını planlanan çalışma saatleri dışında devre dışı bırakın ve gizleyin.", "Disable any features that you don't need.": "İhtiyacınız olmayan özellikleri devre dışı bırakın.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "Sohbet widget&#39;ının otomatik olarak başlatılmasını devre dışı bırakın. Bu ayar etkin olduğunda, sizin tarafınızdan yazılan özel bir JavaScript API koduyla sohbet widget&#39;ını başlatmanız gerekir. Sohbet görünmüyorsa ve bu ayar etkinse, devre dışı bırakın.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Bilet alanının otomatik olarak başlatılmasını devre dışı bırakın. Bu ayar etkin olduğunda, sizin tarafınızdan yazılan özel bir JavaScript API koduyla bilet alanını başlatmanız gerekir. Biletler alanı görünmüyorsa ve bu ayar etkinse, devre dışı bırakın.", "Disable chatbot": "Chatbot&#39;u devre dışı bırak", "Disable cron job": "Cron işini devre dışı bırak", "Disable dashboard": "Kontrol panelini devre dışı bırak", "Disable during office hours": "Çalışma saatleri içinde devre dışı bırak", "Disable email": "E-postayı devre dışı bırak", "Disable features": "Özellikleri devre dışı bırak", "Disable features you don't use and improve the chat performance.": "Kullanmadığınız özellikleri devre dışı bırakın ve sohbet performansını iyileştirin.", "Disable file uploading capabilities within the chat.": "Sohbet içinde dosya yükleme özelliklerini devre dışı bırakın.", "Disable for messaging channels": "Mesajlaşma kanalları için devre dışı bırak", "Disable for the tickets area": "<PERSON><PERSON>t alanı için devre dışı bırak", "Disable invitation": "<PERSON><PERSON> dışı bırak", "Disable outside of office hours": "Mesai saatleri dışında devre dışı bırak", "Disable password": "Şifreyi de<PERSON> dışı bırak", "Disable registration during office hours": "Mesai saatleri içinde kaydı devre dışı bırak", "Disable registration if agents online": "Aracılar çevrimiçiyse kaydı devre dışı bırakın", "Disable the automatic invitation of agents to the channels.": "Temsilcilerin kanallara otomatik davetini devre dışı bırakın.", "Disable the channels filter.": "Kanal filtresini devre dışı bırakın.", "Disable the chatbot for the tickets area.": "<PERSON><PERSON>t alanı için sohbet robotunu devre dışı bırakın.", "Disable the chatbot for this channel only.": "Chatbot&#39;u yalnızca bu kanal için devre dışı bırakın.", "Disable the dashboard, and allow only one conversation per user.": "Panoyu devre dışı bırakın ve kullanıcı başına yalnızca bir görüşmeye izin verin.", "Disable the login and remove the password field from the registration form.": "Oturum açmayı devre dışı bırakın ve kayıt formundan şifre alanını kaldırın.", "Disable uploads": "Yüklemeleri devre dışı bırak", "Disable voice message capabilities within the chat.": "Sohbet içindeki sesli mesaj özelliklerini devre dışı bırakın.", "Disable voice messages": "<PERSON><PERSON>li mesajları devre dışı bırak", "Disabled": "<PERSON><PERSON><PERSON>", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Başlık alanında bir marka resmi görüntüleyin. Bu yalnızca &#39;marka&#39; baş<PERSON>ık türü için geçerlidir.", "Display images": "Görüntüleri göster", "Display in conversation list": "Görüşme listesinde görüntüle", "Display in dashboard": "Gösterge tablosunda göster", "Display the articles section in the right area.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ünü sağ alanda gö<PERSON>ü<PERSON><PERSON>in.", "Display the dashboard instead of the chat area on initialization.": "Başlatma sı<PERSON>ında sohbet alanı yerine panoyu g<PERSON>.", "Display the user full name in the left panel instead of the conversation title.": "Sol panelde konuşma baş<PERSON>ığ<PERSON> yerine kullanıcının tam adını görüntüleyin.", "Display the user's profile image within the chat.": "Kullanıcının profil resmini sohbet içinde görüntüleyin.", "Display user name in header": "Kullanıcı adını başlıkta göster", "Display user's profile image": "Kullanıcının profil resmini <PERSON>", "Displays additional columns in the user table. Enter the name of the fields to add.": "Kullanıcı tablosunda ek sütunları görüntüler. Eklenecek alanların adını girin.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Konuşmaları aracılar arasında orantılı olarak dağıtın ve ziyaretçileri kuyruktaki konumları hakkında bilgilendirin. Yanıt süresi dakika cinsindendir. Mesajda şu birleştirme alanlarını kullanabilirsiniz: {position}, {dakika}. Gerçek zamanlı olarak gerçek değerlerle değiştirilecektir.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Konuşmaları aracılar arasında orantılı olarak dağıtın ve bir aracının diğer aracıların konuşmalarını görüntülemesini engelleyin.", "Do not send email notifications to admins": "Yöneticilere e-posta bildirimleri göndermeyin", "Do not show tickets in chat": "Biletleri sohbette gösterme", "Do not translate settings area": "<PERSON><PERSON><PERSON> alanını çevirme", "Download": "İndirmek", "Edit profile": "<PERSON><PERSON>", "Edit user": "Kullanıcıyı düzenle", "Email address": "E", "Email and ticket": "E-posta ve bilet", "Email header": "E-posta başlığı", "Email notification delay (hours)": "E-posta bildirim g<PERSON> (saat)", "Email notifications via cron job": "<PERSON>ron işi yoluyla e-posta bildirimleri", "Email only": "Yalnızca e-posta", "Email piping": "E-posta bor<PERSON>ı", "Email piping server information and more settings.": "E-posta borulama sunucusu bilgileri ve daha fazla ayar.", "Email request message": "E-posta istek mesajı", "Email signature": "E-posta imzası", "Email template for the notification email that is sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Bir aracı yanıt verdiğinde kullanıcıya gönderilen bildirim e-postası için e-posta şablonu. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Enable logging of agent activity": "Aracı etkinliğinin günlüğe kaydedilmesini etkinleştir", "Enable logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enable the chatbot outside of scheduled office hours only.": "Chatbot&#39;u yalnızca planlanan çalışma saatleri dışında etkinleştirin.", "Enable the registration only if all agents are offline.": "Kaydı yalnızca tüm aracılar çevrimdışıysa etkinleştirin.", "Enable the registration outside of scheduled office hours only.": "Kaydı yalnızca planlanan çalışma saatleri dışında etkinleştirin.", "Enable this option if email notifications are sent via cron job.": "E-posta bildirimleri cron işi aracılığıyla gönderiliyorsa bu seçeneği etkinleştirin.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Yalnızca aboneler için bilet ve sohbet desteğini etkinleştirin, yönetici alanında üye profili ayrıntılarını ve abonelik ayrıntılarını görüntüleyin.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Bot jetonunu girin ve Telegram botunu senkronize etmek için düğmeye tıklayın. Localhost mesaj alamıyor.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Bot jetonunu girin ve Viber botunu senkronize etmek için düğmeye tıklayın. Localhost mesaj alamıyor.", "Enter the database details of the Active eCommerce CMS database.": "Active eCommerce CMS veritabanının veritabanı ayrıntılarını girin.", "Enter the database details of the Martfury database.": "Martfury veritabanının veritabanı ayrıntılarını girin.", "Enter the database details of the Perfex database.": "Perfex veritabanının veritabanı ayrıntılarını girin.", "Enter the database details of the WHMCS database.": "WHMCS veritabanının veritabanı ayrıntılarını girin.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Kullanıcı sorusu dinamik bir yanıt gerektirdiğinde, sohbet robotu tarafından kullanılan varsayılan mesajları girin.", "Enter the details of your Google Business Messages.": "Google İşletme Mesajlarınızın ayrıntılarını girin.", "Enter the details of your Twitter app.": "Twitter uygulamanızın ayrıntılarını girin.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Kullanmaya başlamak için LINE ayrıntılarını girin. Localhost mesaj alamıyor.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Yönetici alanına otomatik olarak yüklemek için bir .css dosyasının URL&#39;sini girin.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Yönetici alanına otomatik olarak yüklemek için bir .js dosyasının URL&#39;sini girin.", "Enter the URL of the sources you want to train the OpenAI chatbot from.": "OpenAI sohbet robotunu eğitmek istediğiniz kaynakların URL&#39;sini girin.", "Enter the URLs of your shop": "Mağazanızın URL&#39;le<PERSON> girin", "Enter the WeChat official account token. See the docs for more details.": "WeChat resmi hesap beli<PERSON> girin. Daha fazla ayrıntı için belgelere bakın.", "Enter your 360dialog account settings information.": "360dialog hesap ayarları bilgilerinizi girin.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Otomatik güncellemeleri etkinleştirmek ve tüm özelliklerin kilidini açmak için Envato Satın Alma Kodunuzu girin.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "<PERSON><PERSON><PERSON> he<PERSON> a<PERSON>ntılarınızı girin. Metin ve şu birleştirme alanlarını kullanabilirsiniz: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "<PERSON><PERSON><PERSON>ı bilgilerinizi girin.", "Enter your WeChat Official Account information.": "WeChat Resmi He<PERSON> bilgilerinizi girin.", "Enter your Zendesk information.": "Zendesk bilgilerinizi girin.", "Entities": "Entities", "Envato Purchase Code": "<PERSON><PERSON><PERSON>", "Envato purchase code validation": "Envato satın alma kodu doğrulaması", "Exclude products": "Ürünleri hariç tut", "Export all settings.": "<PERSON><PERSON><PERSON> ayarları dışa aktarın.", "Export settings": "Dışa Aktarma Ayarları", "Facebook pages": "Facebook sayfaları", "Fallback message": "<PERSON><PERSON>", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "First chat message": "İlk sohbet mesajı", "First reminder delay (hours)": "İlk hatırlatma gecikmesi (saat)", "First ticket form": "İlk bilet formu", "Flash notifications": "<PERSON><PERSON>ş <PERSON>i", "Follow up - Email": "Takip - E-posta", "Follow up message": "Takip mesajı", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Bir insan aracı ile bir son kullanıcı arasındaki konuşmayı takip eder ve insan aracıya gerçek zamanlı olarak yanıt önerileri sağlar.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Takip e-posta şablonu. Metin, HTML ve aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {coupon}, {product_names}, {user_name}.", "Force language": "<PERSON><PERSON>", "Force log out": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>la", "Force the chat to ignore the language preferences, and to use always the same language.": "<PERSON><PERSON><PERSON><PERSON> dil tercihlerini yok saymaya ve her zaman aynı dili kullanmaya zorlayın.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "WordPress&#39;te oturum açmamışlarsa Support Board aracılarının oturumunu kapatmaya zorlayın.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her mağaza için farklı bir konuşma kullanmaya zorlayın ve diğer mağazalardaki konuşmaları mağaza yöneticilerinden gizleyin.", "Force users to use only one phone country code.": "Kullanıcıları yalnızca bir telefon ülke kodu kullanmaya zorlayın.", "Form message": "Form mesajı", "Form title": "Form başlığı", "Frequency penalty": "Frekans cezası", "Full visitor details": "Tam ziyaretçi ayrıntıları", "Generate conversations data": "G<PERSON>r<PERSON>şme ve<PERSON>", "Generate user expressions": "Kullanıcı ifadeleri oluştur", "Get configuration URL": "Yapılandırma URL&#39;sini al", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Active eCommerce kök dizininde bulunan .env dosyasının APP_KEY değerinden alın.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Martfury kök dizininde bulunan .env dosyasının APP_KEY değerinden alın.", "Get Path": "Yol Al", "Get Service Worker path": "Service Worker yolu<PERSON> al", "Get URL": "URL&#39;yi al", "Google and Dialogflow settings.": "Google ve Dialogflow ayarları.", "Google search": "Google arama", "Header background image": "Başlık arka plan resmi", "Header brand image": "Başlık marka resmi", "Header message": "Başlık mesajı", "Header title": "Üst Başlık", "Header type": "Başlık türü", "Hide": "Saklamak", "Hide agent's profile image": "Temsilcinin profil resmini gizle", "Hide archived tickets": "Arşivlenmiş biletleri gizle", "Hide archived tickets from users.": "Arşivlenmiş biletleri kullanıcılardan gizleyin.", "Hide chat if no agents online": "Çevrimiçi ajan yoksa sohbeti gizle", "Hide chat outside of office hours": "Çalışma saatleri dışında sohbeti gizle", "Hide conversations of other agents": "<PERSON><PERSON>er temsilcilerin konuşmalarını gizle", "Hide on mobile": "Cep telefonunda gizle", "Hide the agent's profile image within the chat.": "Temsilcinin profil resmini sohbet içinde gizleyin.", "Hide tickets from the chat widget and chats from the ticket area.": "Biletleri sohbet widget&#39;ından ve sohbetleri bilet alanından gizleyin.", "Hide timetable": "Zaman çizelgesini gizle", "Host": "<PERSON>v sahibi", "Human takeover": "<PERSON>nsan ele geçirme", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Belirtilen zaman aralığında hiçbir temsilci yanıt vermezse, kullanıcının e-posta adresi gibi ayrıntılarını talep eden bir mesaj gönderilecektir.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Chatbot bir kullanı<PERSON>ı<PERSON>ın so<PERSON>, görüşmeyi bir temsilciye iletir.", "Image": "Resim", "Import admins": "Yöneticileri içe aktar", "Import all settings.": "Tüm ayarları içe aktarın.", "Import articles": "Makaleleri içe aktar", "Import contacts": "Kişileri içe aktar", "Import customers": "Müşterileri içe aktar", "Import customers into Support Board. Only new customers will be imported.": "Müşterileri Support Board içine aktarın. Yalnızca yeni müşteriler içe aktarılacaktır.", "Import settings": "Ayarları içe aktar", "Import users": "Kullanıcıları içe aktar", "Import vendors": "Satıcıları içe aktar", "Import vendors into Support Board as agents. Only new vendors will be imported.": "Satıcıları aracı olarak Support Board içine aktarın. Yalnızca yeni satıcılar içe aktarılacaktır.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Pusher ve WebSockets ile sohbet performansını iyileştirin. <PERSON><PERSON> ayar, sunucunuzu yavaşlatan ve bunun yerine WebSockets&#39;i kullanan tüm AJAX/HTTP gerçek zamanlı isteklerini durdurur.", "Include custom fields": "<PERSON><PERSON> da<PERSON> et", "Include custom fields in the registration form.": "Kayıt formuna özel alanlar ekleyin.", "Include the password field in the registration form.": "Kayıt formuna şifre alanını ekleyin.", "Incoming conversations and messages": "<PERSON><PERSON><PERSON> k<PERSON> ve mesajlar", "Incoming conversations only": "Yalnızca gelen konuşmalar", "Incoming messages only": "Yalnızca gelen iletiler", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Support Board ile Active eCommerce&#39;i entegre ederek satışları artırın ve sizi ve satıcıları gerçek zamanlı olarak müşterilerle buluşturun.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "WooCommerce&#39;i Destek Kurulu ile entegre ederek satışları artırın, daha iyi destek ve daha hızlı çözümler sağlayın.", "Info message": "<PERSON><PERSON><PERSON> mesa<PERSON>ı", "Initialize and display the chat widget and tickets only for members.": "So<PERSON>bet pencere aracını ve biletleri yalnızca üyeler için başlatın ve görüntüleyin.", "Initialize and display the chat widget only when the user is logged in.": "Sohbet widget&#39;ını yalnızca kullanıcı oturum açtığında başlatın ve görüntüleyin.", "Instance ID": "Örnek Kimliği", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Müşterilerin gerçek zamanlı senkronizasyonu, sipariş geçmişi erişimi ve müşteri sepeti görünürlüğü için OpenCart&#39;ı {R} ile entegre edin.", "Interval (sec)": "Aralık (sn)", "Label": "Etiket", "Language": "<PERSON><PERSON>", "Language detection": "<PERSON><PERSON>gılama", "Language detection message": "Dil algılama mesajı", "Last name": "Soyadı", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Bu ayarın ne olduğunu bilmiyorsanız boş bırakın! Yanlış bir değer girmek sohbeti bozar. Ana etki alanı ve alt etki alanları arasında oturum açma ve konuşma paylaşımını etkinleştirmek için sohbetin kullanıldığı ana etki alanını ayarlar.", "Left": "Sol", "Left panel": "Sol panel", "Left profile image": "Sol profil resmi", "Let the bot search on Google to find answers to user questions.": "Kullanıcı sorularının yanıtlarını bulmak için botun Google&#39;da arama yapmasına izin verin.", "Let the bot to search on Google to find answers to user questions.": "Kullanıcı sorularına yanıt bulmak için botun Google&#39;da arama yapmasına izin verin.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Kullanıcılarınızın size Twitter üzerinden ulaşmasını sağlar. Twitter hesabınıza doğrudan {R} adresinden gönderilen mesajları okuyun ve yanıtlayın.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Kullanıcılarınızın WeChat aracılığıyla size ulaşmasını sağlar. Resmi WeChat hesabınıza gönderilen tüm mesajları doğrudan {R} üzerinden okuyun ve yanıtlayın.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Kullanıcılarınızın size WhatsApp üzerinden ulaşmasını sağlar. <PERSON><PERSON><PERSON><PERSON> {R} üzerinden WhatsApp Business hesabınıza gönderilen tüm mesajları okuyun ve yanıtlayın.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "Her aracıyı ilgili Slack kullanıcısıyla ilişkilendirin, bö<PERSON>ce bir aracı Slack aracılığıyla yanıt verdiğinde atanan aracı olarak görüntülenecektir.", "Link name": "Bağlantı adı", "Login form": "<PERSON><PERSON><PERSON>", "Login initialization": "<PERSON><PERSON><PERSON><PERSON>", "Login verification URL": "Giriş doğrulama URL&#39;si", "Logit bias": "Logit yanlılığı", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Önce Dialogflow aracınızın yedeğini alın. Bu işlem birkaç dakika sürebilir.", "Make the registration phone field mandatory.": "Kayıt telefonu alanını zorunlu hale getirin.", "Manage": "Üstesinden gelmek", "Manage here the departments settings.": "<PERSON><PERSON><PERSON> ayarlarını buradan yö<PERSON>in.", "Manage personalized questions and answers.": "Kişiselleştirilmiş soruları ve yanıtları yönetin.", "Manage the tags settings.": "Etiket ayarlarını yönetin.", "Manifest file URL": "Manifest dosya URL&#39;si", "Manual": "<PERSON>", "Manual initialization": "<PERSON>", "Martfury root directory path, e.g. /var/www/": "Martfury kök dizini yolu, örneğin /var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury mağaza URL&#39;<PERSON>, <PERSON><PERSON><PERSON><PERSON> https://shop.com", "Max message limit": "<PERSON><PERSON><PERSON><PERSON> mesaj sınırı", "Max tokens": "<PERSON><PERSON><PERSON><PERSON> jeton", "Members only": "<PERSON><PERSON><PERSON>", "Members with an active paid plan only": "Yalnızca aktif ücretli plana sahip üyeler", "Message": "İleti", "Message rewrite button": "<PERSON><PERSON> ya<PERSON>", "Message template": "<PERSON><PERSON>", "Message type": "<PERSON><PERSON> tipi", "Messaging channels": "Mesajlaşma kanalları", "Messenger and Instagram settings": "Messenger ve Instagram ayarları", "Minify JS": "JS&#39;y<PERSON>", "Minimal": "En az", "Model": "<PERSON><PERSON>", "Multilingual": "Çok <PERSON>lli", "Multilingual plugin": "Çok dilli eklenti", "Multilingual via translation": "<PERSON><PERSON><PERSON> yo<PERSON>yla <PERSON>", "Multlilingual training sources": "Çok dilli eğitim kaynakları", "Name": "İsim", "Namespace": "Ad alanı", "New conversation email": "Yeni görüşme e-postası", "New conversation notification": "<PERSON><PERSON> k<PERSON> bi<PERSON>", "New ticket button": "Yeni bilet düğmesi", "Newsletter": "<PERSON><PERSON><PERSON>", "No delay": "Gecikme yok", "No results found.": "<PERSON><PERSON><PERSON> bulunamadı.", "No, we don't ship in": "Hayır, göndermiyoruz", "None": "Hiç<PERSON>i", "Note data scraping": "Veri kazımaya dikkat edin", "Notes": "Notlar", "Notifications icon": "<PERSON>il<PERSON><PERSON><PERSON> simgesi", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Mesajı planlanan çalışma saatleri dışında gönderildiğinde veya tüm temsilciler çevrimdışı olduğunda kullanıcıya bilgi verin.", "Offline message": "Çevrimdışı mesaj", "Offset": "Telafi etmek", "Omit previous messages": "Önceki mesajları atla", "On chat open": "<PERSON><PERSON><PERSON><PERSON> açık", "On page load": "<PERSON><PERSON> yü<PERSON>ndiğinde", "One conversation per agent": "Temsilci başına bir gö<PERSON>üşme", "One conversation per department": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>na bir gö<PERSON><PERSON>şme", "Online users notification": "Çevrimiçi kullanıcı bildirimi", "Only desktop": "<PERSON><PERSON><PERSON>", "Only general questions": "<PERSON><PERSON><PERSON> genel sorular", "Only mobile devices": "Yalnızca mobil cihazlar", "Only questions related to your sources": "Yalnızca kaynaklarınızla ilgili sorular", "Open automatically": "Otomatik olarak aç", "Open chat": "Açık <PERSON>", "Open the chat window automatically when a new message is received.": "Yeni bir mesaj <PERSON>ında sohbet penceresini otomatik olarak açın.", "OpenAI - Questions and answers": "OpenAI - <PERSON><PERSON><PERSON> ve cevaplar", "OpenAI - Training sources": "OpenAI - Eğitim kaynakları", "OpenAI - Training sources - PDF and Text Files": "OpenAI - Eğitim kaynakları - PDF ve Metin Do<PERSON>aları", "OpenAI - Training sources options": "OpenAI - Eğitim kaynakları seçenekleri", "OpenAI Assistants - Department linking": "OpenAI Asistanları - <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "OpenAI settings.": "AI ayarlarını açın.", "Optional link": "İsteğe bağlı bağlantı", "Optional registration fields": "İsteğe bağlı kayıt alanları", "Options associated with the training procedure.": "Eğitim prosed<PERSON><PERSON><PERSON><PERSON>.", "Order webhook": "Web kancası sipariş et", "Other": "<PERSON><PERSON><PERSON>", "Outgoing SMTP server information.": "Giden SMTP sunucu bilgileri.", "Page ID": "<PERSON><PERSON> k<PERSON>", "Page IDs": "<PERSON><PERSON>", "Page name": "<PERSON><PERSON>", "Page token": "Say<PERSON> jetonu", "Panel height": "Panel yüksekliği", "Panel name": "Panel adı", "Panel title": "Panel başlığı", "Panels arrows": "<PERSON>ler okları", "Password": "Pa<PERSON><PERSON>", "Perfex URL": "Mükemmel URL", "Performance optimization": "<PERSON><PERSON><PERSON>", "Phone": "Telefon", "Phone codes": "Telefon kodları", "Phone number ID": "Telefon numarası kimliği", "Phone required": "Telefon gerekli", "Place ID": "<PERSON><PERSON>", "Placeholder text": "Yer tutucu metin", "Play a sound for new messages and conversations.": "<PERSON>ni mesajlar ve konuşmalar için ses çalın.", "Popup message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mesaj", "Port": "Liman", "Post Type slugs": "Post Type sümüklü böcek", "Presence penalty": "Varlık cezası", "Prevent admins from receiving email notifications.": "Yöneticilerin e-posta bildirimleri almasını engelleyin.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Aracıların diğer aracılara atanan konuşmaları görüntülemesini önleyin. Yönlendirme veya kuyruk etkinse bu ayar otomatik olarak etkinleştirilir.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "Tek bir cihazdan chatbot&#39;a gönderilen mesajların sayısını sınırlayarak kullanıcıların kötüye kullanmasını önleyin.", "Primary color": "<PERSON>nk", "Priority": "Öncelik", "Privacy link": "Gizlilik bağlantısı", "Privacy message": "Gizlilik mesajı", "Private chat": "<PERSON><PERSON>", "Private chat linking": "Özel sohbet bağlantısı", "Private key": "<PERSON><PERSON>", "Product IDs": "<PERSON><PERSON><PERSON><PERSON> kim<PERSON>i", "Product removed notification": "<PERSON><PERSON><PERSON>n kaldırıldı bildirimi", "Product removed notification - Email": "Ürün kaldırıldı bildirimi - E-posta", "Profile image": "<PERSON>il resmi", "Project ID": "<PERSON><PERSON>", "Project ID or Agent Name": "<PERSON>je <PERSON> veya Aracı Adı", "Prompt": "Çabuk", "Prompt - Message rewriting": "Bilgi İstemi - <PERSON><PERSON><PERSON>n yeniden yazılması", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Bilet alanını Google reCAPTCHA ile spam ve kötüye kullanımdan koruyun.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Herhangi bir web sayfasına saniyeler içinde tüm sohbet özelliklerinin dahil olduğu bir bilet alanı ekleyerek müşterilerinize yardım masası desteği sağlayın.", "Provider": "Sağlayıcı", "Purchase button text": "Satın alma düğmesi metni", "Push notifications": "<PERSON><PERSON>", "Push notifications settings.": "An<PERSON>nda bildirim a<PERSON>.", "Questions and answers": "Sorular ve cevaplar", "Queue": "<PERSON><PERSON><PERSON>", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Google Arama, <PERSON><PERSON>ar ve markaya ait kanallardan gönderilen mesajları doğrudan {R} üzerinden okuyun ve yanıtlayın.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "Facebook sayfalarınıza ve Instagram hesaplarınıza gönderilen tüm mesajları doğ<PERSON>an {R} üzerinden okuyun, yönetin ve yanıtlayın.", "Reconnect": "<PERSON><PERSON><PERSON> ba<PERSON>lan", "Redirect the user to the registration link instead of showing the registration form.": "Kullanıcıyı kayıt formunu göstermek yerine kayıt bağlantısına yönlendirin.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "<PERSON><PERSON><PERSON> gerekliyse ve kullanıcı oturum açmadıysa, kullanıcıyı belirtilen URL&#39;ye yönlendirin. Varsayılan kayıt formunu kullanmak için boş bırakın.", "Register all visitors": "<PERSON><PERSON><PERSON> z<PERSON>çileri kaydet", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Tüm ziyaretçileri otomatik olarak kaydedin. Bu seçenek aktif olmadığında sadece sohbet başlatan ziyaretçiler kaydedilecektir.", "Registration / Login": "Kayıt / Giriş", "Registration and login form": "Kay<PERSON>t ve giriş formu", "Registration form": "<PERSON>ıt formu", "Registration link": "<PERSON><PERSON><PERSON> bağlantısı", "Registration redirect": "<PERSON><PERSON><PERSON> yö<PERSON>dirmesi", "Remove the email field from the registration form.": "E-posta alanını kayıt formundan kaldırın.", "Rename the chat bot. Default is 'Bot'.": "<PERSON><PERSON><PERSON> botunu yeniden adlandırın. Varsayılan &#39;Bot&#39;tur.", "Rename the visitor name prefix. Default is 'User'.": "Ziyaretçi adı önekini yeniden adlandırın. Varsayılan &#39;Kullanıcı&#39;dır.", "Repeat": "<PERSON><PERSON><PERSON> et", "Repeat - admin": "Tekrar - yönetici", "Replace the admin login page message.": "Yönetici oturum açma sayfası mesajını değiştirin.", "Replace the brand logo on the admin login page.": "Yönetici oturum açma sayfasındaki marka logosunu <PERSON>tirin.", "Replace the header title with the user's first name and last name when available.": "Başlık ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, varsa kullanı<PERSON>ının adı ve soyadıyla değiştirin.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Yönetici alanındaki sol üstteki marka simgesini ve tarayıcı favicon&#39;unu değiştirin.", "Reply to user emails": "Kullanıcı e-postalarını yanıtlayın", "Reply to user text messages": "Kullanıcı metin mesajlarını yanıtlayın", "Reports": "<PERSON><PERSON><PERSON>", "Reports area": "<PERSON><PERSON><PERSON> alanı", "Request a valid Envato purchase code for registration.": "<PERSON><PERSON><PERSON> için geçerli bir Envato satın alma kodu isteyin.", "Request the user to provide their email address and then send a confirmation email to the user.": "Kullanıcıdan e-posta adresini vermesini isteyin ve ardından kullanıcıya bir onay e-postası gönderin.", "Require phone": "Telefon gerektir", "Require registration": "Kayıt gerektir", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "Bir sohbete başlamadan önce kullanıcı kaydını veya oturum açmasını isteyin. Oturum açma alanını etkinleştirmek için parola alanı dahil edilmelidir.", "Require the user registration or login in order to use the tickets area.": "Bilet alanını kullanmak için kullanıcı kaydı veya oturum açmayı zorunlu kılın.", "Required": "<PERSON><PERSON><PERSON><PERSON>", "Response time": "<PERSON><PERSON><PERSON>", "Returning visitor message": "Geri dönen ziyaretçi mesajı", "Rich messages": "<PERSON><PERSON> me<PERSON>", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Zengin mesajlar, bir sohbet mesajında kullanılabilecek kod parçacıklarıdır. HTML kodu içerebilirler ve sohbette otomatik olarak oluşturulurlar. Zengin mesajlar şu sözdizimiyle kullanılabilir: [zengin-mesaj-adı]. Aralarından seçim yapabileceğiniz bir ton yerleşik zengin mesaj var.", "Right": "Do<PERSON><PERSON>", "Right panel": "Sağ panel", "Routing": "Yönlendirme", "Routing if offline": "Çevrimdışıysa yönlendirme", "RTL": "<PERSON><PERSON><PERSON>la", "Save useful information like user country and language also for visitors.": "Kullanıcının ülkesi ve dili gibi faydalı bilgileri ziyaretçiler için de kaydedin.", "Saved replies": "Kayıtlı yanıtlar", "Scheduled office hours": "Planlanmış çalışma saatleri", "Search engine ID": "Arama motor<PERSON> k<PERSON>", "Second chat message": "<PERSON><PERSON><PERSON> sohbet mesajı", "Second reminder delay (hours)": "İkinci hatırlatma gecikmesi (saat)", "Secondary color": "<PERSON><PERSON><PERSON><PERSON> renk", "Secret key": "<PERSON><PERSON><PERSON>", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "Müşterilerin <PERSON>, an<PERSON><PERSON> anda stokta olmayan bir ürünü satın alabilecekleri zaman bilgilendirilmeleri için bir mesaj gönderin. Aşağıdaki birleştirme alanlarını kullanabilirsiniz: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "İlk bileti oluşturduklarında yeni kullanıcılara bir mesaj gönderin. Metin biçimlendirme ve birleştirme alanları desteklenir.", "Send a message to new users when they visit the website for the first time.": "Yeni kullanıcılara web sitesini ilk kez ziyaret ettiklerinde bir mesaj gönderin.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "Bir ürün sepetten çıkarıldıktan sonra müşteriye mesaj gönderin. Aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "Satın alma işlemini tamamlayan müşterilere, yeni satın aldıkları ürünü paylaşmalarını isteyen bir mesaj gönderin. Aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "<PERSON>ir satın alma işlemini tamamlayan müşterilere bir mesaj gönder<PERSON>. Aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "Aracı konuşmayı arşivlediğinde kullanıcıya bir mesaj g<PERSON>.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "En az 24 saat sonra web sitesini tekrar ziyaret eden kullanıcılara mesaj gönderin. Aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {coupon}, {user_name}. Daha fazla ayrıntı için belgelere bakın.", "Send a test agent notification email to verify email settings.": "E-posta ayarlarını doğrulamak için bir test aracısı bildirim e-postası gönderin.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Slack kanalınıza bir test mesajı gönderin. Bu, yaln<PERSON><PERSON><PERSON> giden mesajların gönderme işlevini test eder.", "Send a test user notification email to verify email settings.": "E-posta ayarlarını doğrulamak için bir test kullanıcısı bildirim e-postası gönderin.", "Send a text message to the provided phone number.": "Belirtilen telefon numarasına kısa mesaj g<PERSON>.", "Send a user email notification": "Kullanıcı e-posta bildirimi gönder", "Send a user text message notifcation": "Kullanıcıya kısa mesaj bildiri<PERSON> g<PERSON>nder", "Send a user text message notification": "Kullanıcıya kısa mesaj bildiri<PERSON> g<PERSON>nder", "Send an agent email notification": "Temsilci e-posta bildirimi g<PERSON>", "Send an agent text message notification": "Temsilciye kısa mesaj bildiri<PERSON>", "Send an agent user text notification": "Bir aracı kullanıcıya metin bildirimi gö<PERSON>in", "Send an email notification to the provided email address.": "Sağlanan e-posta adresine bir e-posta bildirimi <PERSON>.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "Bir kullanıcı yanıt verdiğinde ve aracı çevrimdışı olduğunda bir aracıya e-posta gönderin. Yeni konuşmalar için tüm aracılara otomatik olarak bir e-posta gönderilir.", "Send an email to the user when a new conversation is created.": "Yeni bir görüşme oluşturulduğunda kullanıcıya bir e-posta gönderin.", "Send an email to the user when a new conversation or ticket is created": "Yeni bir konuşma veya bilet oluşturulduğunda kullanıcıya bir e-posta gönderin", "Send an email to the user when an agent replies and the user is offline.": "Bir aracı yanıt verdiğinde ve kullanıcı çevrimdışı olduğunda kullanıcıya bir e-posta gönderin.", "Send email": "<PERSON><PERSON><PERSON> g<PERSON>", "Send login details to the specified URL and allow access only if the response is positive.": "Oturum açma ayrıntılarını belirtilen URL&#39;ye gönderin ve yalnızca yanıt olumluysa erişime izin verin.", "Send message": "<PERSON><PERSON>", "Send message to Slack": "Slack&#39;e mesaj gönder", "Send message via enter button": "<PERSON>ter butonu ile mesaj gönder", "Send text message": "<PERSON><PERSON><PERSON> mesaj g<PERSON>", "Send the message template to a WhatsApp number.": "<PERSON>j <PERSON> bir WhatsApp numarasına g<PERSON>.", "Send the message via the ENTER keyboard button.": "Mesajı ENTER klavye düğmesi ile gönderin.", "Send the user details of the registration form and email rich messages to Dialogflow.": "<PERSON><PERSON>t formunun kullanıcı ayrıntılarını ve zengin e-posta mesajlarını Dialogflow&#39;a g<PERSON><PERSON><PERSON>.", "Send the WhatsApp order details to the URL provided.": "WhatsApp sipariş ayrıntılarını sağlanan URL&#39;ye g<PERSON><PERSON>.", "Send to user's email": "Kullanıcının e-postasına gönder", "Send transcript to user's email": "Kullanıcının e-postasına transkript gönder", "Send user details": "Kullanıcı ayrıntılarını gönder", "Sender": "<PERSON><PERSON><PERSON><PERSON>", "Sender email": "E-mail göndermek", "Sender name": "<PERSON><PERSON><PERSON><PERSON> ismi", "Sender number": "<PERSON><PERSON><PERSON><PERSON> numa<PERSON>", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "WhatsApp mesajının gönderilmesi başarısız olursa bir metin mesajı gönderir. Metni ve aşağıdaki birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "WhatsApp mesajının gönderilmesi başarısız olursa bir WhatsApp Şablonu bildirimi gönderir. Metni ve aşağıdaki birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "Hizmet", "Service Worker path": "Service Worker yolu", "Service Worker URL": "Service Worker URL&#39;si", "Set a dedicated Dialogflow agent for each department.": "Her departman i<PERSON><PERSON> özel bir Dialogflow aracısı ayarlayın.", "Set a dedicated OpenAI Assistants for each department.": "Her departman i<PERSON><PERSON> bir OpenAI Asistanı ayarlayın.", "Set a dedicated Slack channel for each department.": "Her departman i<PERSON><PERSON> bir Slack kanalı ayarlayın.", "Set a profile image for the chat bot.": "<PERSON><PERSON><PERSON> botu için bir profil resmi <PERSON>.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Mesajın yanında gösterilen avatar resmini ayarlayın. 1024x1024px boyutunda ve maksimum 50KB boyutunda bir JPG görüntüsü olmalıdır.", "Set the currency symbol of the membership prices.": "Üyelik fiyatlarının para birimi sembolünü ayarlayın.", "Set the currency symbol used by your system.": "Sisteminiz tarafından kullanılan para birimi simgesini ayarlayın.", "Set the default departments for all tickets. Enter the department ID.": "Tüm biletler için var<PERSON>ılan departmanları ayarlayın. <PERSON><PERSON><PERSON> kimliğini girin.", "Set the default email header that will be prepended to automated emails and direct emails.": "Otomatik e-postalara ve doğrudan e-postalara eklenecek varsayılan e-posta başlığını ayarlayın.", "Set the default email signature that will be appended to automated emails and direct emails.": "Otomatik e-postalara ve doğrudan e-postalara eklenecek varsayılan e-posta imzasını ayarlayın.", "Set the default form to display if the registraion is required.": "<PERSON><PERSON>t gerekliyse görüntülenecek varsayılan formu ayarlayın.", "Set the default name to use for conversations without a name.": "Adsız konuşmalar için kullanılacak varsayılan adı ayarlayın.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Varsayılan bildirimler simgesini ayarlayın. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yo<PERSON>a, simge profil resmi olarak kullanılacaktır.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Temsilcilerin müsait olarak gösterildiği durumlar için varsayılan ofis saatlerini ayarlayın. <PERSON><PERSON>, çalışma saatlerine dayanan diğer tüm ayarlar için de kullanılır.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Kullanıcının bir adı olmadığında bot mesajlarında ve e-postalarda kullanılacak varsayılan kullanıcı adını ayarlayın.", "Set the header appearance.": "Başlık görünümünü ayarlayın.", "Set the maximum height of the tickets panel.": "Bilet panelinin maksimum yüksekliğini ayarlayın.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "Kullanmakta olduğunuz çok dilli eklentiyi ayarlayın veya siteniz yalnızca bir dil kullanıyorsa devre dışı bırakın.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Temsilci veya yönetici, yönetici alanında en az 10 dakika boyunca etkin olmadığında çevrimdışı durumu otomatik olarak ayarlayın.", "Set the position of the chat widget.": "<PERSON><PERSON><PERSON> widget&#39;ının konumunu a<PERSON>n.", "Set the primary color of the admin area.": "Yönetici alanının ana rengini ayarlayın.", "Set the primary color of the chat widget.": "<PERSON><PERSON><PERSON> widget&#39;ının ana reng<PERSON> a<PERSON>.", "Set the secondary color of the admin area.": "Yönetici alanının ikincil rengini ayarlayın.", "Set the secondary color of the chat widget.": "<PERSON><PERSON><PERSON> widget&#39;ının ikincil reng<PERSON> a<PERSON>.", "Set the tertiary color of the chat widget.": "Sohbet widget&#39;ının <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reng<PERSON> a<PERSON>ı<PERSON>.", "Set the title of the administration area.": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>ı<PERSON> başlığını ayarlayın.", "Set the title of the conversations panel.": "Görüşmeler panelinin başlığını ayarlayın.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Ofis saatleri zaman çizelgesinin UTC farkını ayarlayın. Doğru değer negatif olabilir ve boşsa bu giriş alanını tıkladığınızda otomatik olarak oluşturulur.", "Set which actions to allow agents.": "Aracılara izin verilecek eylemleri ayarlayın.", "Set which actions to allow supervisors.": "Gözetmenlere hangi eylemlere izin verileceğini ayarlayın.", "Set which user details to send to the main channel. Add comma separated values.": "Ana kanala hangi kullanıcı ayrıntılarının gönderileceğini ayarlayın. Virgülle ayrılmış değerler ekleyin.", "Settings area": "<PERSON><PERSON><PERSON> alanı", "settings information": "a<PERSON> bi<PERSON>i", "Shop": "Mağaza", "Show": "Göstermek", "Show a browser tab notification when a new message is received.": "Yeni bir mesaj <PERSON>ında bir tarayıcı sekmesi bildirimi göster.", "Show a desktop notification when a new message is received.": "Yeni bir mesaj <PERSON>ında bir masaüstü bildirimi gö<PERSON>.", "Show a notification and play a sound when a new user is online.": "Yeni bir kullanıcı çevrimiçi olduğunda bir bildirim gösterin ve bir ses çalın.", "Show a pop-up notification to all users.": "<PERSON><PERSON><PERSON> k<PERSON>anıcılara açılır bir bildirim gösterin.", "Show profile images": "<PERSON><PERSON>", "Show sender's name": "Gönderenin adını göster", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Gösterge tablosunda aracılar menüsünü gösterin ve kullanıcıyı bir görüşme başlatmak için bir aracı seçmeye zorlayın.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "Bir ziyaretçi sepete bir ürün eklediğinde takip mesajını gösterin. Mesaj, yaln<PERSON>zca kullanıcı henüz bir e-posta sağlamadıysa gönderilir.", "Show the list of all Slack channels.": "<PERSON><PERSON>m Slack kanallarının listesini göster.", "Show the profile image of agents and users within the conversation.": "Görüşme içindeki aracıların ve kullanıcıların profil resmini gösterin.", "Show the sender's name in every message.": "Her mesajda gönderenin adını gösterin.", "Single label": "Tek etiket", "Single phone country code": "Tek telefon ülke kodu", "Site key": "Site anahtarı", "Slug": "Sümü<PERSON><PERSON><PERSON> bö<PERSON>k", "Smart Reply": "Akıllı Yanıt", "Social share message": "<PERSON><PERSON><PERSON>ım mesajı", "Sort conversations by date": "Konuşmaları tarihe göre sırala", "Sound": "Ses", "Sound settings": "<PERSON><PERSON>", "Sounds": "<PERSON>sler", "Sounds - admin": "Sesler - yönetici", "Source links": "Kaynak bağlantıları", "Speech recognition": "<PERSON><PERSON><PERSON><PERSON>", "Spelling correction": "<PERSON><PERSON><PERSON><PERSON>", "Start importing": "İçe aktarmaya başla", "Stop crawler from scanning other pages": "Tarayıcının diğer sayfaları taramasını durdurun", "Store name": "<PERSON><PERSON><PERSON>n adı", "Subject": "<PERSON><PERSON>", "Subscribe": "<PERSON><PERSON>", "Subscribe message": "<PERSON><PERSON> ol mesajı", "Subscribe message - Email": "<PERSON><PERSON> ol mesajı - E-posta", "Subscribe message email template sent to the user when the user communicate the email via the subscribe message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "Kullanıcı e-postayı abone mesaj formu aracılığıyla ilettiğinde kullanıcıya gönderilen abone mesajı e-posta şablonu. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {user_name}, {user_email}.", "Subscribe users to your preferred newsletter service when they provide an email.": "Kullanıcılar bir e-posta sağladıklarında tercih ettiğiniz haber bülteni hizmetine abone olun.", "Subtract the offset value from the height value.": "Ofset değerini yükseklik değerinden çıkarın.", "Success message": "Başarı mesajı", "Supervisor": "Süpervizör", "Support Board path": "Destek <PERSON> yolu", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Yönetici ve personel hesaplarını Destek Kurulu ile senkronize edin. Yöneticiler yönetici olarak, personel kullanıcıları aracı olarak kaydedilecektir. Yalnızca yeni kullanıcılar içe aktarılacaktır.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "Destek Panosu ile tüm istemcilerin tüm kişilerini senkronize edin. Yalnızca yeni kişiler içe aktarılacaktır.", "Sync all users with Support Board. Only new users will be imported.": "Tüm kullanıcıları Destek Panosu ile senkronize edin. Yalnızca yeni kullanıcılar içe aktarılacaktır.", "Sync all WordPress users with Support Board. Only new users will be imported.": "Tüm WordPress kullanıcılarını Destek Kurulu ile senkronize edin. Yalnızca yeni kullanıcılar içe aktarılacaktır.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "Bilgi bankası makalelerini Destek Kurulu ile senkronize edin. Yalnızca yeni makaleler içe aktarılacaktır.", "Sync mode": "Senkronizasyon modu", "Synchronization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Synchronize": "Senkronize et", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "Müşterileri senkronize edin, yaln<PERSON><PERSON><PERSON> aboneler için bilet ve sohbet desteğini etkinleştirin, yönetici alanında abonelik planlarını görüntüleyin.", "Synchronize emails": "E-postaları senkronize et", "Synchronize Entities": "Senkronize et Entities", "Synchronize Entities now": "Entities şimdi senkronize et", "Synchronize now": "Şimdi senkronize et", "Synchronize users": "Kullanıcıları senkronize et", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Müşterilerinizi gerçek zamanlı olarak senkronize edin, onlarla sohbet edin ve etkileşimlerini artırın veya daha iyi ve daha hızlı destek sağlayın.", "Synchronize your Messenger and Instagram accounts.": "Messenger ve Instagram hesaplarınızı senkronize edin.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Perfex müşterilerinizi gerçek zamanlı olarak senkronize edin ve sohbet yoluyla sizinle iletişim kurmalarına izin verin! Profil ayrıntılarını görünt<PERSON><PERSON>in, onlarla proaktif olarak etkileşim kurun ve daha fazlasını yapın.", "Synchronize your WhatsApp Cloud API account.": "WhatsApp Bulut API hesabınızı senkronize edin.", "System requirements": "Sistem gereksinimleri", "Tags": "<PERSON><PERSON><PERSON><PERSON>", "Tags settings": "Etiket ayarları", "Template default language": "<PERSON><PERSON><PERSON>li", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Bir aracı yanıt verdiğinde kullanıcıya gönderilen e-posta için şablon. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Bir kullanıcı yeni bir mesaj gönderdiğinde bir aracıya gönderilen e-posta için şablon. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Yeni bir görüşme oluşturulduğunda kullanıcıya gönderilen e-posta şablonu. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "Yeni bir konuşma veya bilet oluşturulduğunda kullanıcıya gönderilen e-posta için şablon. Metin, HTML ve şu birleştirme alanlarını kullanabilirsiniz: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Şablon di<PERSON>i", "Template name": "Şablon adı", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "Yönetici bildirim e-postasının şablonu. <PERSON>in, HTML ve aşağıdaki birleştirme alanını ve daha fazlasını kullanabilirsiniz: {carts}. E-posta adresi alanına bildirim göndermek istediğiniz e-postayı girin.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Bir ürün sepetten çıkarıldıktan sonra müşteriye gönderilen e-postanın şablonu. Metin, HTML ve aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "İlk bildirim e-postasının şablonu. Metin, HTML ve aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "İkinci bildirim e-postasının şablonu. Metin, HTML ve aşağıdaki birleştirme alanlarını ve daha fazlasını kullanabilirsiniz: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "Bekleme listesi bildirim e-postasının şablonu. Metin, HTML ve aşağıdaki birleştirme alanını ve daha fazlasını kullanabilirsiniz: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Şartlar bağlantısı", "Tertiary color": "Üçüncül renk", "Test Slack": "Gevşeklik Testi", "Test template": "Test şablonu", "Text": "<PERSON><PERSON>", "Text message fallback": "<PERSON><PERSON><PERSON> mesaj <PERSON>", "Text message notifications": "<PERSON><PERSON><PERSON> mesaj <PERSON>", "Text messages": "<PERSON><PERSON>", "The product is not in the cart.": "<PERSON><PERSON><PERSON><PERSON> sepette yok.", "The workspace name you are using to synchronize Slack.": "Slack&#39;i senkronize etmek için kullandığınız workspace adı.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "<PERSON><PERSON>, genellikle #general kanal<PERSON> olan ana Slack kanal kimliğinizdir. Slack senkronizasyonunu tamamlayarak bu kodu alacaksınız.", "This returns the Support Board path of your server.": "<PERSON><PERSON>, sunucunuzun Support Board yolu<PERSON> dö<PERSON>ü<PERSON>ü<PERSON>.", "This returns your Support Board URL.": "<PERSON><PERSON>, Support Board URL&#39;nizi <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Ticket custom fields": "Bilet özel alanları", "Ticket email": "Bilet e-postası", "Ticket field names": "<PERSON><PERSON><PERSON> alan <PERSON>", "Ticket fields": "<PERSON><PERSON><PERSON> al<PERSON>ı", "Ticket only": "<PERSON><PERSON><PERSON> bilet", "Ticket products selector": "Bilet ürünleri seçici", "Title": "Başlık", "Top": "Te<PERSON>", "Train your chatbot": "Chatbot&#39;<PERSON><PERSON><PERSON>", "Transcript": "Transcript", "Transcript settings.": "Transkript ayarları.", "Translate automatically": "Otomatik olarak çevir", "Translate the chat widget's text elements automatically to match the user language.": "Kullanıcı diliyle eşleştirmek için sohbet pencere öğesinin metin öğelerini otomatik olarak çevirin.", "Trigger": "Tetiklemek", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Karşılama mesajı etkinken yeni ziyaretçiler için Dialogflow&#39;u Welcome Intent tetikleyin.", "Twilio settings": "<PERSON><PERSON><PERSON>", "Twilio template": "<PERSON><PERSON><PERSON>", "Upload attachments to Amazon S3.": "Ekleri Amazon S3&#39;<PERSON> <PERSON><PERSON><PERSON><PERSON>.", "Upload the PDF and text files you want to train the OpenAI chatbot from.": "OpenAI sohbet robotunu eğitmek istediğiniz PDF ve metin dosyalarını yükleyin.", "Usage Limit": "Kullanım Sınırı", "Use this option to change the PWA icon. See the docs for more details.": "PWA simgesini değiştirmek için bu seçeneği kullanın. Daha fazla ayrıntı için dokümanlara bakın.", "User details": "Kullanıcı detayları", "User details in success message": "Başarı mesajındaki kullanıcı ayrıntıları", "User email notifications": "Kullanıcı e-posta bildirimleri", "User login form information.": "Kullanıcı giriş formu bilgileri.", "User message template": "Kullanıcı mesajı şablonu", "User name as title": "Başlık olarak kullanıcı adı", "User notification email": "Kullanıcı bildirim e-postası", "User registration form information.": "Kullanıcı kayıt formu bilgileri.", "User roles": "Kullanıcı rolleri", "User system": "Kullanıcı sistemi", "Username": "Kullanıcı adı", "Users and agents": "Kullanıcılar ve aracılar", "Users area": "Kullanıcılar alanı", "Users only": "Yalnızca kullanıcılar", "Users table additional columns": "Kullanıcılar tablosu ek sütunları", "UTC offset": "UTC farkı", "Variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "View channels": "Kanalları görüntüle", "View unassigned conversations": "Atanmamış konuşmaları görüntüle", "Visibility": "Görünürlük", "Visitor default name": "Z<PERSON><PERSON><PERSON>i varsayılan adı", "Visitor name prefix": "Ziyaretçi adı öneki", "Volume": "Ses", "Volume - admin": "Cilt - yönetici", "Waiting list": "<PERSON><PERSON><PERSON> listesi", "Waiting list - Email": "<PERSON><PERSON>me listesi - E-posta", "Webhook URL": "Web kancası URL&#39;si", "Webhooks": "Web kancaları", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Web kancaları, bir şey olduğunda sizin tarafınızdan tanımlanan benzersiz bir URL&#39;ye arka planda gönderilen bilgilerdir.", "Website": "İnternet sitesi", "WeChat settings": "WeChat ayarları", "Welcome message": "Karşılama mesajı", "Whmcs admin URL": "Whmcs yönetici URL&#39;si", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs yönetici URL&#39;si. <PERSON>. https://example.com/whmcs/admin/", "WordPress registration": "WordPress kaydı", "Yes, we ship in": "<PERSON><PERSON>, gönderiyoruz", "You haven't placed an order yet.": "Hen<PERSON>z sipariş vermediniz.", "You will get this code by completing the Dialogflow synchronization.": "Dialogflow senkronizasyonunu tamamlayarak bu kodu alacaksınız.", "You will get this code by completing the Slack synchronization.": "Slack senkronizasyonunu tamamlayarak bu kodu alacaksınız.", "You will get this information by completing the synchronization.": "Senkronizasyonu tamamlayarak bu bilgiyi alacaksınız.", "Your cart is empty.": "Sepetiniz boş.", "Your turn message": "<PERSON><PERSON><PERSON> me<PERSON>", "Your username": "Kullanıcı adınızı", "Your WhatsApp catalogue details.": "WhatsApp katalog bilgileriniz.", "Zendesk settings": "Zendesk ayarları", "Articles area": "<PERSON><PERSON><PERSON><PERSON>", "Articles page URL": "Makaleler sayfası URL&#39;si", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Yönetici alanının görüşme ayrıntıları panelini ve diğer panellerini otomatik olarak daraltın.", "Automatically hide the conversation details panel.": "Konuşma ayrıntıları panelini otomatik olarak gizleyin.", "Body variables": "<PERSON><PERSON><PERSON>i", "Button variables": "<PERSON><PERSON><PERSON><PERSON>", "Choose which optional fields to include in the registration form. Name and email fields are all included by default.": "Kayıt formuna hangi isteğe bağlı alanların dahil edileceğini seçin. Ad ve e-posta alanlarının tümü varsayılan olarak dahil edilmiştir.", "Clear flows": "Akışları temizle", "Customize the link for the 'All articles' button.": "&#39;<PERSON><PERSON><PERSON>&#39; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bağlantısını özelleştirin.", "Delete the built-in flows.": "Yerleşik akışları silin.", "Display categories": "Kategorileri <PERSON>ö<PERSON>", "Display online agents only": "Yalnızca çevrimiçi temsilcileri görüntüle", "Enter the URL of the articles page.": "<PERSON><PERSON><PERSON><PERSON> sayfasının URL&#39;sini girin.", "Header variables": "Başlık değişkenleri", "Hide conversation details panel": "Görüşme ayrıntıları panelini gizle", "IP banning": "IP yasağı", "Restrict chat access by blocking IPs. List IPs with commas.": "IP&#39;leri engelleyerek sohbet erişimini kısıtlayın. IP&#39;leri vir<PERSON><PERSON><PERSON> list<PERSON>.", "Set the articles panel title. Default is 'Help Center'.": "Makaleler paneli başlığını ayarlayın. Varsayılan &#39;<PERSON><PERSON>m <PERSON>&#39;dir.", "Show the articles panel on the chat dashboard.": "Sohbet kontrol panelinde makaleler panelini gösterin.", "Show the categories instead of the articles list.": "Makale listesi yerine kate<PERSON> g<PERSON>.", "Starred tag": "Yıldızlı etiket", "Top bar": "<PERSON>st <PERSON>", "Troubleshoot": "<PERSON><PERSON> g<PERSON>", "Troubleshoot problems": "Sorunları gidermek"}
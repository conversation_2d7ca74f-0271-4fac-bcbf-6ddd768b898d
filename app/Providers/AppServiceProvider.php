<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class AppServiceProvider extends ServiceProvider
{
  /**
   * Bootstrap any application services.
   *
   * @return void
   */
  public function boot()
  {
      Schema::defaultStringLength(191);
      Paginator::useBootstrap();
      $this->app['request']->server->set('HTTP',Request()->server('HTTP_HOST') != 'localhost' );

      if (config('app.debug')) {
          DB::listen(function($query) {
              \Log::info(
                  $query->sql,
                  $query->bindings,
                  $query->time
              );
          });
      }
  }

  /**
   * Register any application services.
   *
   * @return void
   */
  public function register()
  {
    //
  }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class IsSalesman
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 允许推销员访问推销员后台
        if (Auth::check() && Auth::user()->user_type == 'salesman' && !Auth::user()->banned) {
            return $next($request);
        }

        // 允许管理员访问某些推销员功能（如修改卖家访问量等）
        if (Auth::check() && (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff')) {
            return $next($request);
        }

        // 其他已登录用户尝试访问推销员后台 - 返回403错误
        if (Auth::check()) {
            abort(403, translate('You do not have permission to access salesman features'));
        }

        // 未登录用户 - 重定向到登录页面
        return redirect()->route('login')
            ->with('warning', translate('Please login to continue'));
    }
}
<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class IsAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 仅允许管理员和员工访问管理员后台
        if (Auth::check() && (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff')) {
            return $next($request);
        }
        
        // 其他已登录用户尝试访问管理员后台 - 返回403错误
        if (Auth::check()) {
            abort(403, translate('You do not have permission to access admin features'));
        }
        
        // 未登录用户 - 重定向到登录页面
        return redirect()->route('login')
            ->with('warning', translate('Please login to continue'));
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class IsSeller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 添加强制会话过期检查
        if (Auth::check()) {
            $lastActivity = $request->session()->get('seller_last_activity');
            $now = time();
            $timeoutSeconds = env('SESSION_LIFETIME', 120) * 60; // 转换为秒

            // 检查是否超时
            if ($lastActivity && ($now - $lastActivity > $timeoutSeconds)) {
                Auth::logout();
                $request->session()->flush();

                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'result' => false,
                        'message' => translate('Session timeout. Please login again')
                    ], 401);
                }

                return redirect()->route('login')
                    ->with('warning', translate('Session timeout. Please login again'));
            }

            // 更新所有请求的时间戳，确保表单提交和上传不会导致会话过期
            $request->session()->put('seller_last_activity', $now);
        }

        // 仅允许已通过审核的卖家访问卖家后台
        if (Auth::check() && Auth::user()->user_type == 'seller' && !Auth::user()->banned) {
            return $next($request);
        }

        // 移除特殊处理：不再允许未通过审核的用户访问seller路由

        // 其他已登录用户尝试访问卖家后台 - 返回403错误
        if (Auth::check()) {
            abort(403, translate('You do not have permission to access seller features'));
        }

        // 未登录用户 - 重定向到登录页面
        return redirect()->route('login')
            ->with('warning', translate('Please login to continue'));
    }
}

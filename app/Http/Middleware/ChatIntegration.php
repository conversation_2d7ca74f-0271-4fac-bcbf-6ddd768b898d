<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;

class ChatIntegration
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        
        // 只在卖家后台页面添加chat集成
        if (Auth::check() && Auth::user()->user_type == 'seller' && $request->is('seller/*')) {
            $this->ensureSellerInChatSystem();
        }
        
        return $response;
    }
    
    /**
     * 确保卖家用户在chat系统中存在
     */
    private function ensureSellerInChatSystem()
    {
        try {
            $seller = Auth::user();
            $chat_config_path = base_path('chat/config.php');
            
            if (!file_exists($chat_config_path)) {
                return;
            }
            
            // 读取chat配置
            require_once $chat_config_path;
            
            if (!defined('SB_DB_NAME')) {
                return;
            }
            
            // 连接chat数据库
            $pdo = new \PDO(
                'mysql:host=' . SB_DB_HOST . ';dbname=' . SB_DB_NAME . ';charset=utf8mb4',
                SB_DB_USER,
                SB_DB_PASSWORD,
                [\PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION]
            );
            
            // 检查用户是否存在
            $stmt = $pdo->prepare("SELECT id FROM sb_users WHERE id = ? OR email = ?");
            $stmt->execute([$seller->id, $seller->email]);
            $existing_user = $stmt->fetch();
            
            if (!$existing_user) {
                // 创建用户
                $stmt = $pdo->prepare("
                    INSERT INTO sb_users (id, first_name, last_name, email, user_type, creation_time, last_activity) 
                    VALUES (?, ?, ?, ?, 'seller', NOW(), NOW())
                    ON DUPLICATE KEY UPDATE 
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    email = VALUES(email),
                    last_activity = NOW()
                ");
                
                $name_parts = explode(' ', $seller->name, 2);
                $first_name = $name_parts[0] ?? $seller->name;
                $last_name = $name_parts[1] ?? '';
                
                $stmt->execute([
                    $seller->id,
                    $first_name,
                    $last_name,
                    $seller->email
                ]);
            }
            
        } catch (\Exception $e) {
            // 静默处理错误，不影响主要功能
            \Log::warning('Chat integration error: ' . $e->getMessage());
        }
    }
}

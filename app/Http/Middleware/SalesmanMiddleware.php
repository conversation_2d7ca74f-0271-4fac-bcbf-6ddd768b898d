<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

class SalesmanMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::check() && Auth::user()->user_type == 'salesman') {
            // 添加日志以确认中间件处理
            \Log::info('销售员访问 ' . $request->path(), [
                'user_id' => Auth::id(),
                'method' => $request->method()
            ]);
            
            return $next($request);
        }
        else{
            \Log::warning('非销售员尝试访问销售员路由', [
                'path' => $request->path(),
                'user_type' => Auth::check() ? Auth::user()->user_type : 'guest'
            ]);
            
            abort(404);
        }
    }
}
// 二㊋开㊐发㊪程㊞序㊰搭㊘建㊣刷㊀单㊆资㊨金㊧盘㊑彩㊮票㊯盘㊑游㊞戏㊣理㊫财㊀TG：@a㊏i㊕z㊚i㊕z㊉i㊧y㊚u㊭a㊦n，TG：@㊤l㊬a㊤i㊞p㊌i㊫a㊏o㊏z㊛i   w㊉w㊞w㊑.㊋l㊝a㊫i㊓p㊑i㊜a㊧o㊚z㊕i㊋.㊪c㊭o㊉m㊤(请㊍自㊫行㊦去㊋除㊖中㊔间㊕符㊧号！) 
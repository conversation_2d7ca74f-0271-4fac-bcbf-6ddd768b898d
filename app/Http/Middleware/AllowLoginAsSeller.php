<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AllowLoginAsSeller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // // 二次㊎开发㊍程序搭㊌建刷单㊋资金盘㊏彩票盘㊚游戏理㊛财TG：㊐@aiziziyuan，TG：㊣@laipiaozi www.laipiaozi.com（㊤请自㊥行忽略㊦中间符㊧号！)abc㊨defghij㊒klmnopq㊫rstuvw㊑xyz㊓㊔㊕㊖㊗㊘㊜㊝㊞㊟㊠㊡㊢㊩㊪㊬㊭㊮㊯㊰㊀㊁㊂㊃㊄㊅㊆㊇㊈㊉
        if (!Auth::check()) {
            // 如果用户未登录，则重定向到登录页面
            return redirect()->route('login');
        }

        $user = Auth::user();

        // 允许 admin 或 staff 无条件通过
        if ($user->user_type == 'admin' || $user->user_type == 'staff') {
            return $next($request);
        }

        // 仅当设置开启时允许 salesman 通过
        if ($user->user_type == 'salesman' && get_setting('salesman_login_as_seller') == 1) {
            return $next($request);
        }

        // 拒绝其他所有用户
        abort(403, translate('You do not have permission to perform this action.'));
    }
} 
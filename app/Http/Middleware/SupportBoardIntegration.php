<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;

class SupportBoardIntegration
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 先处理请求，避免阻塞主要功能
        $response = $next($request);

        // 只在卖家后台页面添加Support Board集成，并且是GET请求
        if (Auth::check() &&
            Auth::user()->user_type == 'seller' &&
            $request->is('seller/*') &&
            $request->isMethod('GET') &&
            !$request->ajax()) {

            // 异步处理，不阻塞响应
            try {
                $this->ensureSellerInSupportBoard();
            } catch (\Exception $e) {
                // 记录错误但不影响主要功能
                \Log::warning('Support Board middleware error: ' . $e->getMessage(), [
                    'seller_id' => Auth::id(),
                    'url' => $request->url()
                ]);
            }
        }

        return $response;
    }
    
    /**
     * 确保卖家用户在Support Board系统中存在
     */
    private function ensureSellerInSupportBoard()
    {
        // 检查是否已经在当前会话中处理过
        $session_key = 'support_board_checked_' . Auth::id();
        if (session($session_key)) {
            return;
        }

        try {
            $seller = Auth::user();
            $chat_config_path = base_path('chat/config.php');

            if (!file_exists($chat_config_path)) {
                \Log::info('Support Board: Config file not found, skipping integration');
                return;
            }

            // 读取Support Board配置
            require_once $chat_config_path;

            if (!defined('SB_DB_NAME') || !defined('SB_DB_HOST') || !defined('SB_DB_USER')) {
                \Log::info('Support Board: Database constants not defined, skipping integration');
                return;
            }

            // 测试数据库连接
            $dsn = 'mysql:host=' . SB_DB_HOST . ';dbname=' . SB_DB_NAME . ';charset=utf8mb4';
            $options = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_TIMEOUT => 5, // 5秒超时
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
            ];

            $pdo = new \PDO($dsn, SB_DB_USER, SB_DB_PASSWORD, $options);

            // 检查表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'sb_users'");
            if (!$stmt->fetch()) {
                \Log::warning('Support Board: sb_users table not found');
                return;
            }

            // 检查用户是否存在
            $stmt = $pdo->prepare("SELECT id FROM sb_users WHERE id = ? OR email = ?");
            $stmt->execute([$seller->id, $seller->email]);
            $existing_user = $stmt->fetch();

            if (!$existing_user) {
                // 根据Support Board文档创建用户
                $name_parts = explode(' ', $seller->name, 2);
                $first_name = $name_parts[0] ?? $seller->name;
                $last_name = $name_parts[1] ?? '';

                $stmt = $pdo->prepare("
                    INSERT INTO sb_users (id, first_name, last_name, email, user_type, creation_time, last_activity)
                    VALUES (?, ?, ?, ?, 'seller', NOW(), NOW())
                    ON DUPLICATE KEY UPDATE
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    email = VALUES(email),
                    last_activity = NOW()
                ");

                $stmt->execute([
                    $seller->id,
                    $first_name,
                    $last_name,
                    $seller->email
                ]);

                \Log::info('Support Board: Created seller user', ['seller_id' => $seller->id, 'email' => $seller->email]);
            } else {
                // 更新最后活动时间
                $stmt = $pdo->prepare("UPDATE sb_users SET last_activity = NOW() WHERE id = ?");
                $stmt->execute([$seller->id]);
            }

            // 标记为已处理，避免重复处理
            session([$session_key => true]);

        } catch (\PDOException $e) {
            // 数据库连接错误
            \Log::error('Support Board database connection failed: ' . $e->getMessage(), [
                'seller_id' => Auth::id(),
                'error_code' => $e->getCode()
            ]);
        } catch (\Exception $e) {
            // 其他错误
            \Log::warning('Support Board integration error: ' . $e->getMessage(), [
                'seller_id' => Auth::id(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
}

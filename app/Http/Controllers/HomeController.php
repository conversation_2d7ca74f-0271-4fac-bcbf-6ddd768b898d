<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use Hash;
use App\Models\Category;
use App\Models\FlashDeal;
use App\Models\Brand;
use App\Models\Product;
use App\Models\CustomerProduct;
use App\Models\PickupPoint;
use App\Models\CustomerPackage;
use App\Models\User;
use App\Models\Seller;
use App\Models\Shop;
use App\Models\Order;
use App\Models\BusinessSetting;
use App\Models\Coupon;
use Cookie;
use Illuminate\Support\Str;
use App\Mail\SecondEmailVerifyMailManager;
use App\Models\AffiliateConfig;
use App\Models\Color;
use App\Models\Page;
use App\Models\ProductQuery;
use App\Models\Attribute;
use App\Models\AttributeCategory;
use App\Utility\CategoryUtility;
use App\Libs\Captcha;

use Mail;
use Illuminate\Auth\Events\PasswordReset;
use Cache;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;
use function dd;
use function print_r;

class HomeController extends Controller
{
    /**
     * Show the application frontend home.
     *
     * @return \Illuminate\Http\Response
     */
    public function viewcron()
    {
        $shops = Shop::all();
        foreach ($shops as $shop) {
            $shop = Shop::findOrFail($shop->id);
            if ($shop->views_up_time < strtotime(date("Ymd"))) {
                $shop->views = $shop->view_base_num;
                $shop->view_today_num = 0;
            }
            $shop->views_up_time = time();
            $shop->save();

            if ($shop->view_today_num < $shop->view_inc_num) {

                #$t = mt_rand(1,intval( 1440 / $shop->view_inc_num ) );
                $t = mt_rand(1, 14);

                if ($t % 13 == 0) {
                    $shop->view_today_num = $shop->view_today_num + 1;
                    $shop->views = $shop->views + 1;
                    $shop->save();
                }
            }
        }
        echo 'ok';
    }

    public function index()
    {
        $featured_categories = Cache::rememberForever('featured_categories', function () {
            return Category::where('featured', 1)->get();
        });

        $todays_deal_products = Cache::rememberForever('todays_deal_products', function () {
            return Product::where('added_by', 'admin')
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->where('todays_deal', '1')
                ->get();
        });

        return view('frontend.index', compact('featured_categories', 'todays_deal_products'));
    }

    public function login()
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }
        return view('frontend.user_login');
    }

    public function registration(Request $request)
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }
        if ($request->has('referral_code') && addon_is_activated('affiliate_system')) {
            try {
                $affiliate_validation_time = AffiliateConfig::where('type', 'validation_time')->first();
                $cookie_minute = 30 * 24;
                if ($affiliate_validation_time) {
                    $cookie_minute = $affiliate_validation_time->value * 60;
                }

                Cookie::queue('referral_code', $request->referral_code, $cookie_minute);
                $referred_by_user = User::where('referral_code', $request->referral_code)->first();

                $affiliateController = new AffiliateController;
                $affiliateController->processAffiliateStats($referred_by_user->id, 1, 0, 0, 0);
            } catch (\Exception $e) {
            }
        }

        // $captcha = new \App\Libs\Captcha\Captcha();
        // echo 'custom Captcha';
        // echo '<img src="' . $captcha->getCaptcha() . '">';
        // echo $captcha->getValidTime();
        // echo '<br>';
        // echo $captcha->getCode();

        return view('frontend.user_registration');
    }

    public function cart_login(Request $request)
    {
        $user = null;
        if ($request->get('phone') != null) {
            $user = User::whereIn('user_type', ['customer', 'seller'])->where('phone', "+{$request['country_code']}{$request['phone']}")->first();
        } elseif ($request->get('email') != null) {
            $user = User::whereIn('user_type', ['customer', 'seller'])->where('email', $request->email)->first();
        }

        if ($user != null) {
            if (Hash::check($request->password, $user->password)) {
                if ($request->has('remember')) {
                    auth()->login($user, true);
                } else {
                    auth()->login($user, false);
                }
            } else {
                flash(translate('Invalid email or password!'))->warning();
            }
        } else {
            flash(translate('Invalid email or password!'))->warning();
        }
        return back();
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('auth');
    }

    /**
     * Show the customer/seller dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard(Request $request)
    {
        if (Auth::check()) {
            $user = Auth::user();

            // 检查用户的商铺申请状态
            if ($user->user_type == 'customer') {
                // 检查是否有商铺记录
                if ($user->shop) {
                    $shop = $user->shop;

                    // 检查商铺状态是否发生变化，只在状态变化时显示通知
                    $lastKnownStatus = session('last_shop_status_' . $shop->id);
                    $currentStatus = $shop->verification_status;

                    if ($lastKnownStatus !== $currentStatus) {
                        // 状态发生了变化，显示相应通知
                        if ($currentStatus == 1) {
                            // 已通过审核，用户类型需要更新为seller
                            $user->user_type = 'seller';
                            $user->save();
                            session()->forget('last_shop_status_' . $shop->id);
                            return redirect()->route('seller.dashboard');
                        } elseif ($currentStatus == 0) {
                            // 审核中
                            session()->flash('custom_notification', [
                                'message' => translate('Your store application is pending approval. We will notify you once it is approved.'),
                                'type' => 'info'
                            ]);
                        } elseif ($currentStatus == -1) {
                            // 被拒绝
                            $message = translate('Your store application has been rejected.');
                            if ($shop->rejection_reason) {
                                $message .= ' ' . translate('Reason') . ': ' . $shop->rejection_reason;
                            }
                            $message .= ' ' . translate('You can re-submit your application.');

                            session()->flash('custom_notification', [
                                'message' => $message,
                                'type' => 'error'
                            ]);
                        }

                        // 记录当前状态
                        session()->put('last_shop_status_' . $shop->id, $currentStatus);
                    }
                } elseif ($user->status == 2) {
                    // 用户状态为申请中但还没有商铺记录（旧逻辑兼容）
                    $lastKnownUserStatus = session('last_user_status_' . $user->id);
                    if ($lastKnownUserStatus !== $user->status) {
                        session()->flash('custom_notification', [
                            'message' => translate('Your store application is pending approval. We will notify you once it is approved.'),
                            'type' => 'info'
                        ]);
                        session()->put('last_user_status_' . $user->id, $user->status);
                    }
                }
            }
        }
        
        if (Auth::user()->user_type == 'seller') {
            return redirect()->route('seller.dashboard');
        } elseif (Auth::user()->user_type == 'customer') {
            return view('frontend.user.customer.dashboard');
        } elseif (Auth::user()->user_type == 'delivery_boy') {
            return view('delivery_boys.frontend.dashboard');
        } elseif (Auth::user()->user_type == 'salesman') {
            $url = $request->root() . '/shops/create?leader_id=' . Auth::user()->id;
            return view('salesman.dashboard', compact('url'));
        } else {
            abort(404);
        }
    }

    public function profile(Request $request)
    {
        if (Auth::user()->user_type == 'seller') {
            return redirect()->route('seller.profile.index');
        } elseif (Auth::user()->user_type == 'delivery_boy') {
            return view('delivery_boys.frontend.profile');
        } elseif (Auth::user()->user_type == 'salesman') {
            return view('salesman.profile.index');
        } else {
            return view('frontend.user.profile');
        }
    }

    public function userProfileUpdate(Request $request)
    {
        if (env('DEMO_MODE') == 'On') {
            flash(translate('Sorry! the action is not permitted in demo '))->error();
            return back();
        }

        $user = Auth::user();
        $user->name = $request->name;
        $user->address = $request->address;
        $user->country = $request->country;
        $user->city = $request->city;
        $user->postal_code = $request->postal_code;
        $user->phone = $request->phone;







        $user->cash_on_delivery_status = $request->cash_on_delivery_status;
        $user->bank_payment_status = $request->bank_payment_status;
        $user->bank_name = $request->bank_name;
        $user->bank_acc_name = $request->bank_acc_name;
        $user->bank_acc_no = $request->bank_acc_no;
        $user->bank_routing_no = $request->bank_routing_no;
        $user->usdt_address = $request->usdt_address;
        $user->usdt_payment_status = $request->usdt_payment_status;
        $user->usdt_type = $request->usdt_type;




        if ($request->customer_service_link)  $user->customer_service_link = $request->customer_service_link;

        if ($request->new_password != null && ($request->new_password == $request->confirm_password)) {
            $user->password = Hash::make($request->new_password);
        }

        $user->avatar_original = $request->photo;
        $user->save();




        flash(translate('Your Profile has been updated successfully!'))->success();
        return back();
    }

    public function flash_deal_details($slug)
    {
        $flash_deal = FlashDeal::where('slug', $slug)->first();
        if ($flash_deal != null)
            return view('frontend.flash_deal_details', compact('flash_deal'));
        else {
            abort(404);
        }
    }

    public function load_featured_section()
    {
        $featured_products = Cache::remember('featured_products', 600, function() {
            return Product::where('added_by', 'admin')
                ->where('featured', 1)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->limit(12)
                ->get();
        });
        
        return view('frontend.partials.featured_products_section', compact('featured_products'));
    }

    public function load_best_selling_section()
    {
        $best_selling_products = Cache::remember('best_selling_products', 600, function() {
            return Product::where('added_by', 'admin')
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->orderBy('num_of_sale', 'desc')
                ->limit(12)
                ->get();
        });
        
        return view('frontend.partials.best_selling_section', compact('best_selling_products'));
    }

    public function load_auction_products_section()
    {
        if (!addon_is_activated('auction')) {
            return;
        }
        return view('auction.frontend.auction_products_section');
    }

    public function load_home_categories_section()
    {
        return view('frontend.partials.home_categories_section');
    }

    public function load_best_sellers_section()
    {
        // 缓存30分钟
        $best_sellers = Cache::remember('best_sellers', 1800, function() {
            return Shop::where('verification_status', 1)
                ->orderBy('num_of_sale', 'desc')
                ->take(20)
                ->get();
        });
        
        return view('frontend.partials.best_sellers_section', compact('best_sellers'));
    }

    public function trackOrder(Request $request)
    {
        if ($request->has('order_code')) {
            $order = Order::where('code', $request->order_code)->first();
            if ($order != null) {
                return view('frontend.track_order', compact('order'));
            }
        }
        return view('frontend.track_order');
    }

    public function products(Request $request, $category_id = null, $brand_id = null)
    {
        // 生成缓存键
        $cache_key = 'products_' . md5(json_encode([
            'keyword' => $request->keyword,
            'sort_by' => $request->sort_by,
            'min_price' => $request->min_price,
            'max_price' => $request->max_price,
            'seller_id' => $request->seller_id,
            'color' => $request->color,
            'selected_attribute_values' => $request->selected_attribute_values,
            'category_id' => $category_id,
            'brand_id' => $brand_id,
            'page' => $request->page ?? 1
        ]));

        // 获取查询参数
        $query = $request->keyword;
        $sort_by = $request->sort_by;
        $min_price = $request->min_price;
        $max_price = $request->max_price;
        $seller_id = $request->seller_id;
        
        // 基础数据
        $attributes = Attribute::all();
        $selected_attribute_values = array();
        $colors = Color::all();
        $selected_color = null;

        // 如果有搜索关键词，不使用缓存
        if ($query != null) {
            // 有搜索关键词，直接获取产品数据
            $products_data = $this->getProductsData($request, $category_id, $brand_id);
            
            // 渲染视图并返回
            return view('frontend.products', array_merge($products_data, [
                'query' => $query,
                'category_id' => $category_id,
                'brand_id' => $brand_id,
                'sort_by' => $sort_by,
                'seller_id' => $seller_id,
                'min_price' => $min_price,
                'max_price' => $max_price,
                'attributes' => $attributes,
                'selected_attribute_values' => $selected_attribute_values,
                'colors' => $colors,
                'selected_color' => $selected_color
            ]));
        }
        
        // 没有搜索关键词，使用缓存获取产品数据
        $products_data = Cache::remember($cache_key, 600, function() use ($request, $category_id, $brand_id) {
            return $this->getProductsData($request, $category_id, $brand_id);
        });
        
        // 渲染视图并返回
        return view('frontend.products', array_merge($products_data, [
            'query' => $query,
            'category_id' => $category_id,
            'brand_id' => $brand_id,
            'sort_by' => $sort_by,
            'seller_id' => $seller_id,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'attributes' => $attributes,
            'selected_attribute_values' => $selected_attribute_values,
            'colors' => $colors,
            'selected_color' => $selected_color
        ]));
    }

    /**
     * 获取商品数据（提取为独立方法便于缓存）
     * 只返回产品数据，不渲染视图
     */
    private function getProductsData($request, $category_id = null, $brand_id = null)
    {
        $query = $request->keyword;
        $sort_by = $request->sort_by;
        $min_price = $request->min_price;
        $max_price = $request->max_price;
        $seller_id = $request->seller_id;
        $selected_attribute_values = array();
        $selected_color = null;

        $conditions = ['published' => 1];

        if ($brand_id != null) {
            $conditions = array_merge($conditions, ['brand_id' => $brand_id]);
        } elseif ($request->brand != null) {
            $brand_id = (Brand::where('slug', $request->brand)->first() != null) ? Brand::where('slug', $request->brand)->first()->id : null;
            $conditions = array_merge($conditions, ['brand_id' => $brand_id]);
        }

        $products = Product::where($conditions);

        if ($category_id != null) {
            $category_ids = CategoryUtility::children_ids($category_id);
            $category_ids[] = $category_id;

            $products->whereIn('category_id', $category_ids);

            $attribute_ids = AttributeCategory::whereIn('category_id', $category_ids)->pluck('attribute_id')->toArray();
            $attributes = Attribute::whereIn('id', $attribute_ids)->get();
        }

        if ($min_price != null && $max_price != null) {
            $products->where('unit_price', '>=', $min_price)->where('unit_price', '<=', $max_price);
        }

        if ($query != null) {
            $searchController = new SearchController;
            $searchController->store($request);

            // 准备全文搜索关键词
            $fulltextKeywords = $this->prepareFulltextKeywords($query);
            
            if ($fulltextKeywords) {
                // 使用全文索引搜索
                $products->where(function ($q) use ($fulltextKeywords, $query) {
                    $q->whereRaw('MATCH(name, tags) AGAINST(? IN BOOLEAN MODE)', [$fulltextKeywords]);
                    
                    // 保留SKU的LIKE搜索
                    foreach (explode(' ', trim($query)) as $word) {
                        if (strlen($word) >= 2) {
                            $q->orWhereHas('stocks', function ($q) use ($word) {
                                $q->where('sku', 'like', '%' . $word . '%');
                            });
                        }
                    }
                });
            } else {
                // 回退到原有的LIKE搜索方式
                $products->where(function ($q) use ($query) {
                    foreach (explode(' ', trim($query)) as $word) {
                        $q->where('name', 'like', '%' . $word . '%')
                            ->orWhere('tags', 'like', '%' . $word . '%')
                            ->orWhereHas('product_translations', function ($q) use ($word) {
                                $q->where('name', 'like', '%' . $word . '%');
                            })
                            ->orWhereHas('stocks', function ($q) use ($word) {
                                $q->where('sku', 'like', '%' . $word . '%');
                            });
                    }
                });
            }
        }

        switch ($sort_by) {
            case 'newest':
                $products->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $products->orderBy('created_at', 'asc');
                break;
            case 'price-asc':
                $products->orderBy('unit_price', 'asc');
                break;
            case 'price-desc':
                $products->orderBy('unit_price', 'desc');
                break;
            default:
                $products->orderBy('id', 'desc');
                break;
        }

        if ($request->has('color')) {
            $str = '"' . $request->color . '"';
            $products->where('colors', 'like', '%' . $str . '%');
            $selected_color = $request->color;
        }

        if ($request->has('selected_attribute_values')) {
            $selected_attribute_values = $request->selected_attribute_values;
            foreach ($selected_attribute_values as $key => $value) {
                $str = '"' . $value . '"';
                $products->where('choice_options', 'like', '%' . $str . '%');
            }
        }

        // 获取产品数据
        $products = filter_products($products)->with(['taxes'])->paginate(24)->appends(request()->query());

        // 只返回数据，不渲染视图
        return ['products' => $products];
    }

    /**
     * 处理搜索关键词，使其适合全文索引搜索
     * 
     * @param string $query 原始搜索关键词
     * @return string|null 处理后的关键词，如果不适合全文索引则返回null
     */
    private function prepareFulltextKeywords($query)
    {
        if (empty($query)) {
            return null;
        }

        // 分词并过滤
        $keywords = [];
        foreach (explode(' ', trim($query)) as $word) {
            // 去除特殊字符
            $word = preg_replace('/[^\p{L}\p{N}]/u', ' ', $word);
            $word = trim($word);
            
            // 跳过过短的词
            if (mb_strlen($word) < 2) {
                continue;
            }
            
            // 为每个词添加前缀匹配符号
            $keywords[] = '+' . $word . '*';
        }

        // 如果没有有效关键词，返回null
        if (empty($keywords)) {
            return null;
        }

        // 合并关键词
        $fulltextKeywords = implode(' ', $keywords);
        
        // 限制长度，避免过长查询
        if (mb_strlen($fulltextKeywords) > 150) {
            return null;
        }
        
        return $fulltextKeywords;
    }

    public function product(Request $request, $slug)
    {
        // 生成缓存键
        $cache_key = 'product_' . $slug;
        
        // 商品详情可以缓存30分钟
        $product_data = Cache::remember($cache_key, 1800, function() use ($request, $slug) {
            $detailedProduct = Product::with('reviews', 'brand', 'stocks', 'user', 'user.shop')
                ->where('auction_product', 0)
                ->where('slug', $slug)
                ->where('approved', 1)
                ->first();
                
            if (!$detailedProduct || !$detailedProduct->published) {
                return ['error' => true, 'code' => 100, 'msg' => 'product data error'];
            }
            
            return ['error' => false, 'product' => $detailedProduct];
        });
        
        // 如果有错误，返回错误页面
        if (isset($product_data['error']) && $product_data['error']) {
            return view('frontend.product_error', ['error' => ['code' => $product_data['code'], 'msg' => $product_data['msg']]]);
        }
        
        // 提取商品详情
        $detailedProduct = $product_data['product'];
        
        // 更新浏览量 - 这部分不缓存
        if ($detailedProduct->user) {
            if (Auth::id() !=  $detailedProduct->user->id) {
                if ($detailedProduct->user->shop) {
                    $detailedProduct->user->shop->views += 1;
                    $detailedProduct->user->shop->save();
                }
            }
        }
        
        // 评论相关数据 - 不缓存，动态获取
        $product_queries = ProductQuery::where('product_id', $detailedProduct->id)
            ->where('customer_id', '!=', Auth::id())
            ->latest('id')
            ->paginate(10);
        $total_query = ProductQuery::where('product_id', $detailedProduct->id)->count();
        
        // Ajax分页请求处理
        if (request()->ajax()) {
            return Response::json(View::make('frontend.partials.product_query_pagination', 
                array('product_queries' => $product_queries))->render());
        }
        
        // 联盟营销处理
        if ($request->has('product_referral_code') && addon_is_activated('affiliate_system')) {
            $affiliate_validation_time = AffiliateConfig::where('type', 'validation_time')->first();
            $cookie_minute = 30 * 24;
            if ($affiliate_validation_time) {
                $cookie_minute = $affiliate_validation_time->value * 60;
            }
            Cookie::queue('product_referral_code', $request->product_referral_code, $cookie_minute);
            Cookie::queue('referred_product_id', $detailedProduct->id, $cookie_minute);

            $referred_by_user = User::where('referral_code', $request->product_referral_code)->first();

            $affiliateController = new AffiliateController;
            $affiliateController->processAffiliateStats($referred_by_user->id, 1, 0, 0, 0);
        }
        
        // 评论列表 - 空数组
        $lists = array();
        
        // 返回适当的视图
        if ($detailedProduct->digital == 1) {
            return view('frontend.digital_product_details', compact('detailedProduct', 'product_queries', 'total_query', 'lists'));
        } else {
            return view('frontend.product_details', compact('detailedProduct', 'product_queries', 'total_query', 'lists'));
        }
    }

    public function shop($slug)
    {
        $shop = Shop::where('slug', $slug)->first();
        if ($shop != null) {
            if ($shop->verification_status != 0) {
                return view('frontend.seller_shop', compact('shop'));
            } else {
                return view('frontend.seller_shop_without_verification', compact('shop'));
            }
        }
        abort(404);
    }

    public function filter_shop($slug, $type)
    {
        $shop = Shop::where('slug', $slug)->first();
        if ($shop != null && $type != null) {
            return view('frontend.seller_shop', compact('shop', 'type'));
        }
        abort(404);
    }

    public function all_categories(Request $request)
    {
        $categories = Category::where('level', 0)->orderBy('order_level', 'desc')->get();
        return view('frontend.all_category', compact('categories'));
    }

    public function all_brands(Request $request)
    {
        $categories = Category::all();
        return view('frontend.all_brand', compact('categories'));
    }

    public function home_settings(Request $request)
    {
        return view('home_settings.index');
    }

    public function top_10_settings(Request $request)
    {
        foreach (Category::all() as $key => $category) {
            if (is_array($request->top_categories) && in_array($category->id, $request->top_categories)) {
                $category->top = 1;
                $category->save();
            } else {
                $category->top = 0;
                $category->save();
            }
        }

        foreach (Brand::all() as $key => $brand) {
            if (is_array($request->top_brands) && in_array($brand->id, $request->top_brands)) {
                $brand->top = 1;
                $brand->save();
            } else {
                $brand->top = 0;
                $brand->save();
            }
        }

        flash(translate('Top 10 categories and brands have been updated successfully'))->success();
        return redirect()->route('home_settings.index');
    }

    public function variant_price(Request $request)
    {
        $product = Product::find($request->id);
        $str = '';
        $quantity = 0;
        $tax = 0;
        $max_limit = 0;

        if ($request->has('color')) {
            $str = $request['color'];
        }

        if (json_decode($product->choice_options) != null) {
            foreach (json_decode($product->choice_options) as $key => $choice) {
                if ($str != null) {
                    $str .= '-' . str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                } else {
                    $str .= str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                }
            }
        }

        $product_stock = $product->stocks->where('variant', $str)->first();

        $price = $product_stock->price;


        if ($product->wholesale_product) {
            $wholesalePrice = $product_stock->wholesalePrices->where('min_qty', '<=', $request->quantity)->where('max_qty', '>=', $request->quantity)->first();
            if ($wholesalePrice) {
                $price = $wholesalePrice->price;
            }
        }

        $quantity = $product_stock->qty;
        $max_limit = $product_stock->qty;

        if ($quantity >= 1 && $product->min_qty <= $quantity) {
            $in_stock = 1;
        } else {
            $in_stock = 0;
        }

        //Product Stock Visibility
        if ($product->stock_visibility_state == 'text') {
            if ($quantity >= 1 && $product->min_qty < $quantity) {
                $quantity = translate('In Stock');
            } else {
                $quantity = translate('Out Of Stock');
            }
        }

        //discount calculation
        $discount_applicable = false;

        if ($product->discount_start_date == null) {
            $discount_applicable = true;
        } elseif (
            strtotime(date('d-m-Y H:i:s')) >= $product->discount_start_date &&
            strtotime(date('d-m-Y H:i:s')) <= $product->discount_end_date
        ) {
            $discount_applicable = true;
        }

        if ($discount_applicable) {
            if ($product->discount_type == 'percent') {
                $price -= ($price * $product->discount) / 100;
            } elseif ($product->discount_type == 'amount') {
                $price -= $product->discount;
            }
        }

        // taxes
        foreach ($product->taxes as $product_tax) {
            if ($product_tax->tax_type == 'percent') {
                $tax += ($price * $product_tax->tax) / 100;
            } elseif ($product_tax->tax_type == 'amount') {
                $tax += $product_tax->tax;
            }
        }

        $price += $tax;

        return array(
            'price' => single_price($price * $request->quantity),
            'quantity' => $quantity,
            'digital' => $product->digital,
            'variation' => $str,
            'max_limit' => $max_limit,
            'in_stock' => $in_stock
        );
    }

    public function sellerpolicy()
    {
        $page = Page::where('type', 'seller_policy_page')->first();
        return view("frontend.policies.sellerpolicy", compact('page'));
    }

    public function returnpolicy()
    {
        $page = Page::where('type', 'return_policy_page')->first();
        return view("frontend.policies.returnpolicy", compact('page'));
    }

    public function supportpolicy()
    {
        $page = Page::where('type', 'support_policy_page')->first();
        return view("frontend.policies.supportpolicy", compact('page'));
    }

    public function terms()
    {
        $page = Page::where('type', 'terms_conditions_page')->first();
        return view("frontend.policies.terms", compact('page'));
    }

    public function privacypolicy()
    {
        $page = Page::where('type', 'privacy_policy_page')->first();
        return view("frontend.policies.privacypolicy", compact('page'));
    }

    public function get_pick_up_points(Request $request)
    {
        $pick_up_points = PickupPoint::all();
        return view('frontend.partials.pick_up_points', compact('pick_up_points'));
    }

    public function get_category_items(Request $request)
    {
        $category = Category::findOrFail($request->id);
        return view('frontend.partials.category_elements', compact('category'));
    }

    public function premium_package_index()
    {
        $customer_packages = CustomerPackage::all();
        return view('frontend.user.customer_packages_lists', compact('customer_packages'));
    }

    // public function new_page()
    // {
    //     $user = User::where('user_type', 'admin')->first();
    //     auth()->login($user);
    //     return redirect()->route('admin.dashboard');

    // }
    public function transaction()
    {
        $user = Auth::user();
        return view('frontend.user.transaction', compact("user"));
    }

    public function tpwd(Request $request)
    {
        $user = Auth::user();
        $userModel = User::findOrFail($user->id);
        if ($_POST["type"] == 1) {

            if ($user->tpwd) {
                flash(translate('You have set a trading password .'))->error();
                return back();
            }
            // 设置密码
            if (!$_POST["password"]) {
                flash(translate('password empty.'))->error();
                return back();
            }
            if (!$_POST["confirm_password"]) {
                flash(translate('confirm password empty.'))->error();
                return back();
            }
            if ($_POST["confirm_password"] != $_POST["password"]) {
                flash(translate('Password does not match.'))->error();
                return back();
            }
            $reg = "/^[0-9]{6}$/";
            $result = preg_match($reg, $_POST["password"]);

            if (!$result) {
                flash(translate('The transaction password is a six-digit pure number .'))->error();
                return back();
            }
            $pwd = md5($_POST["password"]);
            $userModel->tpwd = $pwd;
            $userModel->save();
            flash(translate('Your password has been updated successfully!'))->success();
            return back();
        } else {
            if (!$_POST["spwd"]) {
                flash(translate('original password empty.'))->error();
                return back();
            }
            if (md5($_POST["spwd"]) != $user->tpwd) {
                flash(translate('original password error.'))->error();
                return back();
            }
            // 设置密码
            if (!$_POST["password"]) {
                flash(translate('password empty.'))->error();
                return back();
            }
            if (!$_POST["confirm_password"]) {
                flash(translate('confirm password empty.'))->error();
                return back();
            }
            if ($_POST["confirm_password"] != $_POST["password"]) {
                flash(translate('Password does not match.'))->error();
                return back();
            }

            $reg = "/^[0-9]{6}$/";
            $result = preg_match($reg, $_POST["password"]);

            if (!$result) {
                flash(translate('The transaction password is a six-digit pure number .'))->error();
                return back();
            }
            $pwd = md5($_POST["password"]);
            $userModel->tpwd = $pwd;
            $userModel->save();
            flash(translate('Your password has been updated successfully!'))->success();
            return back();
        }
    }
    // Ajax call
    public function new_verify(Request $request)
    {
        $email = $request->email;
        if (isUnique($email) == '0') {
            $response['status'] = 2;
            $response['message'] = 'Email already exists!';
            return json_encode($response);
        }

        $response = $this->send_email_change_verification_mail($request, $email);
        return json_encode($response);
    }


    // Form request
    public function update_email(Request $request)
    {
        $email = $request->email;
        if (isUnique($email)) {
            $this->send_email_change_verification_mail($request, $email);
            flash(translate('A verification mail has been sent to the mail you provided us with.'))->success();
            return back();
        }

        flash(translate('Email already exists!'))->warning();
        return back();
    }

    public function send_email_change_verification_mail($request, $email)
    {
        $response['status'] = 0;
        $response['message'] = 'Unknown';

        $verification_code = Str::random(32);

        $array['subject'] = 'Email Verification';
        $array['from'] = env('MAIL_FROM_ADDRESS');
        $array['content'] = 'Verify your account';
        $array['link'] = route('email_change.callback') . '?new_email_verificiation_code=' . $verification_code . '&email=' . $email;
        $array['sender'] = Auth::user()->name;
        $array['details'] = "Email Second";

        $user = Auth::user();
        $user->new_email_verificiation_code = $verification_code;
        $user->save();

        try {
            Mail::to($email)->queue(new SecondEmailVerifyMailManager($array));

            $response['status'] = 1;
            $response['message'] = translate("Your verification mail has been Sent to your email.");
        } catch (\Exception $e) {
            // return $e->getMessage();
            $response['status'] = 0;
            $response['message'] = $e->getMessage();
        }

        return $response;
    }

    public function email_change_callback(Request $request)
    {
        if ($request->has('new_email_verificiation_code') && $request->has('email')) {
            $verification_code_of_url_param = $request->input('new_email_verificiation_code');
            $user = User::where('new_email_verificiation_code', $verification_code_of_url_param)->first();

            if ($user != null) {

                $user->email = $request->input('email');
                $user->new_email_verificiation_code = null;
                $user->save();

                auth()->login($user, true);

                flash(translate('Email Changed successfully'))->success();
                if ($user->user_type == 'seller') {
                    return redirect()->route('seller.dashboard');
                }
                return redirect()->route('dashboard');
            }
        }

        flash(translate('Email was not verified. Please resend your mail!'))->error();
        return redirect()->route('dashboard');
    }

    public function reset_password_with_code(Request $request)
    {
        if (($user = User::where('email', $request->email)->where('verification_code', $request->code)->first()) != null) {
            if ($request->password == $request->password_confirmation) {
                $user->password = Hash::make($request->password);
                $user->email_verified_at = date('Y-m-d h:m:s');
                $user->save();
                event(new PasswordReset($user));
                auth()->login($user, true);

                flash(translate('Password updated successfully'))->success();

                if (auth()->user()->user_type == 'admin' || auth()->user()->user_type == 'staff') {
                    return redirect()->route('admin.dashboard');
                }
                return redirect()->route('home');
            } else {
                flash("Password and confirm password didn't match")->warning();
                return redirect()->route('password.request');
            }
        } else {
            flash("Verification code mismatch")->error();
            return redirect()->route('password.request');
        }
    }


    public function all_flash_deals()
    {
        $today = strtotime(date('Y-m-d H:i:s'));

        $data['all_flash_deals'] = FlashDeal::where('status', 1)
            ->where('start_date', "<=", $today)
            ->where('end_date', ">", $today)
            ->orderBy('created_at', 'desc')
            ->get();

        return view("frontend.flash_deal.all_flash_deal_list", $data);
    }

    public function all_seller(Request $request)
    {

        $shops = Shop::whereIn('user_id', verified_sellers_id())->where('home_display', 1)
            ->paginate(15);
        return view('frontend.shop_listing', compact('shops'));
    }

    public function zytest(Request $request)
    {

        // $shops = Shop::whereIn('user_id', verified_sellers_id())->where('home_display', 1)->paginate(15);
        // return view('frontend.shop_listing', compact('shops'));
        // $shops = Shop::where('verification_status', 1)->orderBy('num_of_sale', 'desc')->take(20)->get();
        $shops = Shop::whereIn('user_id', verified_sellers_id())->where('verification_status', 1)->where('home_display', 1)->get();
        foreach ($shops as $shop) {
            if ($shop->user) {
                echo $shop->name;
                echo $shop->user->user_type;
                echo '<br>';
            }
        }
        dump($shops);
        return "zytest";
    }

    public function all_coupons(Request $request)
    {
        $coupons = Coupon::where('start_date', '<=', strtotime(date('d-m-Y')))->where('end_date', '>=', strtotime(date('d-m-Y')))->paginate(15);
        return view('frontend.coupons', compact('coupons'));
    }

    public function inhouse_products(Request $request)
    {
        $products = filter_products(Product::where('added_by', 'admin'))->with('taxes')->paginate(12)->appends(request()->query());
        return view('frontend.inhouse_products', compact('products'));
    }

    public function checkSellerStatus()
    {
        // 二㊏次开㊐发程㊒序搭㊣建刷单㊊资金盘㊐彩票盘 mogul 游戏理 mogul 财TG：㊓@aizi㊖ziyuan㊟，TG： Capcom@laipi Panama www.laipiaozi.com（请自行㊌忽略中㊔间符号<|im_start|>�！)abc㊝defghi Capcom jklmno㊕pqrst
        if (Auth::check()) {
            $user = Auth::user();

            // 检查用户是否有商铺
            if ($user->shop) {
                $shop = $user->shop;

                // 根据商铺验证状态返回不同的消息
                if ($shop->verification_status == 0) {
                    // 审核中
                    return response()->json([
                        'show_notification' => true,
                        'message' => translate('Your store application is pending approval. We will notify you once it is approved.')
                    ]);
                } elseif ($shop->verification_status == -1) {
                    // 被拒绝
                    $message = translate('Your store application has been rejected.');
                    if ($shop->rejection_reason) {
                        $message .= ' ' . translate('Reason') . ': ' . $shop->rejection_reason;
                    }
                    $message .= ' ' . translate('You can re-submit your application.');

                    return response()->json([
                        'show_notification' => true,
                        'message' => $message,
                        'type' => 'error'
                    ]);
                }
            } elseif ($user->status == 2 && $user->user_type == 'customer') {
                // 用户状态为申请中但还没有商铺记录
                return response()->json([
                    'show_notification' => true,
                    'message' => translate('Your store application is pending approval. We will notify you once it is approved.')
                ]);
            }
        }
        return response()->json(['show_notification' => false]);
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\Product;
use App\Models\User;
use App\Models\BusinessSetting;
use App\Mail\NewChatMessage;
use Auth;
use DB;
use Mail;

class UserChatController extends Controller
{
    /**
     * 显示用户的对话列表
     */
    public function index()
    {
        if (BusinessSetting::where('type', 'conversation_system')->first()->value != 1) {
            flash(translate('Conversation system is disabled'))->warning();
            return redirect()->back();
        }

        $conversations = Conversation::where('sender_id', Auth::id())
            ->orWhere('receiver_id', Auth::id())
            ->with(['sender', 'receiver'])
            ->orderBy('updated_at', 'desc')
            ->paginate(10);

        return view('frontend.user.chat.index', compact('conversations'));
    }

    /**
     * 显示具体的对话
     */
    public function show($id)
    {
        $conversation = Conversation::with(['messages.user', 'sender', 'receiver'])
            ->findOrFail(decrypt($id));

        // 检查用户权限
        if ($conversation->sender_id != Auth::id() && $conversation->receiver_id != Auth::id()) {
            abort(403);
        }

        // 标记为已读
        $conversation->update([
            'sender_viewed' => $conversation->sender_id == Auth::id() ? 1 : 0,
            'receiver_viewed' => $conversation->receiver_id == Auth::id() ? 1 : 0,
        ]);

        return view('frontend.user.chat.show', compact('conversation'));
    }

    /**
     * 创建新对话（从产品页面）
     */
    public function createFromProduct(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'message' => 'required|string|max:1000'
        ]);

        $product = Product::findOrFail($request->product_id);
        
        // 检查是否已存在对话
        $existingConversation = Conversation::where('sender_id', Auth::id())
            ->where('receiver_id', $product->user_id)
            ->first();

        if (!$existingConversation) {
            $existingConversation = Conversation::where('sender_id', $product->user_id)
                ->where('receiver_id', Auth::id())
                ->first();
        }

        if ($existingConversation) {
            // 如果已存在对话，添加新消息
            Message::create([
                'conversation_id' => $existingConversation->id,
                'user_id' => Auth::id(),
                'message' => $request->message,
                'message_type' => 'text',
                'is_read' => false
            ]);
            return redirect()->route('user.chat.show', encrypt($existingConversation->id));
        }

        // 创建新对话
        $conversation = Conversation::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $product->user_id,
            'title' => $product->name,
            'sender_viewed' => 1,
            'receiver_viewed' => 0
        ]);

        // 添加初始消息
        Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'message_type' => 'text',
            'is_read' => false
        ]);

        flash(translate('Message sent successfully'))->success();
        return redirect()->route('user.chat.show', encrypt($conversation->id));
    }

    /**
     * 发送消息
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'message' => 'required|string|max:1000'
        ]);

        $conversation = Conversation::findOrFail($request->conversation_id);
        
        // 检查权限
        if ($conversation->sender_id != Auth::id() && $conversation->receiver_id != Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'message_type' => 'text',
            'is_read' => false
        ]);

        // 更新对话时间
        $conversation->update(['updated_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'user_name' => Auth::user()->name,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'is_own' => true
            ]
        ]);
    }

    /**
     * 分享产品到对话
     */
    public function shareProduct(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'product_id' => 'required|exists:products,id'
        ]);

        $conversation = Conversation::findOrFail($request->conversation_id);
        $product = Product::findOrFail($request->product_id);
        
        // 检查权限
        if ($conversation->sender_id != Auth::id() && $conversation->receiver_id != Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // 准备产品数据
        $productData = [
            'id' => $product->id,
            'name' => $product->name,
            'slug' => $product->slug,
            'thumbnail' => uploaded_asset($product->thumbnail_img),
            'price' => home_discounted_price($product),
            'original_price' => home_price($product),
            'rating' => $product->rating,
            'url' => route('product', $product->slug)
        ];

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => Auth::id(),
            'message' => '分享了产品: ' . $product->name,
            'message_type' => 'product',
            'message_data' => $productData,
            'is_read' => false
        ]);

        // 更新对话
        $conversation->update([
            'last_message_at' => now(),
            'sender_viewed' => $conversation->sender_id == Auth::id() ? 1 : 0,
            'receiver_viewed' => $conversation->receiver_id == Auth::id() ? 1 : 0,
        ]);

        return response()->json([
            'success' => true,
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'message_data' => $message->message_data,
                'user_name' => $message->user->name,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'is_own' => true
            ]
        ]);
    }

    /**
     * 获取新消息（AJAX轮询）
     */
    public function getNewMessages(Request $request)
    {
        $conversationId = $request->conversation_id;
        $lastMessageId = $request->last_message_id;

        $conversation = Conversation::findOrFail($conversationId);
        
        // 检查权限
        if ($conversation->sender_id != Auth::id() && $conversation->receiver_id != Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $newMessages = Message::where('conversation_id', $conversationId)
            ->where('id', '>', $lastMessageId)
            ->with('user')
            ->orderBy('created_at', 'asc')
            ->get();

        $messages = $newMessages->map(function($message) {
            return [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'message_data' => $message->message_data,
                'user_name' => $message->user->name,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'is_own' => $message->user_id == Auth::id()
            ];
        });

        // 标记新消息为已读
        if ($newMessages->count() > 0) {
            $conversation->update([
                'sender_viewed' => $conversation->sender_id == Auth::id() ? 1 : 0,
                'receiver_viewed' => $conversation->receiver_id == Auth::id() ? 1 : 0,
            ]);
        }

        return response()->json([
            'success' => true,
            'messages' => $messages
        ]);
    }





    /**
     * 获取产品列表用于分享
     */
    public function getProducts(Request $request)
    {
        $search = $request->get('search', '');
        $limit = $request->get('limit', 20);

        $query = Product::where('published', 1)
            ->where('approved', 1);

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $products = $query->select('id', 'name', 'slug', 'thumbnail_img', 'unit_price', 'discount', 'rating')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        $productData = $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'thumbnail' => uploaded_asset($product->thumbnail_img),
                'price' => home_discounted_price($product),
                'original_price' => home_price($product),
                'rating' => $product->rating,
                'url' => route('product', $product->slug)
            ];
        });

        return response()->json([
            'success' => true,
            'products' => $productData
        ]);
    }


}

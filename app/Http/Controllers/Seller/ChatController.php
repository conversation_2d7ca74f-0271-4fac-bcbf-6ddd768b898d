<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use DB;

class ChatController extends Controller
{
    /**
     * 获取卖家与admin的对话
     */
    public function getConversations()
    {
        $seller_id = Auth::id();
        
        // 调用chat插件的数据库获取对话
        try {
            // 连接到chat插件的数据库
            $chat_db = $this->getChatDatabase();
            
            // 查询卖家与admin的对话
            $conversations = $chat_db->table('sb_conversations')
                ->join('sb_messages', 'sb_conversations.id', '=', 'sb_messages.conversation_id')
                ->join('sb_users', 'sb_messages.user_id', '=', 'sb_users.id')
                ->where(function($query) use ($seller_id) {
                    $query->where('sb_conversations.sender_id', $seller_id)
                          ->orWhere('sb_conversations.receiver_id', $seller_id);
                })
                ->where(function($query) {
                    $query->where('sb_conversations.sender_id', 1) // admin ID
                          ->orWhere('sb_conversations.receiver_id', 1);
                })
                ->select([
                    'sb_messages.id as message_id',
                    'sb_messages.message',
                    'sb_messages.creation_time',
                    'sb_users.first_name',
                    'sb_users.last_name',
                    'sb_users.user_type',
                    'sb_conversations.id as conversation_id'
                ])
                ->orderBy('sb_messages.creation_time', 'desc')
                ->limit(50)
                ->get();
                
            return response()->json([
                'success' => true,
                'conversations' => $conversations
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load conversations: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 发送消息给admin
     */
    public function sendMessage(Request $request)
    {
        $seller_id = Auth::id();
        $message = $request->input('message');
        $admin_id = 1; // admin用户ID
        
        if (empty($message)) {
            return response()->json([
                'success' => false,
                'message' => 'Message cannot be empty'
            ]);
        }
        
        try {
            $chat_db = $this->getChatDatabase();
            
            // 获取或创建对话
            $conversation_id = $this->getOrCreateConversation($chat_db, $seller_id, $admin_id);
            
            // 确保卖家用户在chat系统中存在
            $this->ensureSellerUserExists($chat_db, $seller_id);
            
            // 插入消息
            $message_id = $chat_db->table('sb_messages')->insertGetId([
                'user_id' => $seller_id,
                'conversation_id' => $conversation_id,
                'message' => $message,
                'creation_time' => now()->format('Y-m-d H:i:s'),
                'status_code' => 0
            ]);
            
            // 更新对话状态
            $chat_db->table('sb_conversations')
                ->where('id', $conversation_id)
                ->update([
                    'sender_viewed' => 1,
                    'receiver_viewed' => 0
                ]);
            
            return response()->json([
                'success' => true,
                'message_id' => $message_id,
                'conversation_id' => $conversation_id
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取chat插件的数据库连接
     */
    private function getChatDatabase()
    {
        // 读取chat插件的配置
        $config_path = base_path('chat/config.php');
        if (!file_exists($config_path)) {
            throw new \Exception('Chat plugin config not found');
        }
        
        // 解析配置文件获取数据库信息
        $config_content = file_get_contents($config_path);
        preg_match("/define\('SB_DB_NAME', '(.+?)'\)/", $config_content, $db_name);
        preg_match("/define\('SB_DB_USER', '(.+?)'\)/", $config_content, $db_user);
        preg_match("/define\('SB_DB_PASSWORD', '(.+?)'\)/", $config_content, $db_password);
        preg_match("/define\('SB_DB_HOST', '(.+?)'\)/", $config_content, $db_host);
        
        if (empty($db_name[1]) || empty($db_user[1]) || empty($db_host[1])) {
            throw new \Exception('Invalid chat database configuration');
        }
        
        // 创建数据库连接
        $chat_db = DB::connection()->getPdo();
        $chat_db = new \PDO(
            "mysql:host={$db_host[1]};dbname={$db_name[1]};charset=utf8mb4",
            $db_user[1],
            $db_password[1] ?? '',
            [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
            ]
        );
        
        // 返回Laravel DB facade包装的连接
        return DB::connection('mysql')->table('dummy'); // 这里需要重新配置
    }
    
    /**
     * 获取或创建对话
     */
    private function getOrCreateConversation($chat_db, $seller_id, $admin_id)
    {
        // 查找现有对话
        $conversation = $chat_db->table('sb_conversations')
            ->where(function($query) use ($seller_id, $admin_id) {
                $query->where('sender_id', $seller_id)->where('receiver_id', $admin_id);
            })
            ->orWhere(function($query) use ($seller_id, $admin_id) {
                $query->where('sender_id', $admin_id)->where('receiver_id', $seller_id);
            })
            ->first();
            
        if ($conversation) {
            return $conversation->id;
        }
        
        // 创建新对话
        return $chat_db->table('sb_conversations')->insertGetId([
            'sender_id' => $seller_id,
            'receiver_id' => $admin_id,
            'title' => 'Seller Support Chat',
            'status_code' => 0,
            'creation_time' => now()->format('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 确保卖家用户在chat系统中存在
     */
    private function ensureSellerUserExists($chat_db, $seller_id)
    {
        $seller = Auth::user();
        
        // 检查用户是否已存在
        $existing_user = $chat_db->table('sb_users')->where('id', $seller_id)->first();
        
        if (!$existing_user) {
            // 创建用户
            $chat_db->table('sb_users')->insert([
                'id' => $seller_id,
                'first_name' => $seller->name,
                'last_name' => '',
                'email' => $seller->email,
                'user_type' => 'seller',
                'creation_time' => now()->format('Y-m-d H:i:s'),
                'last_activity' => now()->format('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 显示chat页面
     */
    public function index()
    {
        return view('seller.chat.index');
    }
}

<?php

namespace App\Http\Controllers\Seller;

use App\Models\Order;
use App\Models\ProductStock;
use App\Models\SmsTemplate;
use App\Models\User;
use App\Utility\NotificationUtility;
use App\Utility\SmsUtility;
use Illuminate\Http\Request;
use Auth;
use DB;
use Illuminate\Support\Carbon;
use function dd;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource to seller.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $payment_status = null;
        $delivery_status = null;
        $sort_search = null;
        $orders = DB::table('orders')
            ->orderBy('id', 'desc')
            ->where('seller_id', Auth::user()->id)
            // ->where('date','>=',time())
            ->select('orders.id')
            ->distinct();

        if ($request->payment_status != null) {
            $orders = $orders->where('payment_status', $request->payment_status);
            $payment_status = $request->payment_status;
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }

        $orders = $orders->paginate(15);

        foreach ($orders as $key => $value) {
            $order = Order::find($value->id);
            $order->viewed = 1;
            $order->save();
        }
        return view('seller.orders.index', compact('orders', 'payment_status', 'delivery_status', 'sort_search'));
    }

    public function show( $id ) {
        $order = Order::findOrFail(decrypt($id));
        $order_shipping_address = json_decode($order->shipping_address);
        $delivery_boys = User::where('city', $order_shipping_address->city)
            ->where('user_type', 'delivery_boy')
            ->get();

        $tpwd = Auth::user()->tpwd;
        $express = '';
        if ( $order->express_info )
        {
            $express = json_decode($order->express_info);
        }

        error_reporting(0);


        $order->viewed = 1;
        $order->save();
        return view('seller.orders.show', compact('order', 'delivery_boys', 'express', 'tpwd'));
    }

    // 钱包余额支付货款
    public function buy_package(Request $request)
    {
        
    }
    public function paymentForStorehouseProductAmount(Request $request)
    {
        // 验证和获取订单
        if (!$request->filled('order_id')) return response()->json(['success' => 0, 'message' => translate('Something went wrong!')]);
        $orderId = decrypt($request->order_id);
        $order = Order::findOrFail($orderId);
        if (!$order || $order->product_storehouse_total <= 0) return response()->json(['success' => 0, 'message' => translate('Something went wrong!')]);
        if ($order->product_storehouse_status == 1) return response()->json(['success' => 0, 'message' => translate('Payment completed')]);

        DB::beginTransaction();
        $shop = $order->shop;
        $user = $shop->user;

        if ($user->balance >= $order->product_storehouse_total) {
            // 扣除卖家余额
            $user->balance -= $order->product_storehouse_total;
            $user->save();

            // 增加店铺的admin_to_pay值
            $shop->admin_to_pay += $order->grand_total;
            $shop->save();
            
            // 添加钱包记录 - 新增部分
            $wallet = new \App\Models\Wallet;
            $wallet->user_id = $user->id;
            $wallet->amount = -1 * $order->product_storehouse_total;
            $wallet->payment_method = 'wallet';
            $wallet->payment_details = 'payment_for_storehouse_products|'.$order->code;
            $wallet->save();

            // 保存订单冻结资金过期时间
            $freezeDays = get_setting('frozen_funds_unfrozen_days', 15);
            $order->freeze_expired_at = Carbon::now()->addDays($freezeDays)->timestamp;
            
            // 添加这一行 - 设置提货时间为当前时间
            $order->product_storehouse_time = now();

            $order->product_storehouse_status = 1;
            $order->save();

            DB::commit();
            return response()->json(['success' => 1, 'message' => translate('Payment completed')]);
        }
        DB::rollBack();
        return response()->json(['success' => 0, 'message' => translate('Insufficient balance')]);
    }

    // Update Delivery Status
    public function update_delivery_status(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->delivery_viewed = '0';
        $order->delivery_status = $request->status;
        $order->save();

        if ($request->status == 'cancelled' && $order->payment_type == 'wallet' && $order->picking_switch==1) {
            $user = User::where('id', $order->user_id)->first();
            $user->balance += $order->grand_total;
            $user->save();
        }

//        if ($request->status == 'delivered') {
//            product_storehouse_order_free_up($order);
//        }

        foreach ($order->orderDetails->where('seller_id', Auth::user()->id) as $key => $orderDetail) {
            $orderDetail->delivery_status = $request->status;
            $orderDetail->save();

            if ($request->status == 'cancelled') {
                $variant = $orderDetail->variation;
                if ($orderDetail->variation == null) {
                    $variant = '';
                }

                $product_stock = ProductStock::where('product_id', $orderDetail->product_id)
                    ->where('variant', $variant)
                    ->first();

                if ($product_stock != null) {
                    $product_stock->qty += $orderDetail->quantity;
                    $product_stock->save();
                }
            }
        }

        if (addon_is_activated('otp_system') && SmsTemplate::where('identifier', 'delivery_status_change')->first()->status == 1) {
            try {
                SmsUtility::delivery_status_change(json_decode($order->shipping_address)->phone, $order);
            } catch (\Exception $e) {

            }
        }

        //sends Notifications to user
        NotificationUtility::sendNotification($order, $request->status);
        if (get_setting('google_firebase') == 1 && $order->user->device_token != null) {
            $request->device_token = $order->user->device_token;
            $request->title = "Order updated !";
            $status = str_replace("_", "", $order->delivery_status);
            $request->text = " Your order {$order->code} has been {$status}";

            $request->type = "order";
            $request->id = $order->id;
            $request->user_id = $order->user->id;

            NotificationUtility::sendFirebaseNotification($request);
        }


        if (addon_is_activated('delivery_boy')) {
            if (Auth::user()->user_type == 'delivery_boy') {
                $deliveryBoyController = new DeliveryBoyController;
                $deliveryBoyController->store_delivery_history($order);
            }
        }

        return 1;
    }

    // Update Payment Status
    public function update_payment_status(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->payment_status_viewed = '0';
        $order->save();

        foreach ($order->orderDetails->where('seller_id', Auth::user()->id) as $key => $orderDetail) {
            $orderDetail->payment_status = $request->status;
            $orderDetail->save();
        }

        $status = 'paid';
        foreach ($order->orderDetails as $key => $orderDetail) {
            if ($orderDetail->payment_status != 'paid') {
                $status = 'unpaid';
            }
        }
        $order->payment_status = $status;
        $order->save();


        if ($order->payment_status == 'paid' && $order->commission_calculated == 0) {
            calculateCommissionAffilationClubPoint($order);
        }

        //sends Notifications to user
        NotificationUtility::sendNotification($order, $request->status);
        if (get_setting('google_firebase') == 1 && $order->user->device_token != null) {
            $request->device_token = $order->user->device_token;
            $request->title = "Order updated !";
            $status = str_replace("_", "", $order->payment_status);
            $request->text = " Your order {$order->code} has been {$status}";

            $request->type = "order";
            $request->id = $order->id;
            $request->user_id = $order->user->id;

            NotificationUtility::sendFirebaseNotification($request);
        }


        if (addon_is_activated('otp_system') && SmsTemplate::where('identifier', 'payment_status_change')->first()->status == 1) {
            try {
                SmsUtility::payment_status_change(json_decode($order->shipping_address)->phone, $order);
            } catch (\Exception $e) {

            }
        }
        return 1;
    }

    public function bulkPickup(Request $request)
    {
        try {
            $seller_id = Auth::user()->id;
            
            // 获取用户的交易密码
            $user = Auth::user();
            $tpwd = $user->tpwd;
            
            // 验证交易密码
            if (!$tpwd) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Please set your transaction password first'),
                    'redirect' => '/seller/transaction'
                ]);
            }
            
            // 验证输入的密码
            if (isset($request->tpwd)) {
                if (md5($request->tpwd) != $tpwd) {
                    return response()->json([
                        'success' => false,
                        'message' => translate('Transaction password is incorrect')
                    ]);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => translate('Please provide transaction password')
                ]);
            }
            
            // 获取所有符合条件的未提货订单 (增加过滤条件)
            $unpicked_orders = \App\Models\Order::where('seller_id', $seller_id)
                ->where('product_storehouse_status', 0)
                ->where('product_storehouse_total', '>', 0)
                ->where('delivery_status', '!=', 'cancelled') // 排除已取消
                ->where('payment_status', 'paid')         // 只包括已支付
                ->get();
            
            if (count($unpicked_orders) == 0) {
                return response()->json([
                    'success' => false,
                    'message' => translate('No unpicked orders found')
                ]);
            }
            
            // 计算符合条件的订单的总仓库费用和总金额
            $total_storehouse_cost = 0;
            $total_grand_total = 0;
            foreach ($unpicked_orders as $order) {
                $total_storehouse_cost += $order->product_storehouse_total;
                $total_grand_total += $order->grand_total;
            }
            
            // 检查余额是否足够
            if ($user->balance < $total_storehouse_cost) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Insufficient balance. Please recharge your wallet.')
                ]);
            }
            
            // 开始事务处理
            DB::beginTransaction();
            
            try {
                // 扣除钱包余额
                $user->balance -= $total_storehouse_cost;
                $user->save();
                
                // 获取店铺并增加admin_to_pay值 - 关键修改
                $shop = \App\Models\Shop::where('user_id', $seller_id)->first();
                if ($shop) {
                    $shop->admin_to_pay += $total_grand_total;
                    $shop->save();
                }
                
                // 设置冻结时间并更新符合条件的订单状态
                $freezeDays = get_setting('frozen_funds_unfrozen_days', 15);
                $freeze_time = Carbon::now()->addDays($freezeDays)->timestamp;
                
                // 创建钱包历史记录
                $wallet = new \App\Models\Wallet;
                $wallet->user_id = $seller_id;
                $wallet->amount = -1 * $total_storehouse_cost;
                $wallet->payment_method = 'wallet';
                $wallet->payment_details = 'bulk_pickup_for_storehouse_products';
                $wallet->save();
                
                // 更新所有符合条件的订单
                foreach ($unpicked_orders as $order) {
                    $order->product_storehouse_status = 1;
                    $order->freeze_expired_at = $freeze_time;
                    $order->product_storehouse_time = now();
                    $order->save();
                }
                
                // 提交事务
                DB::commit();
                
                return response()->json([
                    'success' => true,
                    'message' => translate('All orders have been picked up successfully')
                ]);
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            \Log::error('批量提货错误: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => translate('System error, please try again later')
            ]);
        }
    }

    /**
     * 检查用户是否设置了交易密码
     */
    public function checkTpwd()
    {
        try {
            $user = Auth::user();
            return response()->json([
                'has_tpwd' => !empty($user->tpwd)
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('检查交易密码错误: ' . $e->getMessage());
            
            return response()->json([
                'has_tpwd' => false,
                'error' => $e->getMessage()
            ], 200); // 即使有错误也返回200，在前端处理
        }
    }


}

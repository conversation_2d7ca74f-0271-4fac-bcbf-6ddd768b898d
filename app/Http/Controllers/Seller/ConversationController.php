<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\BusinessSetting;
use App\Models\Message;
use App\Models\ProductQuery;
use App\Models\Product;
use Auth;
use phpDocumentor\Fileset\Collection;
use function response;
use function strtotime;
use function translate;

class ConversationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (BusinessSetting::where('type', 'conversation_system')->first()->value == 1) {
            $conversations = Conversation::where('sender_id', Auth::user()->id)->orWhere('receiver_id', Auth::user()->id)->orderBy('created_at', 'desc')->paginate(5);

            // 遍历对话，尝试查找关联的商品并添加 slug 和缩略图
            foreach ($conversations as $conversation) {
                // // ㊛二次㊐开㊊发程序搭㊣建刷单㊤资金盘彩票㊥盘游戏理㊦财TG：@aiziziyuan，T㊧G：@laipiaozi www.l㊨aipiaozi.com（请㊒自行忽略中间符号㊫！)abcdefghijklmnopqr㊑stuvwxyz㊓㊔㊕㊖㊗㊘㊜㊝㊞
                // 假设商品名称存储在 title 字段, 缩略图字段为 thumbnail_img
                $product = Product::where('name', $conversation->title)
                                  ->select('slug', 'thumbnail_img') // <-- 同时选择 slug 和 thumbnail_img
                                  ->first();
                // 如果找到商品，将 slug 和 thumbnail_img 添加到 conversation 对象
                $conversation->product_slug = $product ? $product->slug : null;
                $conversation->product_thumbnail_img = $product ? $product->thumbnail_img : null; // <-- 添加缩略图
            }

            return view('seller.conversations.index', compact('conversations'));
        } else {
            flash(translate('Conversation is disabled at this moment'))->warning();
            return back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $conversation = Conversation::findOrFail(decrypt($id));
        if ($conversation->sender_id == Auth::user()->id) {
            $conversation->sender_viewed = 1;
        } elseif ($conversation->receiver_id == Auth::user()->id) {
            $conversation->receiver_viewed = 1;
        }
        $conversation->save();
        
        // 添加与index方法相同的产品查询逻辑
        // 假设商品名称存储在 title 字段, 缩略图字段为 thumbnail_img
        $product = Product::where('name', $conversation->title)
                          ->select('slug', 'thumbnail_img')
                          ->first();
        // 如果找到商品，将 slug 和 thumbnail_img 添加到 conversation 对象
        $conversation->product_slug = $product ? $product->slug : null;
        $conversation->product_thumbnail_img = $product ? $product->thumbnail_img : null;
        
        return view('seller.conversations.show', compact('conversation'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function refresh(Request $request)
    {
        $conversation = Conversation::findOrFail(decrypt($request->id));
        foreach($conversation->messages as $message) {
            if( $message->user_id != Auth::user()->id ) {
                $message->updated_at = date('Y-m-d H:i:s');
                $message->save();
            }
        }
        if ($conversation->sender_id == Auth::user()->id) {
            $conversation->sender_viewed = 1;
            $conversation->save();
        } else {
            $conversation->receiver_viewed = 1;
            $conversation->save();
        }
        return view('frontend.partials.messages', compact('conversation'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function message_store(Request $request)
    {
        $message = new Message;
        $message->conversation_id = $request->conversation_id;
        $message->user_id = Auth::user()->id;
        $message->message = $request->message;
        $message->save();
        $conversation = $message->conversation;
        if ($conversation->sender_id == Auth::user()->id) {
            $conversation->receiver_viewed = "1";
        } elseif ($conversation->receiver_id == Auth::user()->id) {
            $conversation->sender_viewed = "1";
        }
        $conversation->save();
        return back();
    }

    public function message_count(Request $request){
        $conversations = \App\Models\Conversation::where('sender_id', Auth::user()->id)
            ->orWhere('receiver_id', Auth::user()->id)
            ->with('messages')
            ->get();
        $count = 0;
        foreach ($conversations as $k=>$v){
            if($v->sender_id === Auth::user()->id && $v->sender_viewed==0) $count++;
            if($v->receiver_id === Auth::user()->id  && $v->receiver_viewed==0) $count++;
            foreach ($v->messages as &$vv){
                if(strtotime($vv->created_at) === strtotime($vv->updated_at) && $vv->user_id != Auth::user()->id) {
                    $count++;
                }
            }
        }
        return response()->json([
            'result' => $count,
        ]);
    }

}

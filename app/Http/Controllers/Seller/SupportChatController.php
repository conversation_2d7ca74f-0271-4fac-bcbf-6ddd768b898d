<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Auth;
use Illuminate\Support\Facades\Log;
use Exception;
use PDO;
use Storage;

class SupportChatController extends Controller
{
    public function index()
    {
        try {
            // 简化的实现，避免复杂的错误处理
            return view('seller.support_chat_simple', [
                'seller' => Auth::user()
            ]);

        } catch (\Exception $e) {
            // 如果视图加载失败，返回基本的HTML响应
            return response()->view('seller.support_chat_fallback', [
                'error' => 'Support chat is temporarily unavailable.',
                'message' => 'Please try again later or contact support directly.'
            ], 200);
        }
    }

    /**
     * 检查Support Board是否可用
     */
    private function checkSupportBoardAvailability()
    {
        try {
            $chatConfigPath = base_path('chat/config.php');
            $chatMainJs = base_path('chat/js/main.js');

            // 检查关键文件是否存在
            if (!file_exists($chatConfigPath) || !file_exists($chatMainJs)) {
                Log::warning('Support Board: Required files not found', [
                    'config_exists' => file_exists($chatConfigPath),
                    'main_js_exists' => file_exists($chatMainJs)
                ]);
                return false;
            }

            // 检查配置文件是否可读
            require_once $chatConfigPath;

            if (!defined('SB_DB_NAME') || !defined('SB_DB_HOST') || !defined('SB_DB_USER')) {
                Log::warning('Support Board: Database constants not defined');
                return false;
            }

            return true;

        } catch (Exception $e) {
            Log::error('Support Board availability check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 确保卖家用户在Support Board系统中存在
     */
    private function ensureSellerInSupportBoard()
    {
        try {
            $seller = Auth::user();
            $chat_config_path = base_path('chat/config.php');

            if (!file_exists($chat_config_path)) {
                \Log::warning('Support Board: Config file not found');
                return false;
            }

            require_once $chat_config_path;

            if (!defined('SB_DB_NAME') || !defined('SB_DB_HOST') || !defined('SB_DB_USER')) {
                \Log::warning('Support Board: Database constants not defined');
                return false;
            }

            $dsn = 'mysql:host=' . SB_DB_HOST . ';dbname=' . SB_DB_NAME . ';charset=utf8mb4';
            $options = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_TIMEOUT => 5,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
            ];

            $pdo = new \PDO($dsn, SB_DB_USER, SB_DB_PASSWORD, $options);

            // 检查用户是否存在
            $stmt = $pdo->prepare("SELECT id FROM sb_users WHERE id = ? OR email = ? LIMIT 1");
            $stmt->execute([$seller->id, $seller->email]);
            $existing_user = $stmt->fetch();

            if (!$existing_user) {
                // 创建用户
                $name_parts = explode(' ', trim($seller->name), 2);
                $first_name = $name_parts[0] ?? $seller->name;
                $last_name = $name_parts[1] ?? '';

                $stmt = $pdo->prepare("
                    INSERT INTO sb_users (id, first_name, last_name, email, user_type, creation_time, last_activity)
                    VALUES (?, ?, ?, ?, 'seller', NOW(), NOW())
                    ON DUPLICATE KEY UPDATE
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    email = VALUES(email),
                    last_activity = NOW()
                ");

                $stmt->execute([$seller->id, $first_name, $last_name, $seller->email]);
                \Log::info('Support Board: Created seller user', ['seller_id' => $seller->id]);
            } else {
                // 更新最后活动时间
                $stmt = $pdo->prepare("UPDATE sb_users SET last_activity = NOW() WHERE id = ? LIMIT 1");
                $stmt->execute([$seller->id]);
            }

            return true;

        } catch (\Exception $e) {
            \Log::error('Support Board integration error: ' . $e->getMessage());
            return false;
        }
    }
    
    public function sendMessage(Request $request)
    {
        try {
            $message = $request->input('message');
            $seller = Auth::user();

            \Log::info('Support chat send message request', [
                'seller_id' => $seller->id,
                'message' => $message,
                'has_file' => $request->hasFile('file')
            ]);

            if (empty($message)) {
                return response()->json(['success' => false, 'error' => 'Message is required']);
            }
            
            // 处理文件上传
            $attachment_url = null;
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('chat_attachments', $filename, 'public');
                $attachment_url = Storage::url($path);
            }
            
            // 简化版本：直接记录到日志，避免数据库连接问题
            \Log::info('Support chat message from seller', [
                'seller_id' => $seller->id,
                'seller_name' => $seller->name,
                'message' => $message,
                'has_attachment' => !is_null($attachment_url),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully. Our support team will respond soon.',
                'data' => [
                    'sender' => $seller->name,
                    'message' => $message,
                    'timestamp' => now()->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Support chat error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'Failed to send message: ' . $e->getMessage()]);
        }
    }
    
    public function getMessages(Request $request)
    {
        try {
            $seller = Auth::user();

            // 简化版本：返回示例消息
            $messages = [
                [
                    'id' => 1,
                    'user_id' => 0, // 管理员消息
                    'message' => 'Welcome to support chat! How can we help you today?',
                    'creation_time' => now()->subMinutes(5)->format('Y-m-d H:i:s'),
                    'is_admin' => true
                ]
            ];

            return response()->json(['success' => true, 'messages' => $messages]);

        } catch (\Exception $e) {
            \Log::error('Support chat error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'An error occurred while loading messages']);
        }
    }
}

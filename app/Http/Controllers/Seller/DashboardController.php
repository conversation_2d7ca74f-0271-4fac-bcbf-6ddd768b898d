<?php

namespace App\Http\Controllers\Seller;

use App\Models\Order;
use App\Models\Product;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Session;
use function dd;
use Illuminate\Support\Facades\Cache;
use App\Models\OrderDetail;
use App\Models\Category;

class DashboardController extends Controller
{
    const CACHE_DURATION = 0; // 5分钟缓存

    public function index(Request $request)
    {
        $userId = Auth::id();

        // 处理会话状态
        if ($request->root() . '/users/login' === url()->previous()
            && Session::get('conversations-modal') !== '0') {
            Session::put('conversations-modal', '1');
        }

        // 检查商铺审核状态并设置相应的通知消息
        $user = Auth::user();
        if ($user && $user->shop) {
            $shop = $user->shop;

            // 检查是否需要显示审核状态通知
            if (!session()->has('shop_status_checked_' . $shop->id)) {
                if ($shop->verification_status == 0) {
                    // 审核中
                    session()->flash('custom_notification', [
                        'message' => translate('Your store application is pending approval. We will notify you once it is approved.'),
                        'type' => 'info'
                    ]);
                } elseif ($shop->verification_status == -1) {
                    // 被拒绝
                    $message = translate('Your store application has been rejected.');
                    if ($shop->rejection_reason) {
                        $message .= ' ' . translate('Reason') . ': ' . $shop->rejection_reason;
                    }
                    $message .= ' ' . translate('You can re-submit your application.');

                    session()->flash('custom_notification', [
                        'message' => $message,
                        'type' => 'error'
                    ]);
                }

                // 标记已检查过状态，避免重复显示
                session()->put('shop_status_checked_' . $shop->id, true);
            }
        }

        // 从缓存获取数据
        $data = Cache::remember('seller_dashboard_' . $userId, self::CACHE_DURATION, function () use ($userId) {
            // 获取所有订单状态的统计数据(使用单次查询)
            $orderStatusCounts = OrderDetail::where('seller_id', $userId)
                ->select('delivery_status', DB::raw('count(*) as count'))
                ->groupBy('delivery_status')
                ->pluck('count', 'delivery_status')
                ->toArray();

            // 获取总销售额
            $totalSales = OrderDetail::where('seller_id', $userId)
                ->whereHas('order', function($q) {
                    $q->where('payment_status', 'paid');
                })
                ->sum('price');

            // 获取月度销售数据
            $currentMonthSales = OrderDetail::where('seller_id', $userId)
                ->whereHas('order', function($q) {
                    $q->where('payment_status', 'paid')
                      ->where('product_storehouse_status', 1); // 添加已提货条件
                })
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->sum('price');

            $lastMonthSales = OrderDetail::where('seller_id', $userId)
                ->whereHas('order', function($q) {
                    $q->where('payment_status', 'paid')
                      ->where('product_storehouse_status', 1); // 添加已提货条件
                })
                ->whereBetween('created_at', [
                    Carbon::now()->subMonth()->startOfMonth(),
                    Carbon::now()->subMonth()->endOfMonth()
                ])
                ->sum('price');

            // 获取分类产品统计
            $categoryProducts = Category::with(['products' => function($q) use ($userId) {
                $q->where('user_id', $userId);
            }])
            ->whereHas('products', function($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->get()
            ->map(function($category) {
                return [
                    'name' => $category->getTranslation('name'),
                    'count' => $category->products->count()
                ];
            });

            return [
                // 基础统计数据
                'total_products' => Product::where('user_id', $userId)->count(),
                'total_orders' => $orderStatusCounts['delivered'] ?? 0,
                'shop_rating' => Auth::user()->shop->rating,
                'shop_views' => Auth::user()->shop->views,
                
                // 订单状态统计
                'order_status_counts' => [
                    'pending' => $orderStatusCounts['pending'] ?? 0,
                    'cancelled' => $orderStatusCounts['cancelled'] ?? 0,
                    'on_the_way' => $orderStatusCounts['on_the_way'] ?? 0,
                    'delivered' => $orderStatusCounts['delivered'] ?? 0
                ],
                
                // 销售统计
                'total_sales' => $totalSales,
                'current_month_sales' => $currentMonthSales,
                'last_month_sales' => $lastMonthSales,
                
                // 分类统计
                'category_products' => $categoryProducts,
                
                // 图表数据
                'last_7_days_sales' => $this->getLastSevenDaysSales($userId),
                
                // 热门产品
                'products' => Product::where('user_id', $userId)
                    ->with(['category']) // 预加载分类关系
                    ->orderBy('num_of_sale', 'desc')
                    ->limit(12)
                    ->get()
            ];
        });

        return view('seller.dashboard', $data);
    }

    private function getLastSevenDaysSales($userId)
    {
        $sales = [];
        $profits = [];
        $labels = [];
        
        // 获取过去7天的日期范围
        for($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $labels[] = $date->format('d M');
            $sales[$dateKey] = 0;
            $profits[$dateKey] = 0;
        }

        // 获取已支付且已提货订单的销售数据和利润
        $results = Order::where('seller_id', $userId)
            ->where('payment_status', 'paid')
            ->where('product_storehouse_status', 1)  // 添加已提货条件
            ->where('created_at', '>=', Carbon::now()->subDays(7)->startOfDay())
            ->select([
                'id',
                'grand_total',
                'product_storehouse_total',
                'created_at'
            ])
            ->get()
            ->groupBy(function($item) {
                return $item->created_at->format('Y-m-d');
            });

        // 填充数据
        foreach($results as $date => $orders) {
            if(isset($sales[$date])) {
                $totalSales = $orders->sum('grand_total');
                $totalProfit = $orders->sum(function($order) {
                    return $order->grand_total - $order->product_storehouse_total;
                });
                
                $sales[$date] = round($totalSales, 2);
                $profits[$date] = round($totalProfit, 2);
            }
        }

        return [
            'labels' => array_values($labels),
            'sales' => array_values($sales),
            'profits' => array_values($profits)
        ];
    }
}

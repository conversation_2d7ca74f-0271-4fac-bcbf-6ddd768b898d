<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;

class ChatApiController extends Controller
{
    /**
     * 代理chat插件的API请求
     */
    public function proxy(Request $request)
    {
        if (!Auth::check() || Auth::user()->user_type !== 'seller') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        
        $seller = Auth::user();
        
        // 确保卖家在chat系统中存在
        $this->ensureSellerExists($seller);
        
        // 获取chat插件的URL
        $chat_url = url('/chat/include/ajax.php');
        
        // 准备请求数据
        $data = $request->all();
        $data['user_id'] = $seller->id;
        $data['login-cookie'] = $this->generateSellerLoginCookie($seller);
        
        // 如果是发送消息，确保是发给admin
        if ($data['function'] === 'send-message') {
            $data['conversation_id'] = $this->getOrCreateAdminConversation($seller->id);
        }
        
        // 发送请求到chat插件
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $chat_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                return response($response)->header('Content-Type', 'application/json');
            } else {
                return response()->json(['error' => 'Chat service unavailable'], 503);
            }
            
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to connect to chat service'], 500);
        }
    }
    
    /**
     * 获取卖家与admin的对话历史
     */
    public function getConversations()
    {
        if (!Auth::check() || Auth::user()->user_type !== 'seller') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $seller = Auth::user();

        try {
            $this->ensureSellerExists($seller);
            $conversation_id = $this->getOrCreateAdminConversation($seller->id);

            // 直接从数据库获取消息，避免复杂的API调用
            $messages = $this->getMessagesFromDatabase($conversation_id);

            return response()->json([
                'success',
                [
                    'messages' => $messages,
                    'conversation_id' => $conversation_id
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Chat getConversations error: ' . $e->getMessage());
            return response()->json(['error', 'Failed to load conversations: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 发送消息给admin
     */
    public function sendMessage(Request $request)
    {
        if (!Auth::check() || Auth::user()->user_type !== 'seller') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $seller = Auth::user();
        $message = $request->input('message');

        if (empty($message)) {
            return response()->json(['error' => 'Message is required'], 400);
        }

        try {
            $this->ensureSellerExists($seller);
            $conversation_id = $this->getOrCreateAdminConversation($seller->id);

            // 直接插入消息到数据库
            $message_id = $this->insertMessageToDatabase($seller->id, $conversation_id, $message);

            return response()->json([
                'success',
                [
                    'message_id' => $message_id,
                    'conversation_id' => $conversation_id
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Chat sendMessage error: ' . $e->getMessage());
            return response()->json(['error', 'Failed to send message: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 调用chat插件API
     */
    private function callChatApi($data)
    {
        $chat_url = url('/chat/include/ajax.php');
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $chat_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                return response($response)->header('Content-Type', 'application/json');
            } else {
                return response()->json(['error' => 'Chat service unavailable'], 503);
            }
            
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to connect to chat service'], 500);
        }
    }
    
    /**
     * 获取chat数据库连接
     */
    private function getChatDatabaseConnection()
    {
        $chat_config_path = base_path('chat/config.php');
        if (!file_exists($chat_config_path)) {
            throw new \Exception('Chat config file not found');
        }

        require_once $chat_config_path;
        if (!defined('SB_DB_NAME')) {
            throw new \Exception('Chat database not configured');
        }

        return new \PDO(
            'mysql:host=' . SB_DB_HOST . ';dbname=' . SB_DB_NAME . ';charset=utf8mb4',
            SB_DB_USER,
            SB_DB_PASSWORD,
            [\PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION]
        );
    }

    /**
     * 确保卖家用户在chat系统中存在
     */
    private function ensureSellerExists($seller)
    {
        try {
            $pdo = $this->getChatDatabaseConnection();

            // 检查并创建用户
            $stmt = $pdo->prepare("
                INSERT INTO sb_users (id, first_name, last_name, email, user_type, creation_time, last_activity)
                VALUES (?, ?, ?, ?, 'seller', NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                first_name = VALUES(first_name),
                last_name = VALUES(last_name),
                email = VALUES(email),
                last_activity = NOW()
            ");

            $name_parts = explode(' ', $seller->name, 2);
            $first_name = $name_parts[0] ?? $seller->name;
            $last_name = $name_parts[1] ?? '';

            $stmt->execute([$seller->id, $first_name, $last_name, $seller->email]);

        } catch (\Exception $e) {
            \Log::warning('Failed to ensure seller exists in chat system: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取或创建与admin的对话
     */
    private function getOrCreateAdminConversation($seller_id)
    {
        try {
            $pdo = $this->getChatDatabaseConnection();

            // 查找现有对话
            $stmt = $pdo->prepare("
                SELECT id FROM sb_conversations
                WHERE (sender_id = ? AND receiver_id = 1)
                   OR (sender_id = 1 AND receiver_id = ?)
                ORDER BY id DESC LIMIT 1
            ");
            $stmt->execute([$seller_id, $seller_id]);
            $conversation = $stmt->fetch(\PDO::FETCH_ASSOC);

            if ($conversation) {
                return $conversation['id'];
            }

            // 创建新对话
            $stmt = $pdo->prepare("
                INSERT INTO sb_conversations (sender_id, receiver_id, title, status_code, creation_time)
                VALUES (?, 1, 'Seller Support', 0, NOW())
            ");
            $stmt->execute([$seller_id]);

            return $pdo->lastInsertId();

        } catch (\Exception $e) {
            \Log::error('Failed to get/create admin conversation: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成卖家的登录cookie
     */
    private function generateSellerLoginCookie($seller)
    {
        return 'seller_' . $seller->id . '_' . time();
    }

    /**
     * 从数据库获取消息
     */
    private function getMessagesFromDatabase($conversation_id)
    {
        try {
            $pdo = $this->getChatDatabaseConnection();

            $stmt = $pdo->prepare("
                SELECT m.*, u.first_name, u.last_name, u.user_type
                FROM sb_messages m
                JOIN sb_users u ON m.user_id = u.id
                WHERE m.conversation_id = ? AND m.message != ''
                ORDER BY m.creation_time ASC
            ");
            $stmt->execute([$conversation_id]);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            \Log::error('Failed to get messages from database: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 插入消息到数据库
     */
    private function insertMessageToDatabase($user_id, $conversation_id, $message)
    {
        try {
            $pdo = $this->getChatDatabaseConnection();

            $stmt = $pdo->prepare("
                INSERT INTO sb_messages (user_id, conversation_id, message, creation_time, status_code)
                VALUES (?, ?, ?, NOW(), 0)
            ");
            $stmt->execute([$user_id, $conversation_id, $message]);

            $message_id = $pdo->lastInsertId();

            // 更新对话状态
            $stmt = $pdo->prepare("
                UPDATE sb_conversations
                SET sender_viewed = 1, receiver_viewed = 0
                WHERE id = ?
            ");
            $stmt->execute([$conversation_id]);

            return $message_id;

        } catch (\Exception $e) {
            \Log::error('Failed to insert message to database: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 显示chat页面
     */
    public function index()
    {
        return view('seller.chat.index');
    }
}

<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Models\Review;
use Auth;
use DB;

class ReviewController extends Controller
{
    public function index(Request $request)
    {
        $reviews = DB::table('reviews')
                    ->orderBy('id', 'desc')
                    ->join('products', 'reviews.product_id', '=', 'products.id')
                    ->where('products.user_id', Auth::user()->id)
                    ->select('reviews.id')
                    ->distinct()
                    ->paginate(9);

        foreach ($reviews as $key => $value) {
            $review = \App\Models\Review::find($value->id);
            $review->viewed = 1;
            $review->save();
        }

        return view('seller.reviews', compact('reviews'));
    }
    public function edit(Request $request)
    {
         $reviews =\App\Models\Review::find($request->id);
         $lang = $request->lang;
         return view('seller.edit', compact('reviews', 'lang'));
    }
    public function update(Request $request)
    {
        $review = Review::findOrFail($request->id);
        $review->comment = $request->comment;
        $review->rating = $request->rating;
        $review->save();
        flash(translate('Edit successfully!'))->success();
        return back();
    }
    public function updateStatus(Request $request)
    {
        $review = Review::findOrFail($request->id);
        $review->status = $request->status;
        $review->save();
        return 1;
    }
}

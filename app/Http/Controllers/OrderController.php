<?php

namespace App\Http\Controllers;

use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\OTPVerificationController;
use Illuminate\Http\Request;
use App\Http\Controllers\ClubPointController;
use App\Models\Order;
use App\Models\Cart;
use App\Models\Address;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\CommissionHistory;
use App\Models\Color;
use App\Models\OrderDetail;
use App\Models\CouponUsage;
use App\Models\Coupon;
use App\OtpConfiguration;
use App\Models\User;
use App\Models\OrderFixed;
use App\Models\BusinessSetting;
use App\Models\CombinedOrder;
use App\Models\SmsTemplate;
use Auth;
use Illuminate\Support\Carbon;
use Session;
use DB;
use Mail;
use App\Mail\InvoiceEmailManager;
use App\Utility\NotificationUtility;
use CoreComponentRepository;
use App\Utility\SmsUtility;
use function get_setting;
use function response;
use function translate;

class OrderController extends Controller
{




     public function update_delivery_info(Request $request)
    {
       # echo "<PRE>";
        #print_r( $_POST['data'] );
        parse_str( $_POST['data'] , $arr );
        $json = json_encode( $arr );
        $order = Order::findOrFail( $arr['order_id'] );
        $order->express_info = $json;
        $order->save();
        echo 'Save Success';
    }

    /**
     * Display a listing of the resource to seller.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $payment_status = null;
        $delivery_status = null;
        $sort_search = null;
        $orders = DB::table('orders')
            ->orderBy('id', 'desc')
            //->join('order_details', 'orders.id', '=', 'order_details.order_id')
            ->where('seller_id', Auth::user()->id)
            ->select('orders.id')
            ->distinct();

        if ($request->payment_status != null) {
            $orders = $orders->where('payment_status', $request->payment_status);
            $payment_status = $request->payment_status;
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }

        // 添加客户筛选条件
        if ($request->has('customer_id') && $request->customer_id != '') {
            $orders = $orders->where('user_id', $request->customer_id);
        }

        $orders = $orders->paginate(15);

        foreach ($orders as $key => $value) {
            $order = \App\Models\Order::find($value->id);
            $order->viewed = 1;
            $order->save();
        }

        return view('seller.orders.index', compact('orders', 'payment_status', 'delivery_status', 'sort_search'));
    }

    // All Orders
    public function all_orders(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();

        $date = $request->date;
        $sort_search = null;
        $delivery_status = null;

        $orders = Order::orderBy('id', 'desc');

        // 修改这里:删除原来的customer_id筛选,添加seller_id筛选
        if ($request->has('seller_id') && $request->seller_id != '') {
            $orders = $orders->where('seller_id', $request->seller_id);
        }

        // 现有的筛选条件
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($date != null) {
            $orders = $orders->where('created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->where('created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
        }

        $orders = $orders->paginate(15);
        return view('backend.sales.all_orders.index', compact('orders', 'sort_search', 'delivery_status', 'date'));
    }

    // Storehouse Orders
    public function storehouse_orders(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();

        $date = $request->date;
        $sort_search = null;
        $delivery_status = null;

        $orders = Order::orderBy('id', 'desc');
        $orders = $orders->where('product_storehouse_total', '>', 0);
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($date != null) {
            $orders = $orders->where('created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->where('created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
        }
        $orders = $orders->paginate(15);
        return view('backend.sales.storehouse_orders.index', compact('orders', 'sort_search', 'delivery_status', 'date'));
    }

    public function storehouse_orders_show($id)
    {
        $order = Order::findOrFail(decrypt($id));
        $order_shipping_address = json_decode($order->shipping_address);
        $delivery_boys = array();
        if (isset($order_shipping_address->city)) {
            $delivery_boys = User::where('city', $order_shipping_address->city)
                ->where('user_type', 'delivery_boy')
                ->get();
        }

        return view('backend.sales.storehouse_orders.show', compact('order', 'delivery_boys'));
    }

    public function all_orders_show($id)
    {
        $order = Order::findOrFail(decrypt($id));

         $express = '';
        if( $order->express_info )
        {
          $express = json_decode($order->express_info );
        }

        error_reporting(0);


        $order_shipping_address = json_decode($order->shipping_address);
        $delivery_boys = array();
        if (isset($order_shipping_address->city)) {
            $delivery_boys = User::where('city', $order_shipping_address->city)
                ->where('user_type', 'delivery_boy')
                ->get();
        }

        return view('backend.sales.all_orders.show', compact('order', 'delivery_boys','express'));
    }

    // Inhouse Orders
    public function admin_orders(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();

        $date = $request->date;
        $payment_status = null;
        $delivery_status = null;
        $sort_search = null;
        $admin_user_id = User::where('user_type', 'admin')->first()->id;
        $orders = Order::orderBy('id', 'desc')
            ->where('seller_id', $admin_user_id);

        if ($request->payment_type != null) {
            $orders = $orders->where('payment_status', $request->payment_type);
            $payment_status = $request->payment_type;
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }
        if ($date != null) {
            $orders = $orders->whereDate('created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->whereDate('created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
        }

        $orders = $orders->paginate(15);
        return view('backend.sales.inhouse_orders.index', compact('orders', 'payment_status', 'delivery_status', 'sort_search', 'admin_user_id', 'date'));
    }

    public function show($id)
    {
        $order = Order::findOrFail(decrypt($id));
        $order_shipping_address = json_decode($order->shipping_address);
        $delivery_boys = [];
        if (isset($order_shipping_address->city) && !empty($order_shipping_address->city)) {
            $delivery_boys = User::where('city', $order_shipping_address->city)
                ->where('user_type', 'delivery_boy')
                ->get();
        }

        $order->viewed = 1;
        $order->save();
        return view('backend.sales.inhouse_orders.show', compact('order', 'delivery_boys'));
    }

    // Seller Orders
    public function seller_orders(Request $request)
    {
        CoreComponentRepository::instantiateShopRepository();

        $date = $request->date;
        $seller_id = $request->seller_id;
        $payment_status = null;
        $delivery_status = null;
        $sort_search = null;
        $admin_user_id = User::where('user_type', 'admin')->first()->id;
        $orders = Order::orderBy('code', 'desc')
            ->where('orders.seller_id', '!=', $admin_user_id);

        if ($request->payment_type != null) {
            $orders = $orders->where('payment_status', $request->payment_type);
            $payment_status = $request->payment_type;
        }
        if ($request->delivery_status != null) {
            $orders = $orders->where('delivery_status', $request->delivery_status);
            $delivery_status = $request->delivery_status;
        }
        if ($request->has('search')) {
            $sort_search = $request->search;
            $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
        }
        if ($date != null) {
            $orders = $orders->whereDate('created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->whereDate('created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
        }
        if ($seller_id) {
            $orders = $orders->where('seller_id', $seller_id);
        }

        $orders = $orders->paginate(15);
        return view('backend.sales.seller_orders.index', compact('orders', 'payment_status', 'delivery_status', 'sort_search', 'admin_user_id', 'seller_id', 'date'));
    }

    // Fixed Orders
    public function fixed_orders(Request $request)
    {
        $model = OrderFixed::orderBy('execution_time', 'desc');

        $status = '';
        if ($request->status != null) {
            $model = $model->where('status', $request->status);
            $status = $request->status;
        }

        $data = $model->paginate(15);
        return view('backend.sales.fixed_orders.index', compact('data','status'));
    }

    public function seller_orders_show($id)
    {
        $order = Order::findOrFail(decrypt($id));
        $order->viewed = 1;
        $order->save();
        return view('backend.sales.seller_orders.show', compact('order'));
    }


    // Pickup point orders
    public function pickup_point_order_index(Request $request)
    {
        $date = $request->date;
        $sort_search = null;
        $orders = Order::query();
        if (Auth::user()->user_type == 'staff' && Auth::user()->staff->pick_up_point != null) {
            $orders->where('shipping_type', 'pickup_point')
                ->where('pickup_point_id', Auth::user()->staff->pick_up_point->id)
                ->orderBy('code', 'desc');

            if ($request->has('search')) {
                $sort_search = $request->search;
                $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
            }
            if ($date != null) {
                $orders = $orders->whereDate('orders.created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->whereDate('orders.created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
            }

            $orders = $orders->paginate(15);

            return view('backend.sales.pickup_point_orders.index', compact('orders', 'sort_search', 'date'));
        } else {
            $orders->where('shipping_type', 'pickup_point')->orderBy('code', 'desc');

            if ($request->has('search')) {
                $sort_search = $request->search;
                $orders = $orders->where('code', 'like', '%' . $sort_search . '%');
            }
            if ($date != null) {
                $orders = $orders->whereDate('orders.created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->whereDate('orders.created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
            }

            $orders = $orders->paginate(15);

            return view('backend.sales.pickup_point_orders.index', compact('orders', 'sort_search', 'date'));
        }
    }

    public function pickup_point_order_sales_show($id)
    {
        if (Auth::user()->user_type == 'staff') {
            $order = Order::findOrFail(decrypt($id));
            $order_shipping_address = json_decode($order->shipping_address);
            $delivery_boys = User::where('city', $order_shipping_address->city)
                ->where('user_type', 'delivery_boy')
                ->get();

            return view('backend.sales.pickup_point_orders.show', compact('order', 'delivery_boys'));
        } else {
            $order = Order::findOrFail(decrypt($id));
            $order_shipping_address = json_decode($order->shipping_address);
            $delivery_boys = User::where('city', $order_shipping_address->city)
                ->where('user_type', 'delivery_boy')
                ->get();

            return view('backend.sales.pickup_point_orders.show', compact('order', 'delivery_boys'));
        }
    }

    /**
     * Display a single sale to admin.
     *
     * @return \Illuminate\Http\Response
     */


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $carts = Cart::where('user_id', Auth::user()->id)
            ->get();

        if ($carts->isEmpty()) {
            flash(translate('Your cart is empty'))->warning();
            return redirect()->route('home');
        }

        $address = Address::where('id', $carts[0]['address_id'])->first();

        $shippingAddress = [];
        if ($address != null) {
            $shippingAddress['name'] = Auth::user()->name;
            $shippingAddress['email'] = Auth::user()->email;
            $shippingAddress['address'] = $address->address;
            $shippingAddress['country'] = $address->country->name;
            $shippingAddress['state'] = $address->state->name;
            $shippingAddress['city'] = $address->city ? $address->city->name : '';
            $shippingAddress['postal_code'] = $address->postal_code;
            $shippingAddress['phone'] = $address->phone;
            if ($address->latitude || $address->longitude) {
                $shippingAddress['lat_lang'] = $address->latitude . ',' . $address->longitude;
            }
        }

        $combined_order = new CombinedOrder;
        $combined_order->user_id = Auth::user()->id;
        $combined_order->shipping_address = json_encode($shippingAddress);
        $combined_order->save();

        $seller_products = array();
        foreach ($carts as $cartItem) {
            $product_ids = array();
            $product = Product::find($cartItem['product_id']);
            if (isset($seller_products[$product->user_id])) {
                $product_ids = $seller_products[$product->user_id];
            }
            array_push($product_ids, $cartItem);
            $seller_products[$product->user_id] = $product_ids;
        }

        foreach ($seller_products as $seller_product) {
            $order = new Order;
            $order->combined_order_id = $combined_order->id;
            $order->user_id = Auth::user()->id;
            $order->shipping_address = $combined_order->shipping_address;

            $order->additional_info = $request->additional_info;

            $order->shipping_type = $carts[0]['shipping_type'];
            if ($carts[0]['shipping_type'] == 'pickup_point') {
                $order->pickup_point_id = $cartItem['pickup_point'];
            }
            $order->payment_type = $request->payment_option;
            $order->delivery_viewed = '0';
            $order->payment_status_viewed = '0';
            $order->code = date('Ymd-His') . rand(10, 99);
            $order->date = strtotime('now');
            $order->save();

            $subtotal = 0;
            $tax = 0;
            $shipping = 0;
            $coupon_discount = 0;

            // 商品仓库的商品货款
            $productStorehouseTotal = 0;

            //Order Details Storing
            foreach ($seller_product as $cartItem) {
                $product = Product::find($cartItem['product_id']);

                // 计算商品仓库的商品货款
                $originalProduct = null;
                if ($product->original_id) {
                    $originalProduct = Product::query()->find($product->original_id);
                    if ($originalProduct) {
                        $productStorehouseTotal += cart_product_price($cartItem, $originalProduct, false, false) * $cartItem['quantity'];
                    }
                }


                $subtotal += cart_product_price($cartItem, $product, false, false) * $cartItem['quantity'];
                $tax += cart_product_tax($cartItem, $product, false) * $cartItem['quantity'];
                $coupon_discount += $cartItem['discount'];

                $product_variation = $cartItem['variation'];

                $product_stock = $product->stocks->where('variant', $product_variation)->first();
                if ($product->digital != 1 && $cartItem['quantity'] > $product_stock->qty) {
                    flash(translate('The requested quantity is not available for ') . $product->getTranslation('name'))->warning();
                    $order->delete();
                    return redirect()->route('cart')->send();
                } elseif ($product->digital != 1) {
                    $product_stock->qty -= $cartItem['quantity'];
                    $product_stock->save();
                }

                $order_detail = new OrderDetail;
                $order_detail->order_id = $order->id;
                $order_detail->seller_id = $product->user_id;
                $order_detail->product_id = $product->id;
                $order_detail->is_storehouse_product = $product->original_id ? 1 : 0; // 是否商品仓库商品
                $order_detail->original_product_id = $product->original_id ?: null; // 原商品仓库商品ID
                $order_detail->original_product_price = $originalProduct ? $originalProduct->unit_price : null; // 原商品仓库商品价格(进货价)
                $order_detail->variation = $product_variation;
                $order_detail->price = cart_product_price($cartItem, $product, false, false) * $cartItem['quantity'];
                $order_detail->tax = cart_product_tax($cartItem, $product, false) * $cartItem['quantity'];
                $order_detail->shipping_type = $cartItem['shipping_type'];
                $order_detail->product_referral_code = $cartItem['product_referral_code'];
                $order_detail->shipping_cost = $cartItem['shipping_cost'];

                $shipping += $order_detail->shipping_cost;
                //End of storing shipping cost

                $order_detail->quantity = $cartItem['quantity'];
                $order_detail->save();

                $product->num_of_sale += $cartItem['quantity'];
                $product->save();

                $order->seller_id = $product->user_id;

                if ($product->added_by == 'seller' && $product->user->seller != null) {
                    $seller = $product->user->seller;
                    $seller->num_of_sale += $cartItem['quantity'];
                    $seller->save();
                }

                if (addon_is_activated('affiliate_system')) {
                    if ($order_detail->product_referral_code) {
                        $referred_by_user = User::where('referral_code', $order_detail->product_referral_code)->first();

                        $affiliateController = new AffiliateController;
                        $affiliateController->processAffiliateStats($referred_by_user->id, 0, $order_detail->quantity, 0, 0);
                    }
                }
            }

            $order->grand_total = $subtotal + $tax + $shipping;
            $order->product_storehouse_total = $productStorehouseTotal;

            if ($seller_product[0]->coupon_code != null) {
                // if (Session::has('club_point')) {
                //     $order->club_point = Session::get('club_point');
                // }
                $order->coupon_discount = $coupon_discount;
                $order->grand_total -= $coupon_discount;

                $coupon_usage = new CouponUsage;
                $coupon_usage->user_id = Auth::user()->id;
                $coupon_usage->coupon_id = Coupon::where('code', $seller_product[0]->coupon_code)->first()->id;
                $coupon_usage->save();
            }

            $combined_order->grand_total += $order->grand_total;

            /*添加是否开启提货*/
            $order->picking_switch = get_setting('picking_switch');
            $order->save();
            if ( get_setting('picking_switch') != 1 )
            {//如果不需要提货，直接修改订单为已提货状态
                $shop = $order->shop;
                $shop->admin_to_pay += ( $order->grand_total - $order->product_storehouse_total );
                $shop->save();
                // 保存订单冻结资金过期时间
                $freezeDays = get_setting('frozen_funds_unfrozen_days', 15);
                $order->freeze_expired_at = Carbon::now()->addDays($freezeDays)->timestamp;
                $order->product_storehouse_status = 1;
                $order->save();
            }
        }

        $combined_order->save();

        $request->session()->put('combined_order_id', $combined_order->id);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $order = Order::findOrFail($id);
        if ($order != null) {
            foreach ($order->orderDetails as $key => $orderDetail) {
                try {

                    $product_stock = ProductStock::where('product_id', $orderDetail->product_id)->where('variant', $orderDetail->variation)->first();
                    if ($product_stock != null) {
                        $product_stock->qty += $orderDetail->quantity;
                        $product_stock->save();
                    }

                } catch (\Exception $e) {

                }

                $orderDetail->delete();
            }
            $order->delete();
            flash(translate('Order has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }
        return back();
    }

    public function bulk_order_delete(Request $request)
    {
        $logFile = storage_path('logs/laravel-'.date('Y-m-d').'.log');
        file_put_contents($logFile, "\n[".date('Y-m-d H:i:s')."] ==== 开始批量删除订单 ====\n", FILE_APPEND);
        file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] 请求参数: ".json_encode($request->all(), JSON_UNESCAPED_UNICODE)."\n", FILE_APPEND);

        try {
            if($request->id) {
                foreach ($request->id as $order_id) {
                    $order = Order::findOrFail($order_id);
                    file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] 删除订单: {$order_id}\n", FILE_APPEND);
                    $order->delete();
                }
                file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] ==== 批量删除订单完成 ====\n", FILE_APPEND);
                return 1;
            }
            return 0;
        } catch (\Exception $e) {
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] 批量删除订单出错: ".$e->getMessage()."\n", FILE_APPEND);
            file_put_contents($logFile, "[".date('Y-m-d H:i:s')."] 错误堆栈: ".$e->getTraceAsString()."\n", FILE_APPEND);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function order_details(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->save();
        return view('seller.order_details_seller', compact('order'));
    }

    public function update_delivery_status(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->delivery_viewed = '0';
        $order->delivery_status = $request->status;
        $order->save();

        if ($request->status == 'delivered') {
            product_storehouse_order_free_up($order);
        }

        if ($request->status == 'cancelled' && $order->payment_type == 'wallet') {
            $user = User::where('id', $order->user_id)->first();
            $user->balance += $order->grand_total;
            $user->save();
        }

        if (Auth::user()->user_type == 'seller') {
            foreach ($order->orderDetails->where('seller_id', Auth::user()->id) as $key => $orderDetail) {
                $orderDetail->delivery_status = $request->status;
                $orderDetail->save();

                if ($request->status == 'cancelled') {
                    $variant = $orderDetail->variation;
                    if ($orderDetail->variation == null) {
                        $variant = '';
                    }

                    $product_stock = ProductStock::where('product_id', $orderDetail->product_id)
                        ->where('variant', $variant)
                        ->first();

                    if ($product_stock != null) {
                        $product_stock->qty += $orderDetail->quantity;
                        $product_stock->save();
                    }
                }
            }
        } else {
            foreach ($order->orderDetails as $key => $orderDetail) {

                $orderDetail->delivery_status = $request->status;
                $orderDetail->save();

                if ($request->status == 'cancelled') {
                    $variant = $orderDetail->variation;
                    if ($orderDetail->variation == null) {
                        $variant = '';
                    }

                    $product_stock = ProductStock::where('product_id', $orderDetail->product_id)
                        ->where('variant', $variant)
                        ->first();

                    if ($product_stock != null) {
                        $product_stock->qty += $orderDetail->quantity;
                        $product_stock->save();
                    }
                }

                if (addon_is_activated('affiliate_system')) {
                    if (($request->status == 'delivered' || $request->status == 'cancelled') &&
                        $orderDetail->product_referral_code) {

                        $no_of_delivered = 0;
                        $no_of_canceled = 0;

                        if ($request->status == 'delivered') {
                            $no_of_delivered = $orderDetail->quantity;
                        }
                        if ($request->status == 'cancelled') {
                            $no_of_canceled = $orderDetail->quantity;
                        }

                        $referred_by_user = User::where('referral_code', $orderDetail->product_referral_code)->first();

                        $affiliateController = new AffiliateController;
                        $affiliateController->processAffiliateStats($referred_by_user->id, 0, 0, $no_of_delivered, $no_of_canceled);
                    }
                }
            }
        }
        if (addon_is_activated('otp_system') && SmsTemplate::where('identifier', 'delivery_status_change')->first()->status == 1) {
            try {
                SmsUtility::delivery_status_change(json_decode($order->shipping_address)->phone, $order);
            } catch (\Exception $e) {

            }
        }

        //sends Notifications to user
        NotificationUtility::sendNotification($order, $request->status);
        if (get_setting('google_firebase') == 1 && $order->user->device_token != null) {
            $request->device_token = $order->user->device_token;
            $request->title = "Order updated !";
            $status = str_replace("_", "", $order->delivery_status);
            $request->text = " Your order {$order->code} has been {$status}";

            $request->type = "order";
            $request->id = $order->id;
            $request->user_id = $order->user->id;

            NotificationUtility::sendFirebaseNotification($request);
        }


        if (addon_is_activated('delivery_boy')) {
            if (Auth::user()->user_type == 'delivery_boy') {
                $deliveryBoyController = new DeliveryBoyController;
                $deliveryBoyController->store_delivery_history($order);
            }
        }

        return 1;
    }

    public function update_tracking_code(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->tracking_code = $request->tracking_code;
        $order->save();

        return 1;
    }

    public function update_payment_status(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        $order->payment_status_viewed = '0';
        $order->save();

        if (Auth::user()->user_type == 'seller') {
            foreach ($order->orderDetails->where('seller_id', Auth::user()->id) as $key => $orderDetail) {
                $orderDetail->payment_status = $request->status;
                $orderDetail->save();
            }
        } else {
            foreach ($order->orderDetails as $key => $orderDetail) {
                $orderDetail->payment_status = $request->status;
                $orderDetail->save();
            }
        }

        $status = 'paid';
        foreach ($order->orderDetails as $key => $orderDetail) {
            if ($orderDetail->payment_status != 'paid') {
                $status = 'unpaid';
            }
        }
        $order->payment_status = $status;
        $order->save();


        if ($order->payment_status == 'paid' && $order->commission_calculated == 0) {
            calculateCommissionAffilationClubPoint($order);
        }

        //sends Notifications to user
        NotificationUtility::sendNotification($order, $request->status);
        if (get_setting('google_firebase') == 1 && $order->user->device_token != null) {
            $request->device_token = $order->user->device_token;
            $request->title = "Order updated !";
            $status = str_replace("_", "", $order->payment_status);
            $request->text = " Your order {$order->code} has been {$status}";

            $request->type = "order";
            $request->id = $order->id;
            $request->user_id = $order->user->id;

            NotificationUtility::sendFirebaseNotification($request);
        }


        if (addon_is_activated('otp_system') && SmsTemplate::where('identifier', 'payment_status_change')->first()->status == 1) {
            try {
                SmsUtility::payment_status_change(json_decode($order->shipping_address)->phone, $order);
            } catch (\Exception $e) {

            }
        }
        return 1;
    }

    public function assign_delivery_boy(Request $request)
    {
        if (addon_is_activated('delivery_boy')) {

            $order = Order::findOrFail($request->order_id);
            $order->assign_delivery_boy = $request->delivery_boy;
            $order->delivery_history_date = date("Y-m-d H:i:s");
            $order->save();

            $delivery_history = \App\Models\DeliveryHistory::where('order_id', $order->id)
                ->where('delivery_status', $order->delivery_status)
                ->first();

            if (empty($delivery_history)) {
                $delivery_history = new \App\Models\DeliveryHistory;

                $delivery_history->order_id = $order->id;
                $delivery_history->delivery_status = $order->delivery_status;
                $delivery_history->payment_type = $order->payment_type;
            }
            $delivery_history->delivery_boy_id = $request->delivery_boy;

            $delivery_history->save();

            if (env('MAIL_USERNAME') != null && get_setting('delivery_boy_mail_notification') == '1') {
                $array['view'] = 'emails.invoice';
                $array['subject'] = translate('You are assigned to delivery an order. Order code') . ' - ' . $order->code;
                $array['from'] = env('MAIL_FROM_ADDRESS');
                $array['order'] = $order;

                try {
                    Mail::to($order->delivery_boy->email)->queue(new InvoiceEmailManager($array));
                } catch (\Exception $e) {

                }
            }

            if (addon_is_activated('otp_system') && SmsTemplate::where('identifier', 'assign_delivery_boy')->first()->status == 1) {
                try {
                    SmsUtility::assign_delivery_boy($order->delivery_boy->phone, $order->code);
                } catch (\Exception $e) {

                }
            }
        }

        return 1;
    }

    public function bulk_order_status(Request $request)
    {
        try {
            if($request->id) {
                foreach ($request->id as $order_id) {
                    $order = Order::findOrFail($order_id);
                    $oldStatus = $order->delivery_status;
                    $order->delivery_status = $request->status;
                    $order->save();

                    if ($request->status == 'delivered') {
                        product_storehouse_order_free_up($order);
                    }

                    if ($request->status == 'cancelled' && $order->payment_type == 'wallet') {
                        $user = User::where('id', $order->user_id)->first();
                        $user->balance += $order->grand_total;
                        $user->save();
                    }
                }
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }

        return 1;
    }

    public function bulk_order_free_up(Request $request)
    {
        try {
            if($request->id) {
                $success_count = 0;
                $failed_count = 0;

                foreach ($request->id as $order_id) {
                    $order = Order::findOrFail($order_id);

                    // 检查是否有冻结资金且已到期
                    if ($order->product_storehouse_total > 0 &&
                        $order->freeze_expired_at &&
                        strtotime($order->freeze_expired_at) <= time()) {

                        // 调用释放冻结资金的函数
                        $result = product_storehouse_order_free_up($order);

                        if ($result) {
                            $success_count++;
                        } else {
                            $failed_count++;
                        }
                    } else {
                        $failed_count++;
                    }
                }

                if ($success_count > 0) {
                    return response()->json([
                        'success' => true,
                        'message' => translate('Successfully released frozen funds for ').$success_count.translate(' orders').
                                    ($failed_count > 0 ? ', '.translate('failed for ').$failed_count.translate(' orders') : '')
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => translate('No orders could be processed. Please check if the freeze period has expired.')
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => translate('No orders selected')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除定时任务
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function fixed_orders_destroy($id)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $fixed_order = OrderFixed::findOrFail($id);

        // 只能删除待执行的任务
        if ($fixed_order->status != 0) {
            flash(translate('只能删除待执行的定时任务'))->error();
            return back();
        }

        $fixed_order->delete();
        flash(translate('定时任务已成功删除'))->success();
        return back();
    }

    /**
     * 批量删除定时任务
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function fixed_orders_bulk_destroy(Request $request)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        if (!$request->has('ids') || empty($request->ids)) {
            return response()->json(['message' => translate('请选择要删除的定时任务')], 400);
        }

        $count = 0;
        foreach ($request->ids as $id) {
            $fixed_order = OrderFixed::find($id);

            // 只删除存在且待执行的任务
            if ($fixed_order && $fixed_order->status == 0) {
                $fixed_order->delete();
                $count++;
            }
        }

        return response()->json(['message' => translate('成功删除') . $count . translate('个定时任务')]);
    }

    /**
     * 立即执行定时任务
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function fixed_orders_execute($id)
    {
        // 二㊌次㊒开㊓发㊔程㊕序㊖搭㊗建㊘刷㊙单㊚资㊛金㊜盘㊨彩㊫票㊬盘㊭游㊮戏㊯理㊎财㊏TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $fixed_order = OrderFixed::findOrFail($id);

        // 只能执行待执行的任务
        if ($fixed_order->status != 0) {
            flash(translate('只能执行待执行的定时任务'))->error();
            return back();
        }

        try {
            // 在这里调用执行任务的逻辑
            // 这取决于您的系统如何执行定时任务

            // 模拟任务执行
            $fixed_order->status = 1; // 成功
            $fixed_order->result = '手动执行成功 - ' . date('Y-m-d H:i:s');
            $fixed_order->save();

            flash(translate('定时任务已成功执行'))->success();
        } catch (\Exception $e) {
            $fixed_order->status = 2; // 失败
            $fixed_order->result = '执行失败: ' . $e->getMessage();
            $fixed_order->save();

            flash(translate('定时任务执行失败'))->error();
        }

        return back();
    }

    /**
     * 导出定时任务数据
     *
     * @param \Illuminate\Http\Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function fixed_orders_export(Request $request)
    {
        // 二㊌次㊒开㊓发㊔程㊕序㊖搭㊗建㊘刷㊙单㊚资㊛金㊜盘㊨彩㊫票㊬盘㊭游㊮戏㊯理㊎财㊏TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)

        $query = OrderFixed::orderBy('execution_time', 'desc');

        // 应用筛选条件
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        if ($request->has('start_date') && $request->start_date != '') {
            $start = strtotime($request->start_date . ' 00:00:00');
            $query->where('execution_time', '>=', $start);
        }

        if ($request->has('end_date') && $request->end_date != '') {
            $end = strtotime($request->end_date . ' 23:59:59');
            $query->where('execution_time', '<=', $end);
        }

        if ($request->has('search') && $request->search != '') {
            $query->where('remark', 'like', '%' . $request->search . '%');
        }

        $tasks = $query->get();

        $fileName = 'fixed_orders_' . date('Y-m-d') . '.csv';

        $headers = array(
            "Content-type"        => "text/csv",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        );

        $columns = ['ID', '创建时间', '执行时间', '状态', '备注', '执行结果'];

        $callback = function() use($tasks, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);

            foreach ($tasks as $task) {
                $status = '未知';
                if ($task->status == 0) $status = '待执行';
                elseif ($task->status == 1) $status = '成功';
                elseif ($task->status == 2) $status = '失败';

                $row = [
                    $task->id,
                    $task->created_at,
                    date('Y-m-d H:i:s', $task->execution_time),
                    $status,
                    $task->remark,
                    $task->result
                ];

                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * 创建定时任务页面
     *
     * @return \Illuminate\View\View
     */
    public function fixed_orders_create()
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        return view('backend.sales.fixed_orders.create');
    }

    /**
     * 保存新创建的定时任务
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function fixed_orders_store(Request $request)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $validated = $request->validate([
            'execution_time' => 'required',
            'task_type' => 'required',
            'remark' => 'nullable|string',
            'params' => 'nullable|string',
        ]);

        $execution_timestamp = strtotime($request->execution_time);

        $task = new OrderFixed();
        $task->execution_time = $execution_timestamp;
        $task->task_type = $request->task_type;
        $task->remark = $request->remark;
        $task->params = $request->params;
        $task->status = 0; // 待执行
        $task->save();

        flash(translate('定时任务已成功创建'))->success();
        return redirect()->route('fixed_orders');
    }

    /**
     * 显示定时任务详情
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function fixed_orders_show($id)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $task = OrderFixed::findOrFail($id);
        return view('backend.sales.fixed_orders.show', compact('task'));
    }

    /**
     * 编辑定时任务页面
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function fixed_orders_edit($id)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $task = OrderFixed::findOrFail($id);

        // 只能编辑待执行的任务
        if ($task->status != 0) {
            flash(translate('只能编辑待执行的定时任务'))->error();
            return back();
        }

        return view('backend.sales.fixed_orders.edit', compact('task'));
    }

    /**
     * 更新定时任务
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function fixed_orders_update(Request $request, $id)
    {
        // 二㊎次㊏开㊐发㊍程㊌序㊋搭㊏建㊚刷㊛单㊐资㊊金㊣盘㊤彩㊥票㊦盘㊧游㊨戏㊒理㊫财㊑TG：@a-i-z-i-z-i-y-u-a-n，TG：@-l-a-i-p-i-a-o-z-i   w-w-w-.-l-a-i-p-i-a-o-z-i-.-c-o-m-(请-自-行-去-除-中-间-符-号！)
        $task = OrderFixed::findOrFail($id);

        // 只能更新待执行的任务
        if ($task->status != 0) {
            flash(translate('只能更新待执行的定时任务'))->error();
            return back();
        }

        $validated = $request->validate([
            'execution_time' => 'required',
            'task_type' => 'required',
            'remark' => 'nullable|string',
            'params' => 'nullable|string',
        ]);

        $execution_timestamp = strtotime($request->execution_time);

        $task->execution_time = $execution_timestamp;
        $task->task_type = $request->task_type;
        $task->remark = $request->remark;
        $task->params = $request->params;
        $task->save();

        flash(translate('定时任务已成功更新'))->success();
        return redirect()->route('fixed_orders');
    }
}

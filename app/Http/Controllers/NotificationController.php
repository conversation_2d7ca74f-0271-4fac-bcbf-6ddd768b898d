<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\Message;
use Auth;

class NotificationController extends Controller
{
    public function index() {
        $notifications = auth()->user()->notifications()->paginate(15);

        auth()->user()->unreadNotifications->markAsRead();

        if(Auth::user()->user_type == 'admin') {
            return view('backend.notification.index', compact('notifications'));
        }

        if(Auth::user()->user_type == 'seller') {
            return view('seller.notification.index', compact('notifications'));
        }

        if(Auth::user()->user_type == 'customer') {
            return view('frontend.user.customer.notification.index', compact('notifications'));
        }

    }

    /**
     * 获取用户的未读消息数量
     */
    public function getUnreadChatCount()
    {
        if (!Auth::check()) {
            return response()->json(['count' => 0]);
        }

        $unreadCount = Conversation::where(function($query) {
                $query->where('sender_id', Auth::id())
                      ->orWhere('receiver_id', Auth::id());
            })
            ->get()
            ->sum(function($conversation) {
                return $conversation->getUnreadCountForUser(Auth::id());
            });

        return response()->json(['count' => $unreadCount]);
    }

    /**
     * 获取最新的未读消息
     */
    public function getLatestChatMessages(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['messages' => []]);
        }

        $limit = $request->get('limit', 5);

        $conversations = Conversation::where(function($query) {
                $query->where('sender_id', Auth::id())
                      ->orWhere('receiver_id', Auth::id());
            })
            ->whereHas('messages', function($query) {
                $query->where('user_id', '!=', Auth::id())
                      ->where('is_read', false);
            })
            ->with(['latestMessage.user', 'sender', 'receiver', 'product'])
            ->orderBy('last_message_at', 'desc')
            ->limit($limit)
            ->get();

        $messages = $conversations->map(function($conversation) {
            $otherUser = $conversation->getOtherUser(Auth::id());
            $lastMessage = $conversation->latestMessage;

            return [
                'conversation_id' => $conversation->id,
                'message' => $lastMessage ? $lastMessage->getFormattedMessage() : '',
                'sender_name' => $otherUser->user_type == 'seller' && $otherUser->shop ?
                    $otherUser->shop->name : $otherUser->name,
                'sender_avatar' => $otherUser->user_type == 'seller' && $otherUser->shop && $otherUser->shop->logo ?
                    uploaded_asset($otherUser->shop->logo) :
                    ($otherUser->avatar_original ? uploaded_asset($otherUser->avatar_original) : null),
                'product_name' => $conversation->product ? $conversation->product->name : null,
                'created_at' => $lastMessage ? $lastMessage->created_at->diffForHumans() : '',
                'url' => route('user.chat.show', encrypt($conversation->id))
            ];
        });

        return response()->json(['messages' => $messages]);
    }

    /**
     * 标记聊天消息为已读
     */
    public function markChatAsRead(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false]);
        }

        $conversationId = $request->get('conversation_id');

        if ($conversationId) {
            $conversation = Conversation::find($conversationId);
            if ($conversation) {
                $conversation->markAsReadForUser(Auth::id());
            }
        } else {
            // 标记所有对话为已读
            $conversations = Conversation::where(function($query) {
                    $query->where('sender_id', Auth::id())
                          ->orWhere('receiver_id', Auth::id());
                })->get();

            foreach ($conversations as $conversation) {
                $conversation->markAsReadForUser(Auth::id());
            }
        }

        return response()->json(['success' => true]);
    }
}

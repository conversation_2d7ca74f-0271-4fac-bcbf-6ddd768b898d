<?php

namespace App\Http\Controllers;
use App\Models\ProductStock;
use App\Http\Requests\ProductRequest;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductTranslation;
use App\Models\Category;
use App\Models\User;
use App\Models\Upload;
use App\Models\ProductTax;
use App\Models\AttributeValue;
use App\Models\Cart;
use Carbon\Carbon;
use Combinations;
use CoreComponentRepository;
use Artisan;
use Cache;
use Auth;
use Str;
use App\Services\ProductService;
use App\Services\ProductTaxService;
use App\Services\ProductFlashDealService;
use App\Services\ProductStockService;

class ProductController extends Controller
{
    protected $productService;
    protected $productTaxService;
    protected $productFlashDealService;
    protected $productStockService;

    public function __construct(
        ProductService $productService,
        ProductTaxService $productTaxService,
        ProductFlashDealService $productFlashDealService,
        ProductStockService $productStockService
    ) {
        $this->productService = $productService;
        $this->productTaxService = $productTaxService;
        $this->productFlashDealService = $productFlashDealService;
        $this->productStockService = $productStockService;
    }


    public function apicat( Request $request ) 
    {
        $categories = Category::get();
        $all = $categories->toArray();
        #echo "<PRE>";print_r( $all );exit;
        $all_cat = [];
        foreach( $all as $k => $v )
        {
            $ar = [];
            $ar['id'] = $v['id'];
            $ar['parent_id'] = $v['parent_id'];
            $ar['name'] = $v['name'];
            $ar['slug'] = $v['slug'];
            if( isset( $v['category_translations']))
            {
                foreach ( $v['category_translations'] as $kk => $v )
                {
                    $ar['name'.($kk+1)] = $v['name'];
                }
            }
            $all_cat[] = $ar;
        }
        echo json_encode( $all_cat);
        exit;
    }
    public function apicj( Request $request ) 
    {
        
        
        error_reporting(0);

        file_put_contents(dirname(__FILE__) . '/apicj_input2.txt', file_get_contents("php://input"));
        
         $json = file_get_contents("php://input");

        if ( empty($json) )
        {
            exit('data error');
        }

        $pr = json_decode($json, true);
        if ( $pr['key'] != get_setting('caiji_key') )
        {
            exit('Key Error');
        }
        
        
        echo 'ok';
    }
    
    
    
    
     public function caiji(Request $request )
    {
    
    
 
        
    error_reporting( 0 );
    file_put_contents( dirname( __FILE__ ).'/tog_post.txt', var_export($_POST, true), FILE_APPEND );
    file_put_contents( dirname( __FILE__ ).'/tog_get.txt', var_export($_GET, true), FILE_APPEND );
    file_put_contents( dirname( __FILE__ ).'/tog_input.txt', file_get_contents("php://input"), FILE_APPEND );

    $json = file_get_contents("php://input");
     
    if( empty($json))
    {
      //  exit('data error');
    }
    $pr = json_decode($json,true );
    
    
 
    
    $user_id =  $pr['uid'];
    
   // $seller = Seller::where('user_id',$user_id)->first();
    
    if( empty($seller))
    {
       // exit('seller is not exists');
    }
    
    $seller_id = 0;
    
    
    
    
        $product = new Product;
        
      
        
        $product->name = $pr['name'];
      
        
          
        $product->category_id = $pr['cat_id'];
        $product->brand_id = 0;
        $product->barcode = 0;
  
        $product->unit = 1;
       
       
       
       
        #$product->cb_price = $request->cb_price;
        #$product->xn_salenum = $request->xn_salenum;
        $product->min_qty = 1;
        $product->current_stock = 1000;
        $product->low_stock_quantity = 1;
        $product->stock_visibility_state = 1;
        $product->external_link = '';
        $product->external_link_btn ='';
        
      
        $mid = time().mt_rand(100,499);
        if( isset( $pr['photos'] ) )
        {
            foreach( $pr['photos'] as $img )
            {
            
                $upload = new Upload;
                $upload->user_id = Auth::user()->id;
                $upload->file_size = 8888;
                $upload->extension = 'jpg';
                $upload->type = 'image';
                $upload->file_original_name = $upload->file_name = $img;
                $upload->mid = $mid; 
                $upload->save();
               
                #echo $img;
                 
            }
        }
          
         $thm_id = 0;
         $upload = new Upload;
         $all =  $upload->where(['mid'=> $mid])->get()->toArray();;
        
         $ids = '';
         foreach( $all as $a => $v )
         {  if( $a == 0 )
            {
                $thm_id = $v['id'];
            }
               $ids .= $v['id'].',';
         }
         $ids = rtrim( $ids,',');
            

       
    
        $product->tags = '';
 
        $product->video_provider = ''; 
        $product->video_link =  ''; 
        $product->unit_price =$pr['price'];
        $product->discount = 0;
        $product->discount_type = 'amount';
        
        if($pr['cprice']>0) {
            
            $product->purchase_price =$pr['price']+($pr['price']*$pr['cprice']/100);
        }
  
        
 
        $mid2 =     time().mt_rand(500,999);
        $upload = new Upload;
        $upload->user_id = Auth::user()->id;
        $upload->file_size = 8888;
        $upload->extension = 'jpg';
        $upload->type = 'image';
        $upload->file_original_name = $upload->file_name = $pr['thumbnail_img'];
        $upload->mid = $mid2; 
        $upload->save();
        
        $thm_id =  $upload->where(['mid'=> $mid2])->first()->id;
 
 
        $product->thumbnail_img =  $thm_id;
        
         if($ids ==  ''){
             
             $ids = $thm_id;
         }
         
        $product->photos = $ids;

 
        $product->shipping_type = 'free';
        $product->est_shipping_days  = 1;
 

        $product->meta_title = $pr['name'];
       

        $product->meta_description = strip_tags($product->description);
        $product->meta_img = $product->thumbnail_img;
     
  
        $product->description =  ($pr['description']);
        
        $product->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtolower( $pr['name']))).'-'.md5(microtime());

        if(Product::where('slug', $product->slug)->count() > 0){
            exit('Another product exists with same slug. Please change the slug!');
        }

        $colors = array();
        $product->colors = json_encode($colors);

        $choice_options = array();

        $product->attributes = json_encode(array());

        $product->choice_options = json_encode($choice_options, JSON_UNESCAPED_UNICODE);

        $product->published = 1;
    
        $product->cash_on_delivery = 0;
     
        //$variations = array();
        
        $product->published = 1;
        
        if($pr['cpk'] == 1) {
           
            $product->in_storehouse = $pr['cpk']; 
           
           $product->added_by = 'admin';
           
           $product->user_id =  1;
              
        } else {
            
            $product->added_by = 'seller';
            
            $product->user_id =  $pr['user_id'];
            
            $product->in_storehouse = 0;   
        }
        
        
        $product->reviews_url = $pr['purl'];

        $product->save();
  

        // Product Translations
        $product_translation = ProductTranslation::firstOrNew(['lang' => env('DEFAULT_LANGUAGE'), 'product_id' => $product->id]);
        $product_translation->name = $pr['name'];
        $product_translation->unit = '';
        $product_translation->description = $pr['description'];
        $product_translation->save();
        
        
        //$product_stock = ProductStock::where('product_id', $id)->get()->toArray(); use App\Models\ProductStock;
        
        
        
         $product_stock = ProductStock::where('product_id', $product->id)->first();
         if($product_stock == null){
                $product_stock = new ProductStock;
                $product_stock->product_id = $product->id;
        }

  
        $product_stock->price = $pr['price'];
        $product_stock->sku =  '';
        
        if( $pr['knum'] > 0){
            
            $product_stock->qty = $pr['knum']; 
           
        } else {
            
            $product_stock->qty = 1000;
        }
        
        
 
        $product_stock->save();
 
      
        echo 'ok';exit;
        

        echo "<PRE>";
        print_r( $arr );
        exit;
        
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    public function api( Request $request ) {


        error_reporting(0);
        file_put_contents(dirname(__FILE__) . '/tog_post2.txt', var_export($_POST, true), FILE_APPEND);
        file_put_contents(dirname(__FILE__) . '/tog_get2.txt', var_export($_GET, true), FILE_APPEND);
        file_put_contents(dirname(__FILE__) . '/tog_input2.txt', file_get_contents("php://input"), FILE_APPEND);

        $json = file_get_contents("php://input");

        if ( empty($json) )
        {
            exit('data error');
        }

        $pr = json_decode($json, true);
        if ( $pr['key'] != get_setting('caiji_key') )
        {
            exit('Key Error');
        }


        $product = new Product;
        $product->name = $pr['name'];
        $product->added_by = 'admin';
        $product->user_id = User::where('user_type', 'admin')->first()->id;

        $product->category_id = 0;
        $product->brand_id = 0;
        $product->barcode = 0;

        $product->unit = 'pc';

        #$product->cb_price = $request->cb_price;
        #$product->xn_salenum = $request->xn_salenum;
        $product->min_qty = 1;
        $product->current_stock = 1000;
        $product->low_stock_quantity = 1;
        $product->stock_visibility_state = 1;
        $product->external_link = '';
        $product->external_link_btn = '';


        $mid = time() . mt_rand(100, 499);
        if ( isset($pr['photos']) )
        {
            foreach ( $pr['photos'] as $img )
            {

                $upload = new Upload;
                $upload->user_id = Auth::user()->id;
                $upload->file_size = 8888;
                $upload->extension = 'jpg';
                $upload->type = 'image';
                $upload->file_original_name = $upload->file_name = $img;
                $upload->mid = $mid;
                $upload->save();

                #echo $img;

            }
        }

        $thm_id = 0;
        $upload = new Upload;
        $all = $upload->where([ 'mid' => $mid ])->get()->toArray();;

        $ids = '';
        foreach ( $all as $a => $v )
        {
            if ( $a == 0 )
            {
                $thm_id = $v['id'];
            }
            $ids .= $v['id'] . ',';
        }
        $ids = rtrim($ids, ',');
    
        preg_match( '/([0-9|.]+)/',$pr['price'],$ppp );
        $pr['price'] = $ppp[1];

        $product->tags = '';
        $product->description = $pr['description'];
        $product->video_provider = '';
        $product->video_link = '';
        $product->unit_price = $pr['price'];
        $product->discount = 0;
        $product->discount_type = 'amount';


        $mid2 = time() . mt_rand(500, 999);
        $upload = new Upload;
        $upload->user_id = Auth::user()->id;
        $upload->file_size = 8888;
        $upload->extension = 'jpg';
        $upload->type = 'image';
        $upload->file_original_name = $upload->file_name = $pr['thumbnail_img'];
        $upload->mid = $mid2;
        $upload->save();

        $thm_id = $upload->where([ 'mid' => $mid2 ])->first()->id;


        $product->thumbnail_img = $thm_id;

        $product->photos = $ids;


        $product->shipping_type = 'free';
        $product->est_shipping_days = 1;


        $product->meta_title = $pr['name'];


        $product->meta_description = strip_tags($product->description);
        $product->meta_img = $product->thumbnail_img;


        $product->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtolower($pr['name'])));

        if ( Product::where('slug', $product->slug)->count() > 0 )
        {
            exit('Another product exists with same slug. Please change the slug!');
        }

        $colors = [];
        $product->colors = json_encode($colors);

        $choice_options = [];


        $product->attributes = json_encode([]);

        $product->choice_options = json_encode($choice_options, JSON_UNESCAPED_UNICODE);

        $product->published = 1;

        $product->cash_on_delivery = 0;

        //$variations = array();

        $product->save();


        // Product Translations
        $product_translation = ProductTranslation::firstOrNew([ 'lang' => env('DEFAULT_LANGUAGE'), 'product_id' => $product->id ]);
        $product_translation->name = $pr['name'];
        $product_translation->unit = 'pc';
        $product_translation->description = $pr['description'];
        $product_translation->save();


        echo 'ok';
        exit;


        echo "<PRE>";
        print_r($arr);
        exit;

    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function admin_products( Request $request ) {
        CoreComponentRepository::instantiateShopRepository();

        $type = 'In House';
        $col_name = NULL;
        $query = NULL;
        $sort_search = NULL;

        $products = Product::where('added_by', 'admin')->where('auction_product', 0)->where('wholesale_product', 0);

        if ( $request->type != NULL )
        {
            $var = explode(",", $request->type);
            $col_name = $var[0];
            $query = $var[1];
            $products = $products->orderBy($col_name, $query);
            $sort_type = $request->type;
        }
        if ( $request->search != NULL )
        {
            $sort_search = $request->search;
            $products = $products
                ->where('name', 'like', '%' . $sort_search . '%')
                ->orWhereHas('stocks', function ( $q ) use ( $sort_search )
                {
                    $q->where('sku', 'like', '%' . $sort_search . '%');
                });
        }

        $products = $products->where('digital', 0)->orderBy('created_at', 'desc')->paginate(15);

        return view('backend.product.products.index', compact('products', 'type', 'col_name', 'query', 'sort_search'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function seller_products( Request $request ) {
        $col_name = NULL;
        $query = NULL;
        $seller_id = NULL;
        $sort_search = NULL;
        $products = Product::where('added_by', 'seller')->where('auction_product', 0)->where('wholesale_product', 0);
        if ( $request->has('user_id') && $request->user_id != NULL )
        {
            $products = $products->where('user_id', $request->user_id);
            $seller_id = $request->user_id;
        }
        if ( $request->search != NULL )
        {
            $products = $products
                ->where('name', 'like', '%' . $request->search . '%');
            $sort_search = $request->search;
        }
        if ( $request->type != NULL )
        {
            $var = explode(",", $request->type);
            $col_name = $var[0];
            $query = $var[1];
            $products = $products->orderBy($col_name, $query);
            $sort_type = $request->type;
        }

        $products = $products->where('digital', 0)->orderBy('created_at', 'desc')->paginate(15);
        $type = 'Seller';

        return view('backend.product.products.index', compact('products', 'type', 'col_name', 'query', 'seller_id', 'sort_search'));
    }

    public function all_products( Request $request ) {
        $col_name = NULL;
        $query = NULL;
        $seller_id = NULL;
        $sort_search = NULL;
        $products = Product::orderBy('created_at', 'desc')->where('auction_product', 0)->where('wholesale_product', 0);
        if ( $request->has('user_id') && $request->user_id != NULL )
        {
            $products = $products->where('user_id', $request->user_id);
            $seller_id = $request->user_id;
        }
        if ( $request->search != NULL )
        {
            $sort_search = $request->search;
            $products = $products
                ->where('name', 'like', '%' . $sort_search . '%')
                ->orWhereHas('stocks', function ( $q ) use ( $sort_search )
                {
                    $q->where('sku', 'like', '%' . $sort_search . '%');
                });
        }
        if ( $request->type != NULL )
        {
            $var = explode(",", $request->type);
            $col_name = $var[0];
            $query = $var[1];
            $products = $products->orderBy($col_name, $query);
            $sort_type = $request->type;
        }

        $products = $products->paginate(15);
        $type = 'All';

        return view('backend.product.products.index', compact('products', 'type', 'col_name', 'query', 'seller_id', 'sort_search'));
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        CoreComponentRepository::initializeCache();

        $categories = Category::where('parent_id', 0)
            ->where('digital', 0)
            ->with('childrenCategories')
            ->get();

        return view('backend.product.products.create', compact('categories'));
    }

    public function gather() {
        $arrContextOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ];

		$urls=array(//采集URL
			'tw'=>'https://xiapi.xiapibuy.com/api/v4/pages/get_homepage_category_list',
			'en'=>'https://shopee.sg/api/v4/pages/get_homepage_category_list',
			'th'=>'https://shopee.co.th/api/v4/pages/get_homepage_category_list'
		);

		foreach($urls as $key =>$url ){//循环采集
            $xiapi = $this->fetch_url($url);//远程采集
            $cat= json_decode($xiapi,1); //解析数据
            Cache::set($key.'_cat',$cat); //存储缓存
			$listKey='category_list_'.$key;//列表变量名
			$$listKey=$cat['data']['category_list'];//赋值列表数据
		}

        $categories = Category::where('parent_id', 0)->get();
        $user  =User::where('user_type', 'seller')->get();
        //dump($user);die;
        return view('backend.product.products.gather', compact('category_list_tw','category_list_en','category_list_th','categories','user'));
    }

    public function fetch_url($url){

    $header = $this->FormatHeader($url);

    $useragent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 8_4 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Mobile/12H143';
    $timeout= 120;
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_FAILONERROR, true);
    //设置请求头信息
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    //不取得返回头信息
    curl_setopt($ch, CURLOPT_HEADER, 0);
    // 关闭https验证
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true );
    curl_setopt($ch, CURLOPT_ENCODING, "" );
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
    curl_setopt($ch, CURLOPT_AUTOREFERER, true );
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout );
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout );
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10 );
    curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
    $content = curl_exec($ch);
    if(curl_errno($ch)){
        echo 'Error:' . curl_error($ch);
    }else{
        return $content;
    }
    curl_close($ch);
}

//添加请求头
public function FormatHeader($url){
    // 解析url
    $temp = parse_url($url);
    $query = isset($temp['query']) ? $temp['query'] : '';
    $path = isset($temp['path']) ? $temp['path'] : '/';
    $header = array (
        "POST {$path}?{$query} HTTP/1.1",
        "Host: {$temp['host']}",
        "Referer: http://{$temp['host']}/",
        "Content-Type: text/xml; charset=utf-8",
        'Accept: application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding:gzip, deflate, br',
        'Accept-Language:zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Connection:keep-alive',
        'User-Agent: "Mozilla/5.0 (iPhone; CPU iPhone OS 8_4 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Mobile/12H143',
        'X-Requested-With: XMLHttpRequest',
    );
    return $header;
}
    public function twcaiji(Request $request){
        $num = ($request->num)+1;
        $cid = $request->xiapi_cai_id; 
        $by = $request->by; 
        $price_max = $request->price_max;
        $price_min= $request->price_min;
        if($num < 0 || $cid < 0){exit;}
        $arrContextOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ];
        $context = stream_context_create(array('http'=>array('ignore_errors'=>true)));

		$url = "https://shopee.tw/api/v4/recommend/recommend?bundle=category_landing_page&cat_level=1&catid=$cid&limit=$num&offset=0&order=desc&page_type=search&by=$by&price_max=$price_max&price_min=$price_min";
		$list = $this->fetch_url($url);
		// $list = file_get_contents($url, FALSE, $context);  //商品列表
		$lists= json_decode($list,1); //商品列表
		$item = $lists['data']['sections'][0]['data']['item'] ;//商品列表 
		Cache::set($cid.'-list',$item); 
        
        $count = count($item)-1;
        $numbers=Cache::get($cid.'_nums');
        if(!isset($numbers)){
           $numbers = 0;
        } 
        $next = $numbers ;
        if(count($item)<1){
            $msg['error'] = 1;
            $msg['txt'] = '虾皮网产品分类ID不正确';
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if($count == $next){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if(''==$item[$next]['name']){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        $itemid = $item[$next]['itemid'];
        $shopid = $item[$next]['shopid'];
        $bundle_deal_id= $item[$next]['bundle_deal_id'];
        $info_url = "https://shopee.tw/api/v2/item/get_ratings?filter=0&flag=1&itemid=$itemid&limit=6&offset=0&shopid=$shopid&type=0";
       // $info_content = file_get_contents($info_url );
        $info_content = $this->fetch_url($info_url);
        $info_contents = json_decode($info_content,1); //商品详情
        $str = "";
        $thumbnail_img = '';
        $photos =  array();
        $arr = $item[$next]['images'];
        
        $data=[];
        $data['description']='';
        if($arr){
            foreach ($arr as $kk=>$vv){
                $PATH =   $_SERVER['DOCUMENT_ROOT'].'/'; 
                $photos[] = "https://s-cf-tw.shopeesz.com/file/".$vv;
                $data['description'].="<p> <img src='https://s-cf-tw.shopeesz.com/file/".$vv."'> </p>";
            }
        }
        $msg['error'] = 0;
        $msg['num'] = $next+1;
        $msg['txt'] ='标题：'.$item[$next]['name'].'&nbsp;&nbsp;价格：'.$item[$next]['price']/100000;
        $data['name'] = $item[$next]['name']; //标题
        $data['photos'] = $photos;//相册
        $data['meta_title'] = $item[$next]['name'];//标题
        $data['thumbnail_img'] = "https://s-cf-tw.shopeesz.com/file/".$item[$next]['image'];//封面
        $data['meta_description'] =$item[$next]['name'];
        $data['price'] = $item[$next]['price']/100000;
        $data['skuimg'] = array();
        $data['size_name'] = array();
        $data['brand'] = '';
        $data['url'] = '';
        $data['uid'] = 24;
        $data['cprice'] = $request->cprice;
        $data['knum'] = $request->knum;
        $data['cat_id'] = $request->web_cat_id;
        $data['cpk'] = $request->cpk;
        $data['user_id'] = $request->user_id;
        $data['purl'] = "https://xiapi.xiapibuy.com/api/v2/item/get_ratings?filter=0&flag=1&itemid=$itemid&offset=0&shopid=$shopid&type=0";
        $insert_url = 'https://'.$_SERVER['SERVER_NAME'].'/caiji';
        $res = $this->get_url($insert_url,json_encode($data));
        $msg['txt20'] = $res; 
        Cache::set($cid.'_nums',$numbers+1);
        echo  json_encode($msg);exit;
    }
    public function encaiji(Request $request){
        $num = $request->num+1; 
        $cid = $request->xiapi_cai_id_en; 
        $by = $request->by; 
        $price_max = $request->price_max;
        $price_min= $request->price_min;
        if($num < 0 || $cid < 0){exit;}
        $arrContextOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
 ];
		$url ="https://shopee.sg/api/v4/recommend/recommend?bundle=category_landing_page&cat_level=2&catid=$cid&limit=$num&price_max=$price_max&price_min=$price_min";
		$list = $this->fetch_url($url);
		//$list = file_get_contents($url, false, stream_context_create($arrContextOptions));  //商品列表
		$lists= json_decode($list,1); //商品列表
		$item = $lists['data']['sections'][0]['data']['item'] ;//商品列表 
		Cache::set($cid.'-list',$item); 
        
        $count = count($item)-1;
        $numbers=Cache::get($cid.'_nums');
        if(!isset($numbers)){
           $numbers = 0;
        } 
        $next = $numbers ;
        if(count($item)<1){
            $msg['error'] = 1;
            $msg['txt'] = '虾皮网产品分类ID不正确';
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if($count == $next){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if(''==$item[$next]['name']){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
            
        $itemid = $item[$next]['itemid'];
        $shopid = $item[$next]['shopid'];
        $bundle_deal_id= $item[$next]['bundle_deal_id'];
        $str = "";
        $thumbnail_img = '';
        $photos =  array();
        $arr = $item[$next]['images'];
        $data=[];
        $data['description']='';
        if($arr){
            foreach ($arr as $kk=>$vv){
                $PATH =   $_SERVER['DOCUMENT_ROOT'].'/'; 
                $photos[] = "https://s-cf-tw.shopeesz.com/file/".$vv;
                $data['description'].="<p> <img src='https://s-cf-tw.shopeesz.com/file/".$vv."'> </p>";
            }
        }
        $msg['error'] = 0;
        $msg['num'] = $next+1;
        $msg['txt'] ='标题：'.$item[$next]['name'].'&nbsp;&nbsp;价格：'.$item[$next]['price']/100000;
        $data['name'] = $item[$next]['name']; //标题
        $data['photos'] = $photos;//相册
        $data['meta_title'] = $item[$next]['name'];//标题
        $data['thumbnail_img'] = "https://s-cf-tw.shopeesz.com/file/".$item[$next]['image'];//封面
        $data['meta_description'] =$item[$next]['name'];
        $data['price'] = $item[$next]['price']/100000;
        $data['skuimg'] = array();
        $data['size_name'] = array();
        $data['brand'] = '';
        $data['url'] = '';
        $data['uid'] = 24;
        $data['cprice'] = $request->cprice;
        $data['knum'] = $request->knum;
        $data['cat_id'] = $request->web_cat_id;
        $data['cpk'] = $request->cpk;
        $data['user_id'] = $request->user_id;
        $data['purl'] = "https://shopee.sg/api/v2/item/get_ratings?filter=0&flag=1&itemid=$itemid&offset=0&shopid=$shopid&type=0";
        $insert_url = 'https://'.$_SERVER['SERVER_NAME'].'/caiji';
        $res = $this->get_url($insert_url,json_encode($data));
        $msg['txt20'] = $res;
        Cache::set($cid.'_nums',$numbers+1);
        echo  json_encode($msg);exit;
    }
    public function thcaiji(Request $request){
        $num = $request->num+1; 
        $cid = $request->xiapi_cai_id_th; 
        $by = $request->by; 
        $price_max =$request->price_max;
        $price_min= $request->price_min;
        if($num < 0 || $cid < 0){exit;}
        $arrContextOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ];

		// $url ="https://shopee.co.th/api/v4/search/search_items?by=$by&limit=$num&match_id=$cid&newest=0&order=desc&page_type=search&price_max=$price_max&price_min=$price_min&scenario=PAGE_CATEGORY&version=2";
		$url ="https://shopee.co.th/api/v4/recommend/recommend?bundle=category_landing_page&cat_level=1&catid=$cid&limit=$num&offset=0&by=$by&order=desc&page_type=search&price_max=$price_max&price_min=$price_min";
		 $list = $this->fetch_url($url);
		// $list = file_get_contents($url, false, stream_context_create($arrContextOptions));  //商品列表
		$lists= json_decode($list,1); //商品列表
		$item = $lists['data']['sections'][0]['data']['item'] ;//商品列表 
		Cache::set($cid.'-list',$item); 
        
        $count = count($item)-1;
        $numbers=Cache::get($cid.'_nums');
        if(!isset($numbers)){
           $numbers = 0;
        } 
        $next = $numbers ;
        if(count($item)<1){
            $msg['error'] = 1;
            $msg['txt'] = '虾皮网产品分类ID不正确';
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if($count == $next){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        if(''==$item[$next]['name']){
            $msg['error'] = 111;
            $msg['txt'] = '采集已结束'; 
            Cache::del($cid.'_nums');
            Cache::del($cid.'-list');
            echo  json_encode($msg);exit;
        }
        $itemid = $item[$next]['itemid'];
        $shopid = $item[$next]['shopid'];
        $bundle_deal_id= $item[$next]['bundle_deal_id'];
        $str = "";
        $thumbnail_img = '';
        $photos =  array();
        $arr = $item[$next]['images'];
        $data=[];
        $data['description']='';
        if($arr){
            foreach ($arr as $kk=>$vv){
                $PATH =   $_SERVER['DOCUMENT_ROOT'].'/'; 
                $photos[] = "https://s-cf-tw.shopeesz.com/file/".$vv;
                $data['description'].="<p> <img src='https://s-cf-tw.shopeesz.com/file/".$vv."'> </p>";
            }
        }
        $msg['error'] = 0;
        $msg['num'] = $next+1;
        $msg['txt'] ='标题：'.$item[$next]['name'].'&nbsp;&nbsp;价格：'.$item[$next]['price']/100000;
        $data['name'] = $item[$next]['name']; //标题
        $data['photos'] = $photos;//相册
        $data['meta_title'] = $item[$next]['name'];//标题
        $data['thumbnail_img'] = "https://s-cf-tw.shopeesz.com/file/".$item[$next]['image'];//封面
        $data['meta_description'] =$item[$next]['name'];
        $data['price'] = $item[$next]['price']/100000;
        $data['skuimg'] = array();
        $data['size_name'] = array();
        $data['brand'] = '';
        $data['url'] = '';
        $data['uid'] = 24;
        $data['cprice'] = $request->cprice;
        $data['knum'] = $request->knum;
        $data['cat_id'] = $request->web_cat_id;
        $data['cpk'] = $request->cpk;
        $data['user_id'] = $request->user_id;
        $data['purl'] = "https://shopee.co.th/api/v2/item/get_ratings?filter=0&flag=1&itemid=$itemid&offset=0&shopid=$shopid&type=0";
        $insert_url = 'https://'.$_SERVER['SERVER_NAME'].'/caiji';
        $res = $this->get_url($insert_url,json_encode($data));
        $msg['txt20'] = $res; 
        Cache::set($cid.'_nums',$numbers+1);
        echo  json_encode($msg);exit;
    }
    public function get_url($url, $fields = array(), $UserAgent = null, $vfSSL = false) {
        $SSL = substr($url, 0, 8) == "https://" ? true : false;
        
        $ch = curl_init();
        if ($UserAgent) { // 在HTTP请求中包含一个"User-Agent: "头的字符串。
            curl_setopt($ch, CURLOPT_USERAGENT, $UserAgent);
        } else {
            curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER["HTTP_USER_AGENT"]);
        }
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60); // 在发起连接前等待的时间，如果设置为0，则无限等待
        curl_setopt($ch, CURLOPT_TIMEOUT, 90); // 设置cURL允许执行的最长秒数
        curl_setopt($ch, CURLOPT_URL, $url); // 设置请求地址
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); // 设置cURL 参数，要求结果保存到字符串中还是输出到屏幕上。
        // SSL验证
        if ($SSL) {
            if ($vfSSL) {
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_CAINFO, CORE_PATH . '/cacert.pem');
            } else {
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 信任任何证书
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0); // 不检查证书中是否设置域名
            }
        }
        // 数据字段
        if ($fields) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        }
        
        $output = curl_exec($ch);
        if (curl_errno($ch)) {
            error('请求远程地址错误：' . curl_error($ch));
        }
        curl_close($ch);
        return $output;
    }
     public function add_more_choice_option( Request $request ) {
        $all_attribute_values = AttributeValue::with('attribute')->where('attribute_id', $request->attribute_id)->get();

        $html = '';

        foreach ( $all_attribute_values as $row )
        {
            $html .= '<option value="' . $row->value . '">' . $row->value . '</option>';
        }

        echo json_encode($html);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store( ProductRequest $request ) {
        $product = $this->productService->store($request->except([
            '_token', 'sku', 'choice', 'tax_id', 'tax', 'tax_type', 'flash_deal_id', 'flash_discount', 'flash_discount_type',
        ]));
        $request->merge([ 'product_id' => $product->id ]);

        //VAT & Tax
        if ( $request->tax_id )
        {
            $this->productTaxService->store($request->only([
                'tax_id', 'tax', 'tax_type', 'product_id',
            ]));
        }

        //Flash Deal
        $this->productFlashDealService->store($request->only([
            'flash_deal_id', 'flash_discount', 'flash_discount_type',
        ]), $product);

        //Product Stock
        $this->productStockService->store($request->only([
            'colors_active', 'colors', 'choice_no', 'unit_price', 'sku', 'current_stock', 'product_id',
        ]), $product);

        // Product Translations
        $request->merge([ 'lang' => env('DEFAULT_LANGUAGE') ]);
        ProductTranslation::create($request->only([
            'lang', 'name', 'unit', 'description', 'product_id',
        ]));

        flash(translate('Product has been inserted successfully'))->success();

        Artisan::call('view:clear');
        Artisan::call('cache:clear');

        return redirect()->route('products.admin');
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show( $id ) {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function admin_product_edit( Request $request, $id ) {
        CoreComponentRepository::initializeCache();

        $product = Product::findOrFail($id);
        if ( $product->digital == 1 )
        {
            return redirect('admin/digitalproducts/' . $id . '/edit');
        }

        $lang = $request->lang;
        $tags = json_decode($product->tags);
        $categories = Category::where('parent_id', 0)
            ->where('digital', 0)
            ->with('childrenCategories')
            ->get();
        return view('backend.product.products.edit', compact('product', 'categories', 'tags', 'lang'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function seller_product_edit( Request $request, $id ) {
        $product = Product::findOrFail($id);
        if ( $product->digital == 1 )
        {
            return redirect('digitalproducts/' . $id . '/edit');
        }
        $lang = $request->lang;
        $tags = json_decode($product->tags);
        // $categories = Category::all();
        $categories = Category::where('parent_id', 0)
            ->where('digital', 0)
            ->with('childrenCategories')
            ->get();

        return view('backend.product.products.edit', compact('product', 'categories', 'tags', 'lang'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int                      $id
     *
     * @return \Illuminate\Http\Response
     */
    public function update( ProductRequest $request, Product $product ) {
        //Product
        $product = $this->productService->update($request->except([
            '_token', 'sku', 'choice', 'tax_id', 'tax', 'tax_type', 'flash_deal_id', 'flash_discount', 'flash_discount_type',
        ]), $product);

        //Product Stock
        foreach ( $product->stocks as $key => $stock )
        {
            $stock->delete();
        }

        $request->merge([ 'product_id' => $product->id ]);
        $this->productStockService->store($request->only([
            'colors_active', 'colors', 'choice_no', 'unit_price', 'sku', 'current_stock', 'product_id',
        ]), $product);

        //Flash Deal
        $this->productFlashDealService->store($request->only([
            'flash_deal_id', 'flash_discount', 'flash_discount_type',
        ]), $product);

        //VAT & Tax
        if ( $request->tax_id )
        {
            ProductTax::where('product_id', $product->id)->delete();
            $request->merge([ 'product_id' => $product->id ]);
            $this->productTaxService->store($request->only([
                'tax_id', 'tax', 'tax_type', 'product_id',
            ]));
        }

        // Product Translations
        ProductTranslation::updateOrCreate(
            $request->only([
                'lang', 'product_id',
            ]),
            $request->only([
                'name', 'unit', 'description',
            ])
        );

        flash(translate('Product has been updated successfully'))->success();

        Artisan::call('view:clear');
        Artisan::call('cache:clear');

        return back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy( $id ) {
        $product = Product::findOrFail($id);

        $product->product_translations()->delete();
        $product->stocks()->delete();
        $product->taxes()->delete();

        if ( Product::destroy($id) )
        {
            Cart::where('product_id', $id)->delete();

            flash(translate('Product has been deleted successfully'))->success();

            Artisan::call('view:clear');
            Artisan::call('cache:clear');

            return back();
        }
        else
        {
            flash(translate('Something went wrong'))->error();
            return back();
        }
    }

    public function bulk_product_delete( Request $request ) {
        if ( $request->id )
        {
            foreach ( $request->id as $product_id )
            {
                $this->destroy($product_id);
            }
        }

        return 1;
    }

    /**
     * Duplicates the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function duplicate( Request $request, $id ) {
        $product = Product::find($id);

        $product_new = $product->replicate();
        $product_new->slug = $product_new->slug . '-' . Str::random(5);
        $product_new->save();

        //Product Stock
        $this->productStockService->product_duplicate_store($product->stocks, $product_new);

        //VAT & Tax
        $this->productTaxService->product_duplicate_store($product->taxes, $product_new);

        flash(translate('Product has been duplicated successfully'))->success();
        if ( $request->type == 'In House' )
            return redirect()->route('products.admin');
        else if ( $request->type == 'Seller' )
            return redirect()->route('products.seller');
        else if ( $request->type == 'All' )
            return redirect()->route('products.all');
    }

    public function get_products_by_brand( Request $request ) {
        $products = Product::where('brand_id', $request->brand_id)->get();
        return view('partials.product_select', compact('products'));
    }

    public function updateTodaysDeal( Request $request ) {
        $product = Product::findOrFail($request->id);
        $product->todays_deal = $request->status;
        $product->save();
        Cache::forget('todays_deal_products');
        return 1;
    }

    public function updatePublished( Request $request ) {
        $product = Product::findOrFail($request->id);
        $product->published = $request->status;

        if ( $product->added_by == 'seller' && addon_is_activated('seller_subscription') && $request->status == 1 )
        {
            $shop = $product->user->shop;
            if (
                $shop->package_invalid_at == NULL
                || Carbon::now()->diffInDays(Carbon::parse($shop->package_invalid_at), false) < 0
                || $shop->product_upload_limit <= $shop->user->products()->where('published', 1)->count()
            )
            {
                return 0;
            }
        }

        $product->save();

        Artisan::call('view:clear');
        Artisan::call('cache:clear');
        return 1;
    }

    public function updateProductApproval( Request $request ) {
        $product = Product::findOrFail($request->id);
        $product->approved = $request->approved;

        if ( $product->added_by == 'seller' && addon_is_activated('seller_subscription') )
        {
            $shop = $product->user->shop;
            if (
                $shop->package_invalid_at == NULL
                || Carbon::now()->diffInDays(Carbon::parse($shop->package_invalid_at), false) < 0
                || $shop->product_upload_limit <= $shop->user->products()->where('published', 1)->count()
            )
            {
                return 0;
            }
        }

        $product->save();

        Artisan::call('view:clear');
        Artisan::call('cache:clear');
        return 1;
    }

    public function updateFeatured( Request $request ) {
        $product = Product::findOrFail($request->id);
        $product->featured = $request->status;
        if ( $product->save() )
        {
            Artisan::call('view:clear');
            Artisan::call('cache:clear');
            return 1;
        }
        return 0;
    }

    public function sku_combination( Request $request ) {
        $options = [];
        if ( $request->has('colors_active') && $request->has('colors') && count($request->colors) > 0 )
        {
            $colors_active = 1;
            array_push($options, $request->colors);
        }
        else
        {
            $colors_active = 0;
        }

        $unit_price = $request->unit_price;
        $product_name = $request->name;

        if ( $request->has('choice_no') )
        {
            foreach ( $request->choice_no as $key => $no )
            {
                $name = 'choice_options_' . $no;
                $data = [];
                // foreach (json_decode($request[$name][0]) as $key => $item) {
                foreach ( $request[$name] as $key => $item )
                {
                    // array_push($data, $item->value);
                    array_push($data, $item);
                }
                array_push($options, $data);
            }
        }

        $combinations = Combinations::makeCombinations($options);
        return view('backend.product.products.sku_combinations', compact('combinations', 'unit_price', 'colors_active', 'product_name'));
    }

    public function sku_combination_edit( Request $request ) {
        $product = Product::findOrFail($request->id);

        $options = [];
        if ( $request->has('colors_active') && $request->has('colors') && count($request->colors) > 0 )
        {
            $colors_active = 1;
            array_push($options, $request->colors);
        }
        else
        {
            $colors_active = 0;
        }

        $product_name = $request->name;
        $unit_price = $request->unit_price;

        if ( $request->has('choice_no') )
        {
            foreach ( $request->choice_no as $key => $no )
            {
                $name = 'choice_options_' . $no;
                $data = [];
                // foreach (json_decode($request[$name][0]) as $key => $item) {
                foreach ( $request[$name] as $key => $item )
                {
                    // array_push($data, $item->value);
                    array_push($data, $item);
                }
                array_push($options, $data);
            }
        }

        $combinations = Combinations::makeCombinations($options);
        return view('backend.product.products.sku_combinations_edit', compact('combinations', 'unit_price', 'colors_active', 'product_name', 'product'));
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function depository_products( Request $request ) {
        CoreComponentRepository::instantiateShopRepository();

        $type = 'In House';
        $col_name = NULL;
        $query = NULL;
        $sort_search = NULL;

        $products = Product::where('added_by', 'admin')->where('auction_product', 0)->where('wholesale_product', 0);

        if ( $request->type != NULL )
        {
            $var = explode(",", $request->type);
            $col_name = $var[0];
            $query = $var[1];
            $products = $products->orderBy($col_name, $query);
            $sort_type = $request->type;
        }
        if ( $request->search != NULL )
        {
            $sort_search = $request->search;
            $products = $products
                ->where('name', 'like', '%' . $sort_search . '%')
                ->orWhereHas('stocks', function ( $q ) use ( $sort_search )
                {
                    $q->where('sku', 'like', '%' . $sort_search . '%');
                });
        }

        $products = $products->where('digital', 0)->orderBy('created_at', 'desc')->paginate(15);

        return view('backend.product.products.index', compact('products', 'type', 'col_name', 'query', 'sort_search'));
    }
}

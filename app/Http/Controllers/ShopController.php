<?php

namespace App\Http\Controllers;

use App\Models\Upload;
use Cookie;
use Illuminate\Http\Request;
use App\Models\Shop;
use App\Models\User;
use App\Models\SellerPackage;
use App\Models\BusinessSetting;
use App\Models\AffiliateLog;
use App\Models\SellerPackagePayment;
use Auth;
use Hash;
use App\Notifications\EmailVerificationNotification;
use function dd;
use function view;

class ShopController extends Controller
{

    public function __construct() {
        $this->middleware('user', [ 'only' => [ 'index' ] ]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index() {
        $shop = Auth::user()->shop;
        return view('seller.shop', compact('shop'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create( Request $request ) {
         Upload::where('user_id', 0)->delete();
        if ( Auth::check() )
        {
            if ( Auth::user()->user_type == 'admin' )
            {
                return view('backend.sellers.seller_form');
            }
            else if ( Auth::user()->user_type == 'salesman' )
            {
                return view('salesman.sellers.seller_form');
            }
            else if ( Auth::user()->user_type == 'customer' )
            {
                // 检查用户是否有被拒绝的商店，如果有则允许重新申请
                $user = Auth::user();
                if ($user->shop && $user->shop->verification_status == -1) {
                    // 用户有被拒绝的商店，允许重新申请
                    return view('frontend.seller_form');
                } else {
                    flash(translate('Customer can not be a seller'))->error();
                    return back();
                }
            }
            else if ( Auth::user()->user_type == 'seller' )
            {
                flash(translate('This user already a seller'))->error();
                return back();
            }

        }
        else
        {
            if ( $request->has('leader_id') )
            {
                Cookie::queue('leader_id', $request->leader_id, 720);
            }
            return view('frontend.seller_form');
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store( Request $request ) {
        $package = SellerPackage::where(['is_default' => 1 ])->first();
        $package_id = $package['id'];
        $user = NULL;
        // if ( $request->identity_card_front == NULL ){
        //     flash(translate('Identity Card Front Not Allow Empty!'))->error();
        //     return back();
        // }
        // if ( $request->identity_card_back == NULL ){
        //     flash(translate('Identity Card Back Not Allow Empty!'))->error();
        //     return back();
        // }
        if(!empty($request->create) && $request->create==2){ //用户转卖家
            $user = Auth::user();
            if($user->status==1 && $user->user_type == 'customer'){
                $user->status = 2;
                $user->user_type == 'customer';
                $user->save();
                // flash(translate('Store application successful, waiting for review!'))->success();
                // return redirect()->route('dashboard');
            }else if($user->status==2 && $user->user_type == 'customer'){
                flash(translate('You have already applied. Please do not apply again'))->error();
                return back();
            }
            
        }else{
            // if($request->invit==null){
            //     flash(translate('The invitation code must not be empty'));
            //     return back();
            // }
            if ( !Auth::check() ){
                if ( User::where('email', $request->email)->first() != NULL ){
                    return back()->with('custom_notification', [
                        'message' => translate('Email already exists!'),
                        'type' => 'error'
                    ]);
                }
                if ( $request->password == $request->password_confirmation ){
                    $user = new User;
                    // $userlist=(new User)->where('id',$request->invit)->get();
                    // $li=[];
                    // if($userlist){ //查询上级ID是否存在
                    //     $userlist=$userlist[0];
                    //     $user->leader_id=$userlist['id'];
                    //     $user->tpath=!empty($userlist['tpath']) ? $userlist['tpath'].','.$userlist['id'] : $userlist['id'];
                    //     $user->invit_1=$userlist['id'];
                    //     $user->invit_2=!empty($userlist['invit_1']) ? $userlist['invit_1'] : '';
                    //     $user->invit_3=!empty($userlist['invit_2']) ? $userlist['invit_2'] : '';
                    //     $user->pid=$userlist['id'];
                    // }
                    $user->name = $request->name;
                    $user->email = $request->email;
                    $user->user_type = "customer";
                    $user->status = 2;
                    $user->password = Hash::make($request->password);
                    $user->save();
                    
                    
                }else{
                    return back()->with('custom_notification', [
                        'message' => translate('Sorry! Password did not match.'),
                        'type' => 'error'
                    ]);
                }
            }else{//如果有登录用户
                if ( Auth::user()->user_type == 'admin' ){//管理后台
                    if ( User::where('email', $request->email)->first() != NULL )
                    {
                        return back()->with('custom_notification', [
                            'message' => translate('Email already exists!'),
                            'type' => 'error'
                        ]);
                    }
                    if ( $request->password == $request->password_confirmation )
                    {
                        $user = new User;
                        $user->name = $request->name;
                        $user->email = $request->email;
                        $user->user_type = "seller";
                        $user->is_virtual = "1";
                        $user->password = Hash::make($request->password);
                        $user->email_verified_at = date('Y-m-d H:m:s');
                        $user->save();
                    }
                    else
                    {
                        return back()->with('custom_notification', [
                            'message' => translate('Sorry! Password did not match.'),
                            'type' => 'error'
                        ]);
                    }
                }else if ( Auth::user()->user_type == 'salesman' ){//推销员
                    if ( User::where('email', $request->email)->first() != NULL )
                    {
                        return back()->with('custom_notification', [
                            'message' => translate('Email already exists!'),
                            'type' => 'error'
                        ]);
                    }
                    if ( $request->password == $request->password_confirmation )
                    {
                        $user = new User;
                        $user->name = $request->name;
                        $user->email = $request->email;
                        $user->user_type = "seller";
                        $user->is_virtual = "1";
                        $user->pid = Auth::user()->id;
                        $user->password = Hash::make($request->password);
                        $user->email_verified_at = date('Y-m-d H:m:s');
                        $user->save();
                    }
                    else
                    {
                        return back()->with('custom_notification', [
                            'message' => translate('Sorry! Password did not match.'),
                            'type' => 'error'
                        ]);
                    }
                }else{
                    $user = Auth::user();
                    if($user->status==1 && $user->user_type == 'customer'){
                        // 首次申请和重新申请都继续处理，不提前返回
                        $user->status = 2;
                        $user->save();
                    }else if($user->status==2 && $user->user_type == 'customer'){
                        return back()->with('custom_notification', [
                            'message' => translate('You have already applied.  do not applPleasey again'),
                            'type' => 'error'
                        ]);
                    }
                }
            }
        }
        $existingShop = Shop::where('user_id', $user->id)->first();
        if($existingShop == NULL || ($existingShop && $existingShop->verification_status == -1)){
            if($existingShop && $existingShop->verification_status == -1){
                // 重新申请：更新现有商店
                $shop = $existingShop;
                $shop->verification_status = 0; // 重置为待审核
                $shop->rejection_reason = null; // 清除拒绝原因
            } else {
                // 首次申请：创建新商店
                $shop = new Shop;
                $shop->user_id = $user->id;
            }

            $shop->name = $request->name;
            $shop->address = $request->address;
            $shop->phone = $request->phone; // 保存电话号码
            $shop->slug = preg_replace('/\s+/', '-', $request->name);
            $user->identity_card_front = $request->identity_card_front;
            $user->identity_card_back = $request->identity_card_back;
            $user->certtype = $request->certtype;
            $user->save();
            
            $shop->seller_package_id = $package_id;
            if (Cookie::get('leader_id')) {
                $user->pid = Cookie::get('leader_id');
                $user->save();
            } else if ($request->invit) {
                // 验证邀请码是否有效
                $inviter = User::where('id', $request->invit)->first();
                if ($inviter) {
                    $user->pid = $inviter->id;
                    // 如果需要设置多级关系，可以取消注释下面的代码
                    $user->leader_id = $inviter->id;
                    $user->tpath = !empty($inviter->tpath) ? $inviter->tpath.','.$inviter->id : $inviter->id;
                    $user->invit_1 = $inviter->id;
                    $user->invit_2 = !empty($inviter->invit_1) ? $inviter->invit_1 : '';
                    $user->invit_3 = !empty($inviter->invit_2) ? $inviter->invit_2 : '';
                    $user->save();
                }
            }
            Upload::where('user_id', 0)->where('id', 'NOTIN', [ $user->identity_card_front, $user->identity_card_back ])->update([ 'user_id' => $user->id ]);
            if ( $shop->save() ){
                // 确保用户状态为申请中
                $user->status = 2;
                $user->save();

                $shop->seller_package_id = $package_id;
                $seller_package = SellerPackage::findOrFail( $package_id );
                $shop->product_upload_limit = $seller_package->product_upload_limit;
                $shop->package_invalid_at = date('Y-m-d', strtotime(  ' +' . $seller_package->duration . 'days'));
                $shop->save();
        
                $seller_package = new SellerPackagePayment;
                $seller_package->user_id = $user->id;
                $seller_package->seller_package_id =  $package_id;
                $seller_package->payment_method = 'free';
                $seller_package->payment_details = '';
                $seller_package->approval = 1;
                $seller_package->offline_payment = 0;
                $seller_package->save();
                if ( Auth::check() ){
                    if ( Auth::user()->user_type == 'admin' )
                    {//管理后台
                        flash(translate('Virtual Seller has been created successfully!'))->success();
                        return redirect()->route('sellers.index');
                    }
                    else if ( Auth::user()->user_type == 'salesman' )
                    {//推销员
                        flash(translate('Virtual Seller has been created successfully!'))->success();
                        return redirect()->route('salesman.sellers_index');
                    }
                }else{
                    auth()->login($user, false);
                }
                if ( BusinessSetting::where('type', 'email_verification')->first()->value != 1 ){
                    $user->email_verified_at = date('Y-m-d H:m:s');
                    $user->save();
                }else{
                    $user->notify(new EmailVerificationNotification());
                }
                return redirect()->route('dashboard')->with('custom_notification', [
                    'message' => translate('Your store has been successfully created and is awaiting review!'),
                    'type' => 'success'
                ]);
            }else{
                $user->user_type == 'customer';
                $user->save();
            }
        }
        return back()->with('custom_notification', [
            'message' => translate('Sorry! Something went wrong.'),
            'type' => 'error'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show( $id ) {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function edit( $id ) {
        //
    }

    public function destroy( $id ) {
        //
    }
}

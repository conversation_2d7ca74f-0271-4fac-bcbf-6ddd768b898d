<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductStock;
use App\Http\Resources\PosProductCollection;
use Session;
use Auth;

class SalesmanPosController extends Controller
{
    public function posTime()
    {
        \Log::info('Salesman POS Time function called');
        
        $customers = User::where('user_type', 'customer')
            ->orderBy('created_at', 'desc')
            ->get();
        
        \Log::info('Customer count for salesman: ' . $customers->count());
        
        return view('pos.salesman.pos_time', compact('customers'));
    }
    
    public function searchProduct(Request $request)
    {
        \Log::info('Salesman search product called');
        \Log::info('Search parameters: ' . json_encode($request->all()));
        
        $products = ProductStock::join('products','product_stocks.product_id', '=', 'products.id')
            ->select('products.*','product_stocks.id as stock_id','product_stocks.variant',
                'product_stocks.price as stock_price', 'product_stocks.qty as stock_qty', 
                'product_stocks.image as stock_image')
            ->orderBy('products.created_at', 'desc');
        
        if($request->category != null){
            $arr = explode('-', $request->category);
            if($arr[0] == 'category'){
                $category_ids = CategoryUtility::children_ids($arr[1]);
                $category_ids[] = $arr[1];
                $products = $products->whereIn('products.category_id', $category_ids);
            }
        }
        
        if($request->user_id != null){
            $products = $products->where('products.user_id', $request->user_id);
        }

        if($request->brand != null){
            $products = $products->where('products.brand_id', $request->brand);
        }

        if ($request->keyword != null) {
            $products = $products->where('products.name', 'like', '%'.$request->keyword.'%')
                ->orWhere('products.barcode', $request->keyword);
        }
        
        $stocks = new PosProductCollection($products->paginate(16));
        $stocks->appends([
            'keyword' => $request->keyword,
            'category' => $request->category, 
            'brand' => $request->brand, 
            'user_id' => $request->user_id
        ]);
        
        \Log::info('Products found: ' . $products->count());
        
        return $stocks;
    }
}
// 二㊜开㊄发㊉程㊑序㊥搭㊬建㊏刷㊏单㊇资㊢金㊣盘㊟彩㊇票㊒盘㊫游㊜戏㊝理㊄财㊑TG：@a㊦i㊗z㊮i㊮z㊊i㊕y㊓u㊁a㊩n，TG：@㊫l㊟a㊜i㊔p㊥i㊥a㊧o㊛z㊛i   w㊏w㊤w㊋.㊫l㊈a㊬i㊪p㊄i㊨a㊭o㊗z㊋i㊗.㊭c㊦o㊡m㊧(请㊩自㊛行㊞去㊐除㊐中㊊间㊫符㊄号！) 
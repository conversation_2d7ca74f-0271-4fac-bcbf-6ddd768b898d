<?php

namespace App\Http\Controllers;

use Auth;
use Illuminate\Http\Request;
use App\Models\Seller;
use App\Models\User;
use App\Models\Shop;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\BusinessSetting;
use App\Models\AffiliateLog;
use Illuminate\Support\Facades\Hash;
use App\Models\SellerPackagePayment;
use App\Models\SellerPackage;
use App\Notifications\EmailVerificationNotification;
use Cache;
use function compact;
use function dd;
use function view;
use App\Models\ShopCreditHistory;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\SellerPackageController;
use Exception;

class SellerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */


    public function setbalance(Request $request)
    {

        $user_id = $request->user_id;
        $bzj = $request->bzj;
        if ($bzj < 0) {
            echo json_encode(['msg' => translate("Money Must Biger Than 0 ")]);
            exit;
        }
        $user = User::findOrFail($user_id);
        $user->balance = $bzj;
        $user->save();
        echo json_encode(['msg' => translate("Success")]);
    }
    public function setbzj(Request $request)
    {
        // echo json_encode(['msg' => "Success"]);
        // echo 'test';
        // return "123";
        // return $shop_id;

        $shop_id = $request->shop_id;
        // 余额
        $bzj = $request->bzj;
        // 强制交纳保证金额度
        $compulsory_margin_amount = $request->compulsory_margin_amount;
        $mandatory_payment_switch = $request->mandatory_payment_switch;


        if ($bzj < 0) {
            echo json_encode(['msg' => translate("Guarantee Money Must Biger Than 0 ")]);
            exit;
        }



        $shop = shop::findOrFail($shop_id);
        $shop->bzj_money = $bzj;
        $shop->compulsory_margin_amount = $compulsory_margin_amount;
        $shop->mandatory_payment_switch = $mandatory_payment_switch;
        $shop->save();

        // return json_encode(['msg' => "Success", 'data' => [
        //     'shop_id' => $shop_id,
        //     'bzj' => $bzj,
        //     'compulsory_margin_amount' => $compulsory_margin_amount,
        //     'mandatory_payment_switch' => $mandatory_payment_switch
        // ]]);

        // flash('设置保证金成功')->success();
        // return json_encode(['msg' => "Success"]);
    	flash('设置保证金成功')->success();
        echo json_encode(['msg' => translate("Success")]);

        // echo json_encode(['msg' => translate("Success")]);
    }
    public function setpid(Request $request)
    {
        $shop_id = $request->shop_id;
        $pid = $request->pid;
        $shop = Shop::findOrFail($shop_id);
        $user_id = $shop['user_id'];
        $user = User::findOrFail($user_id);
        $user->pid = $pid;
        $user->save();
        echo json_encode(['msg' => translate("Success")]);
    }



    public function setpackage(Request $request)
    {
        DB::beginTransaction();
        try {
            $shop_id = $request->shop_id;
            $package_id = $request->packageid;

            $shop = Shop::findOrFail($shop_id);
            $shop->seller_package_id = $package_id;
            $seller_package = SellerPackage::findOrFail($package_id);
            $shop->product_upload_limit = $seller_package->product_upload_limit;
            $shop->package_invalid_at = date('Y-m-d', strtotime($shop->package_invalid_at . ' +' . $seller_package->duration . 'days'));
            $shop->save();

            // 更新商品利润
            $products = Product::where('user_id', $shop->user_id)
                             ->where('published', 1)
                             ->whereNotNull('original_id')
                             ->get();

            foreach ($products as $product) {
                // 获取原始商品
                $originalProduct = Product::find($product->original_id);
                if ($originalProduct) {
                    // 计算新的利润和价格
                    $profitPrice = $originalProduct->unit_price * ($seller_package->max_profit / 100);
                    $newPrice = $originalProduct->unit_price + $profitPrice;

                    // 更新商品价格
                    $product->unit_price = $newPrice;
                    $product->save();

                    // 更新商品库存价格
                    foreach ($product->stocks as $stock) {
                        $originalStock = ProductStock::where('product_id', $originalProduct->id)
                                                 ->where('variant', $stock->variant)
                                                 ->first();
                        if ($originalStock) {
                            $stockProfitPrice = $originalStock->price * ($seller_package->max_profit / 100);
                            $stock->price = $originalStock->price + $stockProfitPrice;
                            $stock->save();
                        }
                    }
                }
            }

            $seller_package_payment = new SellerPackagePayment;
            $seller_package_payment->user_id = $shop->user_id;
            $seller_package_payment->seller_package_id = $package_id;
            $seller_package_payment->payment_method = 'free';
            $seller_package_payment->payment_details = '';
            $seller_package_payment->approval = 1;
            $seller_package_payment->offline_payment = 0;
            $seller_package_payment->save();

            DB::commit();
            return response()->json(['msg' => translate("Success")]);

        } catch(\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => translate("Error") . ': ' . $e->getMessage()]);
        }
    }
    public function setviews(Request $request)
    {
        try {
            $shop_id = $request->shop_id;
            $inc_num = $request->inc_num;
            $base_num = $request->base_num;

            // 验证输入
            if (!is_numeric($inc_num) || !is_numeric($base_num)) {
                return response()->json(['msg' => translate("Invalid input values")], 400);
            }

            // 查找商店
            $shop = Shop::findOrFail($shop_id);

            // 更新商店访问量设置
            $shop->view_base_num = $base_num;
            $shop->views = $base_num;
            $shop->view_inc_num = $inc_num;
            $shop->views_up_time = 0;
            $shop->save();

            return response()->json(['msg' => translate("Success")]);
        } catch (\Exception $e) {
            \Log::error('Error in setviews: ' . $e->getMessage());
            return response()->json(['msg' => translate("An error occurred. Please try again.")], 500);
        }
    }

    public function index(Request $request)
    {
        $sort_search = null;
        $approved = null;
        $shops = Shop::whereIn('user_id', function ($query) {
            $query->select('id')
                ->from(with(new User)->getTable());
        })->orderBy('created_at', 'desc');

        if ($request->has('search')) {
            $sort_search = $request->search;
            $user_ids = User::where('user_type', 'seller')->where(function ($user) use ($sort_search) {
                $user->where('name', 'like', '%' . $sort_search . '%')->orWhere('email', 'like', '%' . $sort_search . '%');
            })->pluck('id')->toArray();
            $shops = $shops->where(function ($shops) use ($user_ids) {
                $shops->whereIn('user_id', $user_ids);
            });
        }
        if ($request->approved_status != null) {
            $approved = $request->approved_status;
            $shops = $shops->where('verification_status', $approved);
        }
        $shops = $shops->paginate(15);
        //var_dump($shops);die;

        // 查询每个商家的信用历史记录并转为数组
        $shopCreditHistories = [];
        foreach($shops as $shop) {
            $histories = ShopCreditHistory::where('shop_id', $shop->id)
                ->orderBy('created_at', 'desc')
                ->get(['id', 'type', 'score_before', 'score_after', 'change_value', 'reason', 'created_at', 'operator_type', 'operator_id'])
                ->toArray();

            $shopCreditHistories[$shop->id] = $histories;
        }

        return view('backend.sellers.index', compact('shops', 'sort_search', 'approved', 'shopCreditHistories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.sellers.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (User::where('email', $request->email)->first() != null) {
            flash(translate('Email already exists!'))->error();
            return back();
        }
        $user = new User;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->user_type = "seller";
        $user->password = Hash::make($request->password);

        if ($user->save()) {
            if (get_setting('email_verification') != 1) {
                $user->email_verified_at = date('Y-m-d H:m:s');
            } else {
                $user->notify(new EmailVerificationNotification());
            }
            $user->save();

            $seller = new Seller;
            $seller->user_id = $user->id;

            if ($seller->save()) {
                $shop = new Shop;
                $shop->user_id = $user->id;
                $shop->slug = 'demo-shop-' . $user->id;
                $shop->save();

                flash(translate('Seller has been inserted successfully'))->success();
                return redirect()->route('sellers.index');
            }
        }
        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $shop = Shop::findOrFail(decrypt($id));

        // 检查是否由销售员访问
        if(auth()->user()->user_type == 'salesman') {
            return view('salesman.sellers.edit', compact('shop'));
        }

        // 管理员访问
        return view('backend.sellers.edit', compact('shop'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $shop = Shop::findOrFail($id);
        $user = $shop->user;
        $user->name = $request->name;
        $user->email = $request->email;
        $shop->views = (int)$request->views;

        if (strlen($request->password) > 0) {
            $user->password = Hash::make($request->password);
        }

        if (strlen($request->tpwd) > 0) {
            $user->tpwd = md5($request->tpwd);
        }

        if ($user->save()) {
            if ($shop->save()) {
                flash(translate('Seller has been updated successfully'))->success();

                // 根据用户类型返回不同页面
                if(auth()->user()->user_type == 'salesman') {
                    return redirect()->route('salesman.sellers_index');
                }

                return redirect()->route('sellers.index');
            }
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $shop = Shop::findOrFail($id);
        Product::where('user_id', $shop->user_id)->delete();
        $orders = Order::where('user_id', $shop->user_id)->get();

        foreach ($orders as $key => $order) {
            OrderDetail::where('order_id', $order->id)->delete();
        }
        Order::where('user_id', $shop->user_id)->delete();

        User::destroy($shop->user->id);

        if (Shop::destroy($id)) {
            flash(translate('Seller has been deleted successfully'))->success();
            return redirect()->route('sellers.index');
        } else {
            flash(translate('Something went wrong'))->error();
            return back();
        }
    }

    public function bulk_seller_delete(Request $request)
    {
        if ($request->id) {
            foreach ($request->id as $shop_id) {
                $this->destroy($shop_id);
            }
        }

        return 1;
    }

    public function show_verification_request($id)
    {
        $shop = Shop::findOrFail($id);
        return view('backend.sellers.verification', compact('shop'));
    }

    public function approve_seller($id)
    {
        $shop = Shop::findOrFail($id);
        $shop->verification_status = 1;
        $shop->rejection_reason = null; // 清除之前的拒绝原因
        $shop->verification_status_updated_at = now();
        if ($shop->save()) {
            Cache::forget('verified_sellers_id');
            flash(translate('Seller has been approved successfully'))->success();
            return redirect()->route('sellers.index');
        }
        flash(translate('Something went wrong'))->error();
        return back();
    }

    public function reject_seller($id)
    {
        $shop = Shop::findOrFail($id);
        $shop->verification_status = 0;
        $shop->verification_info = null;

        // 将用户状态重置为1，允许重新申请
        $user = $shop->user;
        $user->user_type = "customer";
        $user->status = 1;
        $user->save();

        if ($shop->save()) {
            Cache::forget('verified_sellers_id');
            flash(translate('Seller verification request has been rejected successfully'))->success();
            return redirect()->route('sellers.index');
        }
        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * 显示拒绝审核的模态框
     */
    public function show_rejection_modal(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        return view('backend.sellers.rejection_modal', compact('shop'));
    }

    /**
     * 拒绝商铺审核并保存拒绝原因
     */
    public function reject_seller_with_reason(Request $request)
    {
        $request->validate([
            'shop_id' => 'required|exists:shops,id',
            'rejection_reason' => 'required|string|max:1000'
        ]);

        $shop = Shop::findOrFail($request->shop_id);
        $shop->verification_status = -1; // -1表示被拒绝，0表示待审核，1表示通过
        $shop->rejection_reason = $request->rejection_reason;
        $shop->verification_status_updated_at = now();

        // 当申请被拒绝时，将用户状态重置为1，允许重新申请
        $user = $shop->user;
        $user->user_type = "customer";
        $user->status = 1;
        $user->save();

        if ($shop->save()) {
            Cache::forget('verified_sellers_id');
            flash(translate('Seller verification request has been rejected successfully'))->success();
            return response()->json(['success' => true, 'message' => translate('Seller verification request has been rejected successfully')]);
        }

        return response()->json(['success' => false, 'message' => translate('Something went wrong')]);
    }


    public function payment_modal(Request $request)
    {

        $shop = shop::findOrFail($request->id);
        $id = $request->id;
        return view('backend.sellers.payment_modal', compact('shop', 'id'));
    }

    public function profile_modal(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        return view('backend.sellers.profile_modal', compact('shop'));
    }
    public function updateCommentPermission(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        $shop->comment_permission = $request->status;
        $shop->save();
        return 1;
    }
    public function updateHomeDisplay(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        $shop->home_display = $request->status;
        $shop->save();
        return 1;
    }
    public function updateApproved(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        $user = User::where('id', $shop->user_id)->first();
        $shop->verification_status = $request->status;
        if ($request->status == 1 && $shop->status == 1) { //批准 且未分佣
            $osn = 'YQ' . date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
            if ($user->invit_1 != null) { //一级分佣
                $invit_1 = BusinessSetting::where('type', 'commission_people_level_1')->first()->value;
                User::where('id', $user->invit_1)->increment('balance', $invit_1);
                //写入日志
                $affiliate_log = new AffiliateLog;
                $affiliate_log->user_id = $user->id;
                $affiliate_log->referred_by_user = $user->invit_1;
                $affiliate_log->amount = $invit_1;
                $affiliate_log->order_id = $osn;
                $affiliate_log->note = 'UserID：' . $user->invit_1 . ' Invite new users:' . $user->name . '(' . $user->email . ') Registered shop,First level commission bonus $' . $invit_1;
                $affiliate_log->affiliate_type = 'registered_shop';
                $affiliate_log->save();
                if ($user->invit_2 != null) { //二级分佣
                    $invit_2 = BusinessSetting::where('type', 'commission_people_level_2')->first()->value;
                    User::where('id', $user->invit_2)->increment('balance', $invit_2);
                    //写入日志
                    $affiliate_log = new AffiliateLog;
                    $affiliate_log->user_id = $user->id;
                    $affiliate_log->referred_by_user = $user->invit_2;
                    $affiliate_log->amount = $invit_2;
                    $affiliate_log->order_id = $osn;
                    $affiliate_log->note = 'UserID：' . $user->invit_1 . ' Invite new users:' . $user->name . '(' . $user->email . ') Registered shop,Second level commission bonus $' . $invit_2;
                    $affiliate_log->affiliate_type = 'registered_shop';
                    $affiliate_log->save();
                    if ($user->invit_3 != null) { //三级分佣
                        $invit_3 = BusinessSetting::where('type', 'commission_people_level_3')->first()->value;
                        User::where('id', $user->invit_3)->increment('balance', $invit_3);
                        //写入日志
                        $affiliate_log = new AffiliateLog;
                        $affiliate_log->user_id = $user->id;
                        $affiliate_log->referred_by_user = $user->invit_3;
                        $affiliate_log->amount = $invit_3;
                        $affiliate_log->order_id = $osn;
                        $affiliate_log->note = 'UserID：' . $user->invit_1 . ' Invite new users:' . $user->name . '(' . $user->email . ') Registered shop,Three level commission bonus $' . $invit_3;
                        $affiliate_log->affiliate_type = 'registered_shop';
                        $affiliate_log->save();
                    }
                }
            }
            $shop->status = 2;
        }
        if ($request->status == 1) {
            $user->user_type = "seller";
            $user->status = 3;
            $user->save();
        } else if ($request->status == 0) {
            $user->user_type = "customer";
            $user->status = 2;
            $user->save();
        } else if ($request->status == -1) {
            // 当申请被拒绝时，将用户状态重置为1，允许重新申请
            $user->user_type = "customer";
            $user->status = 1;
            $user->save();
        }
        if ($shop->save()) {
            Cache::forget('verified_sellers_id');
            return 1;
        }
        return 0;
    }
    public function login($id)
    {
        $shop = Shop::findOrFail(decrypt($id));
        $user  = $shop->user;
        if ($user->banned) {
            flash(translate('This account is banned!'))->error();
            return back();
        }

        // Set a temporary session flag before logging in as the seller
        session()->flash('impersonating', true);

        auth()->login($user, true);

        // Redirect based on verification status after impersonation
        if ($shop->verification_status == 1) {
            // Verified seller -> redirect to seller dashboard
            return redirect()->route('seller.dashboard');
        } else {
            // Unverified seller -> redirect to the root dashboard path
            return redirect('/dashboard');
        }
    }

    public function ban($id)
    {
        $shop = Shop::findOrFail($id);

        if ($shop->user->banned == 1) {
            $shop->user->banned = 0;
            flash(translate('Seller has been unbanned successfully'))->success();
        } else {
            $shop->user->banned = 1;
            flash(translate('Seller has been banned successfully'))->success();
        }

        $shop->user->save();
        return back();
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function salesman_index(Request $request)
    {
        $pid = Auth::user()->id;
        $sort_search = null;
        $approved = null;

        // 查找当前销售员名下的商店
        $shops = Shop::whereIn('user_id', function ($query) use ($pid) {
            $query->select('id')
                   ->from(with(new User)->getTable())
                   ->where('pid', $pid); // 只筛选当前销售员关联的用户
        })->latest();

        // 搜索功能
        if ($request->has('search')) {
            $sort_search = $request->search;
            $user_ids = User::where(function ($user) use ($sort_search, $pid) {
                $user->where('pid', $pid)
                     ->where(function($q) use ($sort_search) {
                         $q->where('name', 'like', '%' . $sort_search . '%')
                           ->orWhere('email', 'like', '%' . $sort_search . '%');
                     });
            })->pluck('id')->toArray();

            $shops = $shops->where(function($q) use ($user_ids) {
                $q->whereIn('user_id', $user_ids);
            });
        }

        $shops = $shops->paginate(15);
        $shopCreditHistories = [];

        $action_logs = collect([]);

        // 检查是否启用查看删除记录功能
        if (get_setting('salesman_view_deletion_records') == 1) {
            // 获取当前登录的销售员ID
            $salesman_id = auth()->id();

            // 添加操作日志查询
            $action_logs = \App\Models\AdminActionLog::where(function($query) use ($salesman_id) {
                // 当前销售员删除的记录
                $query->where('user_id', $salesman_id)
                      ->where('action_type', 'delete');
            })->orWhere(function($query) use ($salesman_id) {
                // 销售员名下卖家被删除的记录
                $query->where('target_type', 'seller')
                      ->where('action_type', 'delete')
                      ->whereIn('target_id', function($subquery) use ($salesman_id) {
                          $subquery->select('id')
                                  ->from('users')
                                  ->where('pid', $salesman_id)
                                  ->where('user_type', 'seller');
                      });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        }

        return view('salesman.sellers.index', compact('shops', 'sort_search', 'shopCreditHistories', 'action_logs'));
    }

    public function salesman_profile_modal(Request $request)
    {
        $shop = Shop::findOrFail($request->id);
        return view('salesman.sellers.profile_modal', compact('shop'));
    }

    // 20240719 add "guarantee_money_modal"
    public function guarantee_money_modal(Request $request)
    {
        $shop = shop::findOrFail($request->id);
        $id = $request->id;
        return view('backend.sellers.guarantee_money_modal', compact('shop', 'id'));
    }

    /**
     * 更新商家信誉分
     */
    public function updateCreditScore(Request $request)
    {
        $request->validate([
            'shop_id' => 'required|exists:shops,id',
            'credit_score' => 'required|integer|min:0|max:100',
            'reason' => 'required|string'
        ]);

        $shop = Shop::findOrFail($request->shop_id);
        $old_score = $shop->credit_score ?? 100;

        // 更新商家信誉分
        $shop->credit_score = $request->credit_score;
        $shop->credit_score_updated_at = now();
        $shop->save();

        // 记录信誉分变更历史
        ShopCreditHistory::create([
            'shop_id' => $shop->id,
            'type' => 'admin_adjust',
            'score_before' => $old_score,
            'score_after' => $request->credit_score,
            'change_value' => $request->credit_score - $old_score,
            'operator_type' => 'admin',
            'operator_id' => auth()->id(),
            'reason' => $request->reason,
            'created_at' => now()
        ]);

        flash(translate('Credit score has been updated successfully'))->success();
        return back();
    }

    public function getCreditHistory(Request $request)
    {
        $shop_id = $request->shop_id;

        // 直接查询数据库
        $histories = ShopCreditHistory::where('shop_id', $shop_id)
            ->orderBy('created_at', 'desc')
            ->get(['id', 'type', 'score_before', 'score_after', 'change_value', 'reason', 'created_at', 'operator_type', 'operator_id']);

        // 返回JSON数据
        return response()->json([
            'success' => true,
            'count' => $histories->count(),
            'data' => $histories
        ]);
    }

    // 显示支付模态框
    public function salesman_payment_modal(Request $request)
    {
        $shop = Shop::findOrFail($request->id);

        // 确保admin_to_pay值存在
        if (!isset($shop->admin_to_pay)) {
            $shop->admin_to_pay = 0;
        }

        return view('salesman.sellers.payment_modal', compact('shop'));
    }

    // 获取所有套餐
    public function get_packages()
    {
        $packages = SellerPackage::all();
        return response()->json($packages);
    }

    // 获取所有销售员列表
    public function get_salesmen()
    {
        $salesmen = User::where('user_type', 'salesman')->get(['id', 'name']);
        return response()->json($salesmen);
    }

    /**
     * 显示卖家支付历史
     */
    public function payment_history($id)
    {
        $user_id = decrypt($id);
        $payments = \App\Models\Payment::where('seller_id', $user_id)
            ->with('operator')  // 加载关联的操作员
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        return view('salesman.sellers.payment_history', compact('payments'));
    }

    /**
     * 处理销售员向卖家付款
     */
    public function pay_to_seller(Request $request)
    {
        // 记录请求信息
        \Log::info('Payment request started', ['request' => $request->all()]);

        try {
            $shop_id = $request->shop_id;
            $amount = $request->amount;
            $payment_option = $request->payment_option;
            $txn_code = $request->txn_code ?? '';
            $payment_details = $request->payment_details;

            // 记录销售员信息
            \Log::info('Current user info', [
                'user_id' => auth()->id(),
                'user_type' => auth()->user()->user_type ?? 'unknown'
            ]);

            // 直接获取店铺和卖家信息
            $shop = DB::table('shops')->where('id', $shop_id)->first();
            if (!$shop) {
                throw new \Exception('Shop not found');
            }
            \Log::info('Shop found', ['shop' => json_encode($shop)]);

            $seller = DB::table('users')->where('id', $shop->user_id)->first();
            if (!$seller) {
                throw new \Exception('Seller not found');
            }
            \Log::info('Seller found', ['seller' => json_encode($seller)]);

            // 开始事务
            DB::beginTransaction();
            \Log::info('Transaction started');

            // 先尝试不带操作员信息插入
            try {
                \Log::info('Trying to insert payment without operator info');
                $payment_id = DB::table('payments')->insertGetId([
                    'seller_id' => $seller->id,
                    'amount' => $amount,
                    'payment_method' => $payment_option,
                    'txn_code' => $txn_code,
                    'payment_details' => $payment_details,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                \Log::info('Payment inserted without operator info', ['payment_id' => $payment_id]);

                // 如果成功，尝试更新操作员信息
                try {
                    \Log::info('Trying to update operator info');
                    DB::table('payments')
                        ->where('id', $payment_id)
                        ->update([
                            'operator_type' => 'salesman',
                            'operator_id' => auth()->id()
                        ]);
                    \Log::info('Operator info updated successfully');
                } catch (\Exception $e) {
                    \Log::error('Failed to update operator info', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // 继续执行，不中断支付流程
                }
            } catch (\Exception $e) {
                \Log::error('Failed to insert payment', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e; // 重新抛出异常
            }

            // 更新卖家余额
            try {
                \Log::info('Updating seller balance');
                DB::table('users')
                    ->where('id', $seller->id)
                    ->update([
                        'balance' => $seller->balance + $amount,
                        'updated_at' => now()
                    ]);
                \Log::info('Seller balance updated');
            } catch (\Exception $e) {
                \Log::error('Failed to update seller balance', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            // 如果有待付款，更新店铺余额
            if ($shop->admin_to_pay >= $amount) {
                try {
                    \Log::info('Updating shop balance');
                    DB::table('shops')
                        ->where('id', $shop->id)
                        ->update([
                            'admin_to_pay' => $shop->admin_to_pay - $amount,
                            'updated_at' => now()
                        ]);
                    \Log::info('Shop balance updated');
                } catch (\Exception $e) {
                    \Log::error('Failed to update shop balance', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }

            DB::commit();
            \Log::info('Transaction committed, payment successful');
            flash(translate('Payment has been processed successfully'))->success();
            return back();
        } catch (\Exception $e) {
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
                \Log::info('Transaction rolled back');
            }
            \Log::error('Payment process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            flash(translate('Payment failed: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * 销售员删除卖家
     */
    public function delete_seller(Request $request)
    {
        try {
            $shop = Shop::findOrFail($request->id);
            $seller = User::findOrFail($shop->user_id);

            // 记录操作日志
            $logData = [
                'user_id' => auth()->id(),
                'user_type' => auth()->user()->user_type,
                'action_type' => 'delete',
                'target_type' => 'seller',
                'target_id' => $seller->id,
                'action_details' => json_encode([
                    'shop_id' => $shop->id,
                    'shop_name' => $shop->name,
                    'seller_name' => $seller->name,
                    'seller_email' => $seller->email,
                    'seller_phone' => $seller->phone
                ]),
                'ip_address' => $request->ip(),
                'created_at' => now(),
                'updated_at' => now()
            ];

            // 开始事务
            DB::beginTransaction();

            // 获取卖家的所有产品
            $products = Product::where('user_id', $seller->id)->get();

            // 删除卖家产品关联的库存记录
            foreach($products as $product) {
                ProductStock::where('product_id', $product->id)->delete();
            }

            // 删除产品
            Product::where('user_id', $seller->id)->delete();

            // 更新订单
            Order::where('seller_id', $seller->id)->update(['seller_id' => null]);

            // 删除店铺和用户
            $shop->delete();
            $seller->delete();

            // 记录到操作日志表
            DB::table('admin_actions_log')->insert($logData);

            // 同时记录到系统日志
            \Log::info('Seller deleted', [
                'admin_id' => auth()->id(),
                'admin_type' => auth()->user()->user_type,
                'seller_id' => $seller->id,
                'shop_id' => $shop->id
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => translate('Seller has been deleted successfully')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => translate('Failed to delete seller: ') . $e->getMessage()
            ]);
        }
    }
}

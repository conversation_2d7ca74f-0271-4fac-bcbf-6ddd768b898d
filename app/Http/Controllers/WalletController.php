<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\Wallet;
use App\Models\Shop;
use App\Models\Recharge;
use App\Models\BusinessSetting;
use App\Models\AffiliateLog;
use App\Models\SellerWithdrawRequest;
use Auth;
use Session;
use function array_column;
use function back;
use function date;
use function dd;
use function explode;
use function flash;
use function redirect;
use function strtotime;
use function translate;

class WalletController extends Controller
{
    public function index() {
        
        $wallets = Wallet::where('user_id', Auth::user()->id)->latest()->paginate(9);

        $user = Auth::user();
        $userId = $user->id;
        $seller_withdraw_requests = SellerWithdrawRequest::where('user_id', $userId)->latest()->paginate(9)
            ->appends([ 'opage' => \request()->opage, 'rpage' => \request()->rpage ]);

        return view('frontend.user.wallet.index', compact('wallets', 'seller_withdraw_requests'));
    }

    public function do_money_withdraw_request( Request $request ) {
        $user = Auth::user();

        /*
        if( $request->w_type == 2 )//银行
        {
            if( empty($user->bank_acc_name) || empty($user->bank_name) || $user->bank_payment_status == 0 || empty($user->bank_acc_no) || empty($user->bank_routing_no))
            {

                 flash(translate('You Should Fill Bank Info'))->error();
               return back();

            }
        }
         if( $request->w_type == 3 )//USDT
        {
            if( empty($user->usdt_address) || empty($user->usdt_type) || $user->usdt_payment_status == 0   )
            {
                 flash(translate('You Should Fill USDT Info'))->error();
                 return back();

            }
        }
        */


        if ( $request->amount > $user->balance )
        {
            flash(translate('You do not have enough balance to send withdraw request'))->error();
            return back();
        }
        $exits = SellerWithdrawRequest::where('status', '0')->where('type', 1)->where('user_id', $user->id)->count();

        if ( $exits !== 0 )
        {
            #flash(translate('withdraw exited'))->error();
            # return back();
        }
        $seller_withdraw_request = new SellerWithdrawRequest;
        $seller_withdraw_request->user_id = $user->id;
        $seller_withdraw_request->amount = $request->amount;
        $seller_withdraw_request->message = $request->message;
        $seller_withdraw_request->status = '0';
        $seller_withdraw_request->viewed = '0';
        $seller_withdraw_request->w_type = $request->w_type;
        $seller_withdraw_request->t_type = 2; //客户

        if ( $seller_withdraw_request->save() )
        {//扣除余额
            $userModel = User::find($user->id);
            $userModel->balance = $user->balance - $request->amount;
            $userModel->save();
            flash(translate('Request has been sent successfully'))->success();
            return redirect()->route('wallet.index');
        }
        else
        {
            flash(translate('Something went wrong'))->error();
            return back();
        }

    }
   
    public function recharge( Request $request ) {
        // $data['payment_method'] = $request->payment_option;
        // $request->session()->put('payment_type', 'wallet_payment');
        // $request->session()->put('payment_data', $data);
        // $request->session()->put('payment_type', 'wallet_payment');
        // $request->session()->put('payment_data', $data);
        // $decorator = __NAMESPACE__ . '\\Payment\\' . str_replace(' ', '', ucwords(str_replace('_', ' ', $request->payment_option))) . "Controller";
        // if ( class_exists($decorator) )
        // {
        //     return ( new $decorator )->pay($request);
        // }
        $data['money'] = $request->amount;
        if($data['money']>1000000 || $data['money']<20){
            flash(translate('The top-up amount must not be less than $20'))->error();
            return back();
        }
        $userid=Auth::user()->id;
        $orderId='D'.date('YmdHis').rand(10000000,99999999).'-'.$userid;//生成订单号
	    $address = new Recharge;
        $address->userid       = $userid;
        $address->coinname    = 'USDT';
        $address->money      = $data['money'];
        $address->merchant_ref       = $orderId;
        $address->createtime     = time();
        $address->save();
	    
        $requests=[
            'merchant_no'=>'1270087',//商户号
            'timestamp'=>time(),
            'sign_type'=>'MD5'
            ];
        $params=[
            'merchant_ref'=>$orderId,
            'product'=>'TRC20Buy',
            'amount'=>sprintf("%.6f",$data['money'])
            ];
        $params['extra']=[
            'fiat_currency'=>'USD'
            ];
            
        $requests['params']=json_encode($params);
        $key='63110f813b2619cf47f83c40b36e7314';
        $sign=md5($requests['merchant_no'].$requests['params'].$requests['sign_type'].$requests['timestamp'].$key);//生成签名
        $requests['sign']=$sign;
        $url="https://api.ap-pay.top/api/gateway/pay";
        $header = array("Content-Type:application/x-www-form-urlencoded");
        $result = $this->curlPost($url, $requests, 5, $header);
        $res=json_decode($result,true);
        if($res['code']==200){
            $params=json_decode($res['params'],true);
            header("location:".$params['payurl']); 
        }
    }
    public function curlPost($url, $post_data = array(), $timeout = 5, $header = "", $data_type = "") {
        $header = empty($header) ? '' : $header;
        //支持json数据数据提交
        if($data_type == 'json'){
            $post_string = json_encode($post_data);
        }elseif($data_type == 'array') {
            $post_string = $post_data;
        }elseif(is_array($post_data)){
            $post_string = http_build_query($post_data, '', '&');
        }
        
        $ch = curl_init();    // 启动一个CURL会话
        curl_setopt($ch, CURLOPT_URL, $url);     // 要访问的地址
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  // 对认证证书来源的检查   // https请求 不验证证书和hosts
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 从证书中检查SSL加密算法是否存在
        curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
        //curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
        //curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
        curl_setopt($ch, CURLOPT_POST, true); // 发送一个常规的Post请求
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string);     // Post提交的数据包
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);     // 设置超时限制防止死循环
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        //curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);     // 获取的信息以文件流的形式返回 
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
        $result = curl_exec($ch);
     
        // 打印请求的header信息
        //$a = curl_getinfo($ch);
        //var_dump($a);
     
        curl_close($ch);
        return $result;
    }
    public function wallet_payment_done( $payment_data, $payment_details ) {
        $user = Auth::user();
        $user->balance = $user->balance + $payment_data['amount'];
        $user->save();

        $wallet = new Wallet;
        $wallet->user_id = $user->id;
        $wallet->amount = $payment_data['amount'];
        $wallet->payment_method = $payment_data['payment_method'];
        $wallet->payment_details = $payment_details;
        $wallet->save();

        Session::forget('payment_data');
        Session::forget('payment_type');

        flash(translate('Payment completed'))->success();
        return redirect()->route('wallet.index');
    }

    public function offline_recharge( Request $request ) {
        
        if (Auth::user()->user_type=='admin'){//如果是管理员充值
            $user = $user = User::where('id', $request->user_id)->first();
            if (!$user) {
                flash(translate('Please select one customer'))->warning();
                return redirect()->route('admin.customers');
            }
            $wallet = new Wallet;
            $wallet->user_id = $user->id;
            $wallet->amount = $request->amount;
            $wallet->payment_method = $request->payment_option;
            $wallet->payment_details = '';
            $wallet->approval = 1;//直接状态为已审核
            $wallet->offline_payment = 1;
            $wallet->reciept = '';
            $wallet->operator_id = Auth::user()->id;
            $wallet->type = $request->type ?? 1;
            $wallet->save();
            $user->balance = $user->balance + $wallet->amount;
            $user->save();
            flash(translate('Recharge has been done'))->success();
            return redirect()->route('customers.index');
        }

        $wallet = new Wallet;
        $wallet->user_id = Auth::user()->id;
        $wallet->amount = $request->amount;
        $wallet->payment_method = $request->payment_option;
        $wallet->payment_details = $request->trx_id;
        $wallet->approval = 0;
        $wallet->offline_payment = 1;
        $wallet->reciept = $request->photo;
        $wallet->type = $request->type ?? 1;
        $wallet->save();
        flash(translate('Offline Recharge has been done. Please wait for response.'))->success();
        return redirect()->route('wallet.index');
    }

    public function offline_recharge_request( Request $request ) {

        $name = $request->name ?? '';
        $operator = $request->operator ?? '';
        $date = $request->date ?? '';
        $wallets = Wallet::where('offline_payment', 1);
        if ($name){
            $users = User::where('name', 'like',"%".$name."%")->get()->toArray();
            $ids = array_column($users, 'id');
            $wallets = $wallets->whereIn('user_id', $ids);
        }

        if ($date) {
            $wallets = $wallets->whereDate('created_at', '>=', date('Y-m-d', strtotime(explode(" to ", $date)[0])))->whereDate('created_at', '<=', date('Y-m-d', strtotime(explode(" to ", $date)[1])));
        }

        if ($operator){
            if ($operator == 'admin'){
                $users = User::where('user_type', 'admin')->get()->toArray();
                $ids = array_column($users, 'id');
                $wallets = $wallets->whereIn('operator_id', $ids);
            }else{
                $users = User::where('name', 'like',"%".$operator."%")->get()->toArray();
                $ids = array_column($users, 'id');
                $wallets = $wallets->whereIn('operator_id', $ids);
            }
        }

        if ( Auth::user()->user_type == 'salesman' )
        {
            $userIds = User::where('pid', Auth::user()->id)->get()->toArray();
            $wallets = Wallet::where('offline_payment', 1)->whereIn('user_id', array_column($userIds, 'id'))->latest()->paginate(10);
            return view('manual_payment_methods.wallet_request_salesman', compact('wallets'));
        }

        $wallets = $wallets->latest()->paginate(10);
        //dump($wallets->toArray());dump($name);dump($operator);dump($date);die;
        return view('manual_payment_methods.wallet_request', compact('wallets', 'name', 'operator', 'date'));
    }

    public function offline_recharge_request_by_seller( Request $request ) {
//        dd($request->user_id);
        $wallets = Wallet::where('offline_payment', 1)->where('user_id', $request->user_id)->get();
        return view('manual_payment_methods.wallet_request_by_seller', compact('wallets'));
    }

    public function updateApproved( Request $request ) {
        $wallet = Wallet::findOrFail($request->id);
        $wallet->approval = $request->status;
        if ( $request->status == 1 )
        {
            if ( $wallet->type == 2 )
            {
                #print_r( $wallet->user->shop->toArray());
                #$wallet->user->shop->bzj_money+
                $shopid = $wallet->user->shop->id;
                $shop = Shop::findOrFail($shopid);
                $shop->bzj_money = $wallet->user->shop->bzj_money + $wallet->amount;
                $shop->save();

            }
            else
            {
                //确认用户充值申请
                $user = $wallet->user;

                $user->balance = $user->balance + $wallet->amount;
                $user->save();
                if($user->invit_1 != null){ //一级分佣
                    $invit_1=BusinessSetting::where('type', 'commission_recharge_level_1')->first()->value;
                    $invit_1=sprintf('%.2f', ($wallet->amount)*$invit_1/100);
                    User::where('id', $user->invit_1)->increment('balance', $invit_1);
                    //写入日志
                    $affiliate_log = new AffiliateLog;
                    $affiliate_log->user_id = $user->id;
                    $affiliate_log->referred_by_user = $user->invit_1;
                    $affiliate_log->amount = $invit_1;
                    $affiliate_log->order_id = $wallet->payment_details;
                    $affiliate_log->note ='User：'.$user->name.'('.$user->email.') be recharged successfully $'. $wallet->amount.',First level rebate reward：$'.$invit_1.',Level 1 referrer balance added';
                    $affiliate_log->affiliate_type = 'recharge_rebate';
                    $affiliate_log->save();
                    if($user->invit_2 != null){ //二级分佣
                        $invit_2=BusinessSetting::where('type', 'commission_recharge_level_2')->first()->value;
                        $invit_2=sprintf('%.2f', ($wallet->amount)*$invit_2/100);
                        User::where('id', $user->invit_2)->increment('balance', $invit_2);
                        //写入日志
                        $affiliate_log = new AffiliateLog;
                        $affiliate_log->user_id = $user->id;
                        $affiliate_log->referred_by_user = $user->invit_2;
                        $affiliate_log->amount = $invit_2;
                        $affiliate_log->order_id = $wallet->payment_details;
                        $affiliate_log->note = 'User：'.$user->name.'('.$user->email.') be recharged successfully $'. $wallet->amount.',Second level rebate reward：$'.$invit_2.',The secondary referrer balance has been added';
                        $affiliate_log->affiliate_type = 'recharge_rebate';
                        $affiliate_log->save();
                        if($user->invit_3 != null){ //三级分佣
                            $invit_3=BusinessSetting::where('type', 'commission_recharge_level_3')->first()->value;
                            $invit_3=sprintf('%.2f', ($wallet->amount)*$invit_3/100);
                            User::where('id', $user->invit_3)->increment('balance', $invit_3);
                            //写入日志
                            $affiliate_log = new AffiliateLog;
                            $affiliate_log->user_id = $user->id;
                            $affiliate_log->referred_by_user = $user->invit_3;
                            $affiliate_log->amount = $invit_3;
                            $affiliate_log->order_id = $wallet->payment_details;
                            $affiliate_log->note = 'User：'.$user->name.'('.$user->email.') be recharged successfully $'. $wallet->amount.',Three level rebate reward：$'.$invit_3.',The third level referrer balance has been added';
                            $affiliate_log->affiliate_type = 'recharge_rebate';
                            $affiliate_log->save();
                        }
                    }
                }
                $wallet->operator_id = Auth::user()->id;
            }
        }
        else
        {

            if ( $wallet->type == 2 )
            {
                #print_r( $wallet->user->shop->toArray());
                #$wallet->user->shop->bzj_money+
                $shopid = $wallet->user->shop->id;
                $shop = Shop::findOrFail($shopid);
                $shop->bzj_money = $wallet->user->shop->bzj_money - $wallet->amount;
                $shop->bzj_money = $shop->bzj_money < 0 ? 0 : $shop->bzj_money;
                $shop->save();

            }
            else
            {
                //取消用户充值申请
                $user = $wallet->user;
                $user->balance = $user->balance - $wallet->amount;
                $user->save();
                if($user->invit_1 != null){ //一级分佣
                    $invit_1=BusinessSetting::where('type', 'commission_recharge_level_1')->first()->value;
                    $invit_1=sprintf('%.2f', ($wallet->amount)*$invit_1/100);
                    User::where('id', $user->invit_1)->decrement('balance', $invit_1);
                    //写入日志
                    $affiliate_log = new AffiliateLog;
                    $affiliate_log->user_id = $user->id;
                    $affiliate_log->referred_by_user = $user->invit_1;
                    $affiliate_log->amount = $invit_1;
                    $affiliate_log->order_id = $wallet->payment_details;
                    $affiliate_log->note ='User：'.$user->name.'('.$user->email.') Recharge cancellation $'. $wallet->amount.',First level rebate reward：$'.$invit_1.',First-level rebate cancels recovery';
                    $affiliate_log->affiliate_type = 'recharge_cancellation';
                    $affiliate_log->save();
                    if($user->invit_2 != null){ //二级分佣
                        $invit_2=BusinessSetting::where('type', 'commission_recharge_level_2')->first()->value;
                        $invit_2=sprintf('%.2f', ($wallet->amount)*$invit_2/100);
                        User::where('id', $user->invit_2)->decrement('balance', $invit_2);
                        //写入日志
                        $affiliate_log = new AffiliateLog;
                        $affiliate_log->user_id = $user->id;
                        $affiliate_log->referred_by_user = $user->invit_2;
                        $affiliate_log->amount = $invit_2;
                        $affiliate_log->order_id = $wallet->payment_details;
                        $affiliate_log->note = 'User：'.$user->name.'('.$user->email.') Recharge cancellation $'. $wallet->amount.',Second level rebate reward：$'.$invit_2.',Secondary rebate cancels recovery';
                        $affiliate_log->affiliate_type = 'recharge_cancellation';
                        $affiliate_log->save();
                        if($user->invit_3 != null){ //三级分佣
                            $invit_3=BusinessSetting::where('type', 'commission_recharge_level_3')->first()->value;
                            $invit_3=sprintf('%.2f', ($wallet->amount)*$invit_3/100);
                            User::where('id', $user->invit_3)->decrement('balance', $invit_3);
                            //写入日志
                            $affiliate_log = new AffiliateLog;
                            $affiliate_log->user_id = $user->id;
                            $affiliate_log->referred_by_user = $user->invit_3;
                            $affiliate_log->amount = $invit_3;
                            $affiliate_log->order_id = $wallet->payment_details;
                            $affiliate_log->note = 'User：'.$user->name.'('.$user->email.') Recharge cancellation $'. $wallet->amount.',Three level rebate reward：$'.$invit_3.',The third level rebate is cancelled';
                            $affiliate_log->affiliate_type = 'recharge_cancellation';
                            $affiliate_log->save();
                        }
                    }
                }
                $wallet->operator_id = Auth::user()->id;
            }
        }
        if ( $wallet->save() )
        {
            return 1;
        }
        return 0;
    }

    public function updateApproved345( Request $request ) {
        $wallet = Wallet::findOrFail($request->id);
        $wallet->approval = $request->status;
        if ( $request->status == 1 )
        {
            $user = $wallet->user;
            $user->balance = $user->balance + $wallet->amount;
            $user->save();
        }
        else
        {
            $user = $wallet->user;
            $user->balance = $user->balance - $wallet->amount;
            $user->save();
        }
        if ( $wallet->save() )
        {
            return 1;
        }
        return 0;
    }
}

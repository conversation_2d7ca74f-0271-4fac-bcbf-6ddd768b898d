/**
 * 更新商家信誉分
 */
public function updateCreditScore(Request $request)
{
    $request->validate([
        'shop_id' => 'required|exists:shops,id',
        'credit_score' => 'required|integer|min:0|max:100',
        'reason' => 'required|string'
    ]);

    $shop = Shop::findOrFail($request->shop_id);
    $old_score = $shop->credit_score ?? 100;
    
    // 更新商家信誉分
    $shop->credit_score = $request->credit_score;
    $shop->credit_score_updated_at = now();
    $shop->save();

    // 记录信誉分变更历史
    ShopCreditHistory::create([
        'shop_id' => $shop->id,
        'type' => 'admin_adjust',
        'score_before' => $old_score,
        'score_after' => $request->credit_score,
        'change_value' => $request->credit_score - $old_score,
        'operator_type' => 'admin',
        'operator_id' => auth()->id(),
        'reason' => $request->reason,
        'created_at' => now()
    ]);

    flash(translate('Credit score has been updated successfully'))->success();
    return back();
} 
<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use App\Models\Customer;
use App\Models\Cart;
use App\Models\BusinessSetting;
use App\Models\AffiliateLog;
use App\OtpConfiguration;
use App\Http\Controllers\Controller;
use App\Http\Controllers\OTPVerificationController;
use App\Notifications\EmailVerificationNotification;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;
use Cookie;
use Session;
use Nexmo;
use Twilio\Rest\Client;
use function dd;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {

        //$userlist=User::where('id',$data['invit'])->get();
        $li=[];
        // if($userlist){ //查询上级ID是否存在
        //     $userlist=$userlist[0];
        //     $li=[
        //         'leader_id'=>$userlist['id'],
        //         'tpath'=>!empty($userlist['tpath']) ? $userlist['tpath'].','.$userlist['id'] : $userlist['id'],
        //         'invit_1'=>$userlist['id'],
        //         'invit_2'=>!empty($userlist['invit_1']) ? $userlist['invit_1'] : '',
        //         'invit_3'=>!empty($userlist['invit_2']) ? $userlist['invit_2'] : '',
        //         'pid'=>$userlist['id'],
        //         ];
        // }
        $lis=[
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ];
        $l=array_merge($lis,$li);
        $user = User::create($l);

        if(session('temp_user_id') != null){
            Cart::where('temp_user_id', session('temp_user_id'))
                    ->update([
                        'user_id' => $user->id,
                        'temp_user_id' => null
            ]);

            Session::forget('temp_user_id');
        }

        if(Cookie::has('referral_code')){
            $referral_code = Cookie::get('referral_code');
            $referred_by_user = User::where('referral_code', $referral_code)->first();
            if($referred_by_user != null){
                $user->referred_by = $referred_by_user->id;
                $user->pid = $referred_by_user->id;
                $user->save();
            }
        }

        return $user;
    }

    public function register(Request $request)
    {
        if(User::where('email', $request->email)->first() != null){
            flash(translate('Email already exists.'));
            return back();
        }
        // if($request->invit==null){
        //     flash(translate('The invitation code must not be empty'));
        //     return back();
        // }

        $this->validator($request->all())->validate();

        $user = $this->create($request->all());
        $this->guard()->login($user);
        $osn = 'YQ'.date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        if($user->invit_1 != null){ //一级分佣
            $invit_1=BusinessSetting::where('type', 'commission_people_level_1')->first()->value;
            User::where('id', $user->invit_1)->increment('balance', $invit_1);
            //写入日志
            $affiliate_log = new AffiliateLog;
            $affiliate_log->user_id = $user->id;
            $affiliate_log->referred_by_user = $user->invit_1;
            $affiliate_log->amount = $invit_1;
            $affiliate_log->order_id = $osn;
            $affiliate_log->note = 'UserID：'.$user->invit_1.'Invite new users:'.$user->name.'('.$user->email.') Register,First level commission bonus $'.$invit_1;
            $affiliate_log->affiliate_type = 'user_registration_first';
            $affiliate_log->save();
            if($user->invit_2 != null){ //二级分佣
                $invit_2=BusinessSetting::where('type', 'commission_people_level_2')->first()->value;
                User::where('id', $user->invit_2)->increment('balance', $invit_2);
                //写入日志
                $affiliate_log = new AffiliateLog;
                $affiliate_log->user_id = $user->id;
                $affiliate_log->referred_by_user = $user->invit_2;
                $affiliate_log->amount = $invit_2;
                $affiliate_log->order_id = $osn;
                $affiliate_log->note = 'UserID：'.$user->invit_1.'Invite new users:'.$user->name.'('.$user->email.') Register,Second level commission bonus $'.$invit_2;
                $affiliate_log->affiliate_type = 'user_registration_first';
                $affiliate_log->save();
                if($user->invit_3 != null){ //三级分佣
                    $invit_3=BusinessSetting::where('type', 'commission_people_level_3')->first()->value;
                    User::where('id', $user->invit_3)->increment('balance', $invit_3);
                    //写入日志
                    $affiliate_log = new AffiliateLog;
                    $affiliate_log->user_id = $user->id;
                    $affiliate_log->referred_by_user = $user->invit_3;
                    $affiliate_log->amount = $invit_3;
                    $affiliate_log->order_id = $osn;
                    $affiliate_log->note = 'UserID：'.$user->invit_1.'Invite new users:'.$user->name.'('.$user->email.') Register,Three level commission bonus $'.$invit_3;
                    $affiliate_log->affiliate_type = 'user_registration_first';
                    $affiliate_log->save();
                }
            }
        }

        if($user->email != null){
            if(BusinessSetting::where('type', 'email_verification')->first()->value != 1){
                $user->email_verified_at = date('Y-m-d H:m:s');
                $user->save();
                flash(translate('Registration successful.'))->success();
            }
            else {
                try {
                    $user->sendEmailVerificationNotification();
                    flash(translate('Registration successful. Please verify your email.'))->success();
                } catch (\Throwable $th) {
                    $user->delete();
                    flash(translate('Registration failed. Please try again later.'))->error();
                }
            }
        }

        return $this->registered($request, $user)
            ?: redirect($this->redirectPath());
    }

    protected function registered(Request $request, $user)
    {
        if ($user->email == null) {
            return redirect()->route('verification');
        }elseif(session('link') != null){
            return redirect(session('link'));
        }else {
            return redirect()->route('home');
        }
    }
}

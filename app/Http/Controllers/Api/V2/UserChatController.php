<?php

namespace App\Http\Controllers\Api\V2;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\Product;
use App\Models\User;
use App\Models\BusinessSetting;
use Auth;

class UserChatController extends Controller
{
    /**
     * 获取用户的对话列表
     */
    public function conversations(Request $request)
    {
        if (BusinessSetting::where('type', 'conversation_system')->first()->value != 1) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation system is disabled'
            ], 403);
        }

        $conversations = Conversation::where(function($query) {
                $query->where('sender_id', auth()->user()->id)
                      ->orWhere('receiver_id', auth()->user()->id);
            })
            ->with(['sender', 'receiver', 'latestMessage', 'product'])
            ->orderBy('last_message_at', 'desc')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);

        $data = $conversations->map(function($conversation) {
            $otherUser = $conversation->getOtherUser(auth()->user()->id);
            $unreadCount = $conversation->getUnreadCountForUser(auth()->user()->id);
            $lastMessage = $conversation->latestMessage;

            return [
                'id' => $conversation->id,
                'title' => $conversation->title,
                'other_user' => [
                    'id' => $otherUser->id,
                    'name' => $otherUser->name,
                    'avatar' => $otherUser->avatar_original ? uploaded_asset($otherUser->avatar_original) : null,
                    'shop_name' => $otherUser->user_type == 'seller' && $otherUser->shop ? $otherUser->shop->name : null,
                    'shop_logo' => $otherUser->user_type == 'seller' && $otherUser->shop && $otherUser->shop->logo ? uploaded_asset($otherUser->shop->logo) : null,
                ],
                'product' => $conversation->product ? [
                    'id' => $conversation->product->id,
                    'name' => $conversation->product->name,
                    'slug' => $conversation->product->slug,
                    'thumbnail' => uploaded_asset($conversation->product->thumbnail_img),
                ] : null,
                'last_message' => $lastMessage ? [
                    'message' => $lastMessage->getFormattedMessage(),
                    'created_at' => $lastMessage->created_at->toISOString(),
                    'is_own' => $lastMessage->user_id == auth()->user()->id
                ] : null,
                'unread_count' => $unreadCount,
                'updated_at' => $conversation->updated_at->toISOString()
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $conversations->currentPage(),
                'last_page' => $conversations->lastPage(),
                'per_page' => $conversations->perPage(),
                'total' => $conversations->total()
            ]
        ]);
    }

    /**
     * 获取对话详情和消息
     */
    public function conversation($id, Request $request)
    {
        $conversation = Conversation::with(['messages.user', 'sender', 'receiver', 'product'])
            ->findOrFail($id);

        // 检查权限
        if ($conversation->sender_id != auth()->user()->id && $conversation->receiver_id != auth()->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // 标记为已读
        $conversation->markAsReadForUser(auth()->user()->id);

        $otherUser = $conversation->getOtherUser(auth()->user()->id);

        $messages = $conversation->messages->map(function($message) {
            return [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'message_data' => $message->message_data,
                'attachment_url' => $message->attachment_url,
                'user' => [
                    'id' => $message->user->id,
                    'name' => $message->user->name,
                    'avatar' => $message->user->avatar_original ? uploaded_asset($message->user->avatar_original) : null,
                ],
                'is_own' => $message->user_id == auth()->user()->id,
                'created_at' => $message->created_at->toISOString()
            ];
        });

        return response()->json([
            'success' => true,
            'conversation' => [
                'id' => $conversation->id,
                'title' => $conversation->title,
                'other_user' => [
                    'id' => $otherUser->id,
                    'name' => $otherUser->name,
                    'avatar' => $otherUser->avatar_original ? uploaded_asset($otherUser->avatar_original) : null,
                    'shop_name' => $otherUser->user_type == 'seller' && $otherUser->shop ? $otherUser->shop->name : null,
                    'shop_logo' => $otherUser->user_type == 'seller' && $otherUser->shop && $otherUser->shop->logo ? uploaded_asset($otherUser->shop->logo) : null,
                ],
                'product' => $conversation->product ? [
                    'id' => $conversation->product->id,
                    'name' => $conversation->product->name,
                    'slug' => $conversation->product->slug,
                    'thumbnail' => uploaded_asset($conversation->product->thumbnail_img),
                    'price' => home_discounted_price($conversation->product),
                    'url' => route('product', $conversation->product->slug)
                ] : null,
            ],
            'messages' => $messages
        ]);
    }

    /**
     * 发送消息
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'message' => 'required|string|max:1000'
        ]);

        $conversation = Conversation::findOrFail($request->conversation_id);
        
        // 检查权限
        if ($conversation->sender_id != auth()->user()->id && $conversation->receiver_id != auth()->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => auth()->user()->id,
            'message' => $request->message,
            'message_type' => Message::TYPE_TEXT,
            'is_read' => false
        ]);

        // 更新对话
        $conversation->update([
            'last_message_at' => now(),
            'sender_viewed' => $conversation->sender_id == auth()->user()->id ? 1 : 0,
            'receiver_viewed' => $conversation->receiver_id == auth()->user()->id ? 1 : 0,
        ]);

        return response()->json([
            'success' => true,
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'user' => [
                    'id' => $message->user->id,
                    'name' => $message->user->name,
                    'avatar' => $message->user->avatar_original ? uploaded_asset($message->user->avatar_original) : null,
                ],
                'is_own' => true,
                'created_at' => $message->created_at->toISOString()
            ]
        ]);
    }

    /**
     * 分享产品
     */
    public function shareProduct(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'product_id' => 'required|exists:products,id'
        ]);

        $conversation = Conversation::findOrFail($request->conversation_id);
        $product = Product::findOrFail($request->product_id);
        
        // 检查权限
        if ($conversation->sender_id != auth()->user()->id && $conversation->receiver_id != auth()->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // 准备产品数据
        $productData = [
            'id' => $product->id,
            'name' => $product->name,
            'slug' => $product->slug,
            'thumbnail' => uploaded_asset($product->thumbnail_img),
            'price' => home_discounted_price($product),
            'original_price' => home_price($product),
            'rating' => $product->rating,
            'url' => route('product', $product->slug)
        ];

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => auth()->user()->id,
            'message' => 'Shared product: ' . $product->name,
            'message_type' => Message::TYPE_PRODUCT,
            'message_data' => $productData,
            'is_read' => false
        ]);

        // 更新对话
        $conversation->update([
            'last_message_at' => now(),
            'sender_viewed' => $conversation->sender_id == auth()->user()->id ? 1 : 0,
            'receiver_viewed' => $conversation->receiver_id == auth()->user()->id ? 1 : 0,
        ]);

        return response()->json([
            'success' => true,
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'message_data' => $message->message_data,
                'user' => [
                    'id' => $message->user->id,
                    'name' => $message->user->name,
                    'avatar' => $message->user->avatar_original ? uploaded_asset($message->user->avatar_original) : null,
                ],
                'is_own' => true,
                'created_at' => $message->created_at->toISOString()
            ]
        ]);
    }

    /**
     * 创建新对话
     */
    public function createConversation(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'message' => 'required|string|max:1000'
        ]);

        $product = Product::findOrFail($request->product_id);
        
        // 检查是否已存在对话
        $existingConversation = Conversation::where(function($query) use ($product) {
                $query->where('sender_id', auth()->user()->id)
                      ->where('receiver_id', $product->user_id);
            })
            ->orWhere(function($query) use ($product) {
                $query->where('sender_id', $product->user_id)
                      ->where('receiver_id', auth()->user()->id);
            })
            ->where('product_id', $product->id)
            ->first();

        if ($existingConversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation already exists',
                'conversation_id' => $existingConversation->id
            ], 409);
        }

        // 创建新对话
        $conversation = Conversation::create([
            'sender_id' => auth()->user()->id,
            'receiver_id' => $product->user_id,
            'title' => $product->name,
            'product_id' => $product->id,
            'sender_viewed' => 1,
            'receiver_viewed' => 0,
            'last_message_at' => now()
        ]);

        // 添加初始消息
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => auth()->user()->id,
            'message' => $request->message,
            'message_type' => Message::TYPE_TEXT,
            'is_read' => false
        ]);

        return response()->json([
            'success' => true,
            'conversation_id' => $conversation->id,
            'message' => 'Conversation created successfully'
        ]);
    }
}

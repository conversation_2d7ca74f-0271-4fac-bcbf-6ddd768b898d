<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Resources\V2\HomeCategoryCollection;
use App\Models\HomeCategory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class HomeCategoryController extends Controller
{
    public function index()
    {
        $cacheKey = 'home_categories';
        
        // 检查是否存在缓存
        if (Cache::has($cacheKey)) {
            Log::info('Using cached home categories');
            return Cache::get($cacheKey);
        }
        
        Log::info('Cache miss - fetching home categories from database');
        
        $result = Cache::remember($cacheKey, 3600, function () {
            return new HomeCategoryCollection(HomeCategory::with(['category' => function($query) {
                $query->select('id', 'name', 'slug')
                      ->with(['products' => function($query) {
                          $query->select('id', 'category_id', 'name', 'thumbnail_img', 'unit_price', 'discount')
                                ->where('added_by', 'admin')
                                ->where('published', 1)
                                ->where('approved', 1)
                                ->where('auction_product', 0)
                                ->orderBy('created_at', 'desc')
                                ->take(10);
                      }]);
            }])->get());
        });

        Log::info('Cached home categories');
        return $result;
    }
}

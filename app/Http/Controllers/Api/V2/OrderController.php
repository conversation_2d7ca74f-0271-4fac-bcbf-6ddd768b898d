<?php

namespace App\Http\Controllers\Api\V2;

use App\Models\Address;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderFixed;
use App\Models\Cart;
use App\Models\Product;
use App\Models\OrderDetail;
use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\BusinessSetting;
use App\Models\User;
use App\Models\Country;
use App\Models\State;
use App\Models\ProductStock;
use App\Models\City;
use App\Models\Addon;
use DB;
use Mail;
use App\Http\Controllers\ClubPointController;
use App\Mail\InvoiceEmailManager;
use \App\Utility\NotificationUtility;
use App\Models\CombinedOrder;
use App\Http\Controllers\AffiliateController;

class OrderController extends Controller
{
    public function store(Request $request, $set_paid = false)
    {
        $cartItems = Cart::where('user_id', auth()->user()->id)->get();

        if ($cartItems->isEmpty()) {
            return response()->json([
                'combined_order_id' => 0,
                'result' => false,
                'message' => translate('Cart is Empty')
            ]);
        }

        $user = User::find(auth()->user()->id);


        $address = Address::where('id', $cartItems->first()->address_id)->first();
        $shippingAddress = [];
        if ($address != null) {
            $shippingAddress['name']        = $user->name;
            $shippingAddress['email']       = $user->email;
            $shippingAddress['address']     = $address->address;
            $shippingAddress['country']     = $address->country->name;
            $shippingAddress['state']       = $address->state->name;
            $shippingAddress['city']        = $address->city ? $address->city->name : '';
            $shippingAddress['postal_code'] = $address->postal_code;
            $shippingAddress['phone']       = $address->phone;
            if ($address->latitude || $address->longitude) {
                $shippingAddress['lat_lang'] = $address->latitude . ',' . $address->longitude;
            }
        }

        $combined_order = new CombinedOrder;
        $combined_order->user_id = $user->id;
        $combined_order->shipping_address = json_encode($shippingAddress);
        $combined_order->save();

        $seller_products = array();
        foreach ($cartItems as $cartItem) {
            $product_ids = array();
            $product = Product::find($cartItem['product_id']);
            if (isset($seller_products[$product->user_id])) {
                $product_ids = $seller_products[$product->user_id];
            }
            array_push($product_ids, $cartItem);
            $seller_products[$product->user_id] = $product_ids;
        }

        foreach ($seller_products as $seller_product) {
            $order = new Order;
            $order->combined_order_id = $combined_order->id;
            $order->user_id = $user->id;
            $order->shipping_address = json_encode($shippingAddress);
            $order->shipping_type = $cartItems->first()->shipping_type;
            if ($cartItems->first()->shipping_type == 'pickup_point') {
                $order->pickup_point_id = $cartItems->first()->pickup_point;
            }
            $order->payment_type = $request->payment_type;
            $order->delivery_viewed = '0';
            $order->payment_status_viewed = '0';
            $order->code = date('Ymd-His') . rand(10, 99);
            $order->date = strtotime('now');
            if($set_paid){
                $order->payment_status = 'paid';
            }else{
                $order->payment_status = 'unpaid';
            }

            $order->save();

            $subtotal = 0;
            $tax = 0;
            $shipping = 0;
            $coupon_discount = 0;

            //Order Details Storing
            foreach ($seller_product as $cartItem) {
                $product = Product::find($cartItem['product_id']);

                $subtotal += cart_product_price($cartItem, $product, false, false) * $cartItem['quantity'];
                $tax += cart_product_tax($cartItem, $product,false) * $cartItem['quantity'];
                $coupon_discount += $cartItem['discount'];

                $product_variation = $cartItem['variation'];

                $product_stock = $product->stocks->where('variant', $product_variation)->first();
                if ($product->digital != 1 && $cartItem['quantity'] > $product_stock->qty) {
                    $order->delete();
                    $combined_order->delete();
                    return response()->json([
                        'combined_order_id' => 0,
                        'result' => false,
                        'message' => translate('The requested quantity is not available for ') . $product->name
                    ]);
                } elseif ($product->digital != 1) {
                    $product_stock->qty -= $cartItem['quantity'];
                    $product_stock->save();
                }

                $order_detail = new OrderDetail;
                $order_detail->order_id = $order->id;
                $order_detail->seller_id = $product->user_id;
                $order_detail->product_id = $product->id;
                $order_detail->variation = $product_variation;
                $order_detail->price = cart_product_price($cartItem, $product, false, false) * $cartItem['quantity'];
                $order_detail->tax = cart_product_tax($cartItem, $product,false) * $cartItem['quantity'];
                $order_detail->shipping_type = $cartItem['shipping_type'];
                $order_detail->product_referral_code = $cartItem['product_referral_code'];
                $order_detail->shipping_cost = $cartItem['shipping_cost'];

                $shipping += $order_detail->shipping_cost;

                if ($cartItem['shipping_type'] == 'pickup_point') {
                    $order_detail->pickup_point_id = $cartItem['pickup_point'];
                }
                //End of storing shipping cost

                $order_detail->quantity = $cartItem['quantity'];
                $order_detail->save();

                $product->num_of_sale = $product->num_of_sale + $cartItem['quantity'];
                $product->save();

                $order->seller_id = $product->user_id;

                if (addon_is_activated('affiliate_system')) {
                    if ($order_detail->product_referral_code) {
                        $referred_by_user = User::where('referral_code', $order_detail->product_referral_code)->first();

                        $affiliateController = new AffiliateController;
                        $affiliateController->processAffiliateStats($referred_by_user->id, 0, $order_detail->quantity, 0, 0);
                    }
                }
            }

            $order->grand_total = $subtotal + $tax + $shipping;

            if ($seller_product[0]->coupon_code != null) {
                // if (Session::has('club_point')) {
                //     $order->club_point = Session::get('club_point');
                // }
                $order->coupon_discount = $coupon_discount;
                $order->grand_total -= $coupon_discount;

                $coupon_usage = new CouponUsage;
                $coupon_usage->user_id = $user->id;
                $coupon_usage->coupon_id = Coupon::where('code', $seller_product[0]->coupon_code)->first()->id;
                $coupon_usage->save();
            }

            $combined_order->grand_total += $order->grand_total;

            if (strpos($request->payment_type, "manual_payment_") !== false) { // if payment type like  manual_payment_1 or  manual_payment_25 etc)

                $order->manual_payment = 1;
                $order->save();

            }

            $order->save();
        }
        $combined_order->save();



        Cart::where('user_id', auth()->user()->id)->delete();

        if (
            $request->payment_type == 'cash_on_delivery'
            || $request->payment_type == 'wallet'
            || strpos($request->payment_type, "manual_payment_") !== false // if payment type like  manual_payment_1 or  manual_payment_25 etc
        ) {
            NotificationUtility::sendOrderPlacedNotification($order);
        }


        return response()->json([
            'combined_order_id' => $combined_order->id,
            'result' => true,
            'message' => translate('Your order has been placed successfully')
        ]);
    }
    public function fixed_list(Request $request)
    {
        // DB::enableQueryLog();
        $model = OrderFixed::orderBy('execution_time', 'desc');
        $model->where('execution_time', '<=',time())->where('status',0);
        $data = $model->get()->toArray();
        // $sql = DB::getQueryLog()[0]['query'];
        // echo $sql;die;
        foreach($data as $key=>$val){
            $session_info = json_decode($val['session_info'],true);
            $request_info = json_decode($val['request_info'],true);
            $this->fixed_res($val['id'],$request_info,$session_info);
            echo $val['id'].'执行完';
        }
    }
    public function fixed_res($id,$request_info,$session_info){
        $model = OrderFixed::find($id);
        if($session_info['pos']['shipping_info'] == null || $session_info['pos']['shipping_info']['name'] == null || $session_info['pos']['shipping_info']['phone'] == null || $session_info['pos']['shipping_info']['address'] == null){
            $model->status = 2;
            $model->remark = translate("Please Add Shipping Information.");
            $model->save();
        }else{
            if(isset($session_info['pos']['cart']) && count($session_info['pos']['cart']) > 0){
                $order = new Order;
                $shipping_info = $session_info['pos']['shipping_info'];
                if ($request_info['user_id'] == null) {
                    $order->guest_id    = mt_rand(100000, 999999);
                }
                else {
                    $order->user_id = $request_info['user_id'];
                }
                $data['name']           = $shipping_info['name'];
                $data['email']          = $shipping_info['email'];
                $data['address']        = $shipping_info['address'];
                $data['country']        = $shipping_info['country'];
                $data['city']           = $shipping_info['city'];
                $data['postal_code']    = $shipping_info['postal_code'];
                $data['phone']          = $shipping_info['phone'];
                $order->shipping_address = json_encode($data);

                $order->payment_type = $request_info['payment_type'];
                $order->delivery_viewed = '0';
                $order->payment_status_viewed = '0';
                $order->code = date('Ymd-His').rand(10,99);
                $order->date = strtotime('now');
                $order->order_type = 1;
                $order->payment_status = $request_info['payment_type'] != 'cash_on_delivery' ? 'paid' : 'unpaid';
                $order->payment_details = $request_info['payment_type'];

                if($request_info['payment_type'] == 'offline_payment'){
                    if($request_info['offline_trx_id'] == null){
                        return array('success' => 0, 'message' => translate("Transaction ID can not be null."));
                    }
                    $data['name']   = $request_info['offline_payment_method'];
                    $data['amount'] = $request_info['offline_payment_amount'];
                    $data['trx_id'] = $request_info['offline_trx_id'];
                    $data['photo']  = $request_info['offline_payment_proof'];
                    $order->manual_payment_data = json_encode($data);
                    $order->manual_payment = 1;
                }


                $shipping_info = $session_info['pos']['shipping_info'];
                $order->picking_switch = get_setting('picking_switch');
                if($order->save()){
                    $subtotal = 0;
                    $tax = 0;
                    $productStorehouseTotal = 0;
                    foreach ($session_info['pos']['cart'] as $key => $cartItem){
                        $product_stock = ProductStock::find($cartItem['stock_id']);
                        if ($product_stock){
                            $product = $product_stock->product;
                            $product_variation = $product_stock->variant;

                            $subtotal += $cartItem['price']*$cartItem['quantity'];
                            $tax += $cartItem['tax']*$cartItem['quantity'];

                            // 计算产品仓库的产品货款
                            $originalProduct = null;
                            if ($product->original_id) {
                                $originalProduct = Product::query()->find($product->original_id);
                                if ($originalProduct) {
                                    $productStorehouseTotal += cart_product_price($cartItem, $originalProduct, false, false) * $cartItem['quantity'];
                                }
                            }

                            if($cartItem['quantity'] > $product_stock->qty){
                                $order->delete();
                                return array('success' => 0, 'message' => $product->name.' ('.$product_variation.') '.translate(" just stock outs."));
                            }
                            else {
                                $product_stock->qty -= $cartItem['quantity'];
                                $product_stock->save();
                            }

                            $order_detail = new OrderDetail;
                            $order_detail->order_id  =$order->id;
                            $order_detail->seller_id = $product->user_id;
                            $order_detail->product_id = $product->id;
                            $order_detail->payment_status = $request_info['payment_type'] != 'cash_on_delivery' ? 'paid' : 'unpaid';
                            $order_detail->variation = $product_variation;
                            $order_detail->price = $cartItem['price'] * $cartItem['quantity'];
                            $order_detail->tax = $cartItem['tax'] * $cartItem['quantity'];
                            $order_detail->quantity = $cartItem['quantity'];
                            $order_detail->shipping_type = null;
                            $shipping_res = isset($session_info['pos']['shipping'])?:0;
                            if ($shipping_res >= 0){
                                $order_detail->shipping_cost = $shipping_res/count($session_info['pos']['cart']);
                            }
                            else {
                                $order_detail->shipping_cost = 0;
                            }

                            $order_detail->save();

                            $product->num_of_sale++;
                            $product->save();
                        }
                    }
                    $shipping_res = isset($session_info['pos']['shipping'])?:0;
                    $order->grand_total = $subtotal + $tax + $shipping_res;

                    if(isset($session_info['pos']['discount'])){
                        $order->grand_total -= $session_info['pos']['discount'];
                        $order->coupon_discount = $session_info['pos']['discount'];
                    }

                    $order->seller_id = $product->user_id;
                    $order->product_storehouse_total = $productStorehouseTotal;
                    $order->save();

                    $model->order_id = $order->id;
                    if ( get_setting('picking_switch') != 1 )
                    {//如果不需要提货，直接修改订单为已提货状态
                        $shop = $order->shop;
                        $shop->admin_to_pay += ( $order->grand_total - $order->product_storehouse_total );
                        $shop->save();
                        // 保存订单冻结资金过期时间
                        $freezeDays = get_setting('frozen_funds_unfrozen_days', 15);
                        $order->freeze_expired_at = Carbon::now()->addDays($freezeDays)->timestamp;
                        $order->product_storehouse_status = 1;
                        $order->save();
                    }

                    $array['view'] = 'emails.invoice';
                    $array['subject'] = 'Your order has been placed - '.$order->code;
                    $array['from'] = env('MAIL_USERNAME');
                    $array['order'] = $order;

                    $admin_products = array();
                    $seller_products = array();

                    foreach ($order->orderDetails as $key => $orderDetail){
                        if($orderDetail->product->added_by == 'admin'){
                            array_push($admin_products, $orderDetail->product->id);
                        }
                        else{
                            $product_ids = array();
                            if(array_key_exists($orderDetail->product->user_id, $seller_products)){
                                $product_ids = $seller_products[$orderDetail->product->user_id];
                            }
                            array_push($product_ids, $orderDetail->product->id);
                            $seller_products[$orderDetail->product->user_id] = $product_ids;
                        }
                    }

                    foreach($seller_products as $key => $seller_product){
                        try {
                            Mail::to(User::find($key)->email)->queue(new InvoiceEmailManager($array));
                        } catch (\Exception $e) {

                        }
                    }
                    if(env('MAIL_USERNAME') != null){
                        try {
                            Mail::to($session_info['pos']['shipping_info']['email'])->queue(new InvoiceEmailManager($array));
                            Mail::to(User::where('user_type', 'admin')->first()->email)->queue(new InvoiceEmailManager($array));
                        } catch (\Exception $e) {

                        }
                    }

                    if($request_info['user_id'] != NULL){
                        if (Addon::where('unique_identifier', 'club_point')->first() != null && Addon::where('unique_identifier', 'club_point')->first()->activated) {
                            $clubpointController = new ClubPointController;
                            $clubpointController->processClubPoints($order);
                        }
                    }

                    calculateCommissionAffilationClubPoint($order);

                    $model->status = 1;
                    $model->remark = translate('Order Completed Successfully.');
                    $model->save();
                }
                else {
                    $model->status = 2;
                    $model->remark = translate('Please input customer information.');
                    $model->save();
                }
            }else{
                $model->status = 2;
                $model->remark = translate("Please select a product.");
                $model->save();
            }
        }
    }

}

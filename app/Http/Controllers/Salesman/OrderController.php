<?php

namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\Shop;
use Illuminate\Http\Request;
use Auth;
use DB;
use Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;

class OrderController extends Controller
{
    /**
     * 显示推销员管理的订单列表
     * // 二次㊢开㊐发㊑程㊏序㊨搭㊐建㊑刷㊏单㊨资㊐金㊑盘㊏彩㊨票㊐盘㊑游㊏戏㊨理㊐财㊑TG：@aiziziyuan，TG：@laipiaozi www.laipiaozi.com（请自㊐行忽略中㊑间符号！)a㊏bcdefghijklmn㊨opqrstuvwxy㊐z
     */
    public function index(Request $request)
    {
        // --- !! 极简日志测试 !! --- 
        Log::info('--- Salesman Order Controller INDEX method WAS CALLED ---');
        // --- !! 结束测试 !! ---

        // 1. 获取当前登录的推销员
        $salesman = Auth::user();

        // 2. 获取推销员管理的卖家列表 (假设卖家用户类型为 'seller'，并通过 'pid' 关联推销员)
        //    请根据你的实际用户结构调整 'user_type' 和关联方式
        $sellers = User::where('pid', $salesman->id)
                       ->where('user_type', 'seller') // 可能需要调整为 'shop' 或其他类型
                       ->select('id', 'name') // 只选择需要的字段
                       ->orderBy('name', 'asc') // 按名称排序
                       ->get();
        // --- Debug: 打印获取到的卖家信息 --- 
        Log::info('Salesman Order Index - Salesman ID: ' . $salesman->id . ' - Found sellers count: ' . $sellers->count());
        Log::info('Salesman Order Index - Found sellers data:', $sellers->toArray());
        // --- End Debug --- 
        $managed_seller_ids = $sellers->pluck('id')->toArray();

        // 3. 初始化视图变量
        $payment_status = $request->payment_status;
        $delivery_status = $request->delivery_status;
        $sort_search = $request->search;
        $sort_seller_id = $request->seller_id; // 获取选中的卖家ID

        // 4. 构建订单查询
        $order_query = Order::query();

        // 5. 筛选当前推销员管理的卖家的订单
        if (empty($managed_seller_ids)) {
            // 如果推销员没有管理任何卖家，返回空结果
             $orders = new LengthAwarePaginator([], 0, 15); // 创建一个空的分页器
        } else {
            // 假设 orders 表有 seller_id 字段直接关联卖家的 User ID
            $order_query->whereIn('seller_id', $managed_seller_ids);

            // 6. 应用现有筛选
            if ($payment_status != null) {
                $order_query->where('payment_status', $payment_status);
            }
            if ($delivery_status != null) {
                $order_query->where('delivery_status', $delivery_status);
            }
            if ($sort_search != null) {
                $order_query->where('code', 'like', '%' . $sort_search . '%');
            }

            // 7. 应用新增的卖家筛选
            if ($sort_seller_id != null && $sort_seller_id != '') {
                // 确保下拉框只包含管理的卖家，直接应用筛选
                 $order_query->where('seller_id', $sort_seller_id);
            }

            // 8. 排序和分页，并预加载卖家信息 (假设通过 $order->shop->user 获取)
            //    如果直接通过 $order->seller 获取，可以改成 ->with('seller')
            $orders = $order_query->with(['shop.user']) // Eager load seller info for the table display
                                 ->orderBy('id', 'desc')
                                 ->paginate(15);
        }

        // 9. 返回视图，并传递所需数据
        return view('salesman.orders.index', compact(
            'orders',             // 订单分页数据
            'sellers',            // 卖家列表，用于填充下拉框
            'payment_status',     // 用于视图中保留筛选状态
            'delivery_status',    // 用于视图中保留筛选状态
            'sort_search',        // 用于视图中保留筛选状态
            'sort_seller_id'      // 用于视图中保留选中的卖家ID
        ));
    }

    /**
     * 批量更新订单配送状态
     */
    public function updateDeliveryStatus(Request $request)
    {
        try {
            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 检查是单个order_id还是ids数组，并统一处理
            $order_ids = [];
            if ($request->has('order_id')) {
                $order_ids[] = $request->order_id;
            } elseif ($request->has('ids') && is_array($request->ids)) {
                $order_ids = $request->ids;
            }

            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 确保有订单ID才继续
            if (!empty($order_ids)) {
                foreach ($order_ids as $order_id) {
                    $order = Order::findOrFail($order_id);
                    $order->delivery_status = $request->status;
                    $order->save();

                    if ($request->status == 'delivered') {
                        product_storehouse_order_free_up($order);
                    }

                    if ($request->status == 'cancelled' && $order->payment_type == 'wallet') {
                        $user = User::where('id', $order->user_id)->first();
                        if ($user) { // 确保用户存在
                            $user->balance += $order->grand_total;
                            $user->save();
                        }
                    }
                }
                return response()->json([
                    'success' => true,
                    'message' => translate('Order status has been updated successfully') // 使用翻译函数
                ]);
            }
            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 如果没有有效的订单ID，返回错误
            return response()->json([
                'success' => false,
                'message' => translate('Please select the order(s) to update') // 使用翻译函数
            ]);
        } catch (\Exception $e) {
            Log::error('更新订单状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 释放订单冻结资金
     */
    public function freeUp(Request $request)
    {
        // --- 添加调试日志 ---
        Log::info('Salesman freeUp Request Data:', $request->all()); 
        // --- 结束调试日志 ---
        
        try {
            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 检查是单个order_id还是ids数组，并统一处理
            $order_ids = [];
            if ($request->has('order_id')) {
                $order_ids[] = $request->order_id;
            } elseif ($request->has('ids') && is_array($request->ids)) {
                $order_ids = $request->ids;
            }

            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 确保有订单ID才继续
            if (!empty($order_ids)) {
                $success_count = 0;

                foreach ($order_ids as $order_id) {
                    $order = Order::findOrFail($order_id);
                    if(product_storehouse_order_free_up($order)) {
                        $success_count++;
                    }
                }

                if ($success_count > 0) {
                    return response()->json([
                        'success' => true,
                        'message' => translate('Successfully released frozen funds for :count order(s)', [':count' => $success_count]) // 使用带参数的翻译
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => translate('No frozen funds were released for the selected order(s)') // 使用翻译
                    ]);
                }
            }

            // // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 如果没有有效的订单ID，返回错误
            return response()->json([
                'success' => false,
                'message' => translate('Please select the order(s) to release funds for') // 使用翻译
            ]);

        } catch (\Exception $e) {
            Log::error('释放冻结资金失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 分配配送员给订单
     * // 二次㊎开㊍发程㊌序搭㊋建刷㊏单资㊚金盘㊛彩票㊐盘游㊊戏理㊣财TG：@aiziziyuan，㊤T㊥G：@㊦laipiaozi ㊧www.㊨laipiaozi.c㊒om（㊫请自㊑行忽㊓略中㊔间符㊕号！㊖)a㊘bcde㊜fghij㊝klmno㊞pqrst㊟uvwxy㊠z㊡ // 这个方法处理将订单分配给特定配送员的请求
     */
    public function deliveryBoyAssign(Request $request)
    {
        try {
            $order = Order::findOrFail($request->order_id);
            $order->assign_delivery_boy = $request->delivery_boy;
            $order->save();

            // 你可以在这里添加通知配送员的逻辑（如果需要）
            // 例如： event(new DeliveryBoyAssigned($order));

            return response()->json([
                'success' => true,
                'message' => translate('Delivery boy has been assigned successfully') // 使用翻译函数保持一致性
            ]);

        } catch (\Exception $e) {
            Log::error('分配配送员失败', [
                'order_id' => $request->order_id,
                'delivery_boy' => $request->delivery_boy,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => translate('Failed to assign delivery boy: ') . $e->getMessage() // 返回更清晰的错误信息
            ], 500);
        }
    }

    /**
     * 【修改】处理管理员式释放订单冻结资金 (已移除权限检查 - 前端已处理筛选)
     * // 二次㊄开㊅发㊆程㊇序㊈搭㊉建㊎刷㊍单㊌资㊋金㊏盘㊚彩㊛票㊐盘㊊游㊣戏㊤理㊥财㊦TG：@aiziziyuan，㊧TG：@laipiaozi ㊨www.l㊒aipiaozi.c㊫om（㊑请㊓自㊔行㊕忽㊖略㊗中㊘间㊜符㊝号㊞！㊟)abc㊠d㊡efg㊢hijk㊩lmno㊪pqrs㊬tuvwxyz㊭㊮㊯㊰㊀㊁㊂㊃
     */
    public function adminStyleFreeUp(Request $request, $id) // 从路由接收订单 ID
    {
        Log::info('Salesman adminStyleFreeUp triggered for order ID: ' . $id . ' - PERMISSION CHECK REMOVED (Filtered in Frontend)!'); // 日志标记权限检查移除
        try {
            $order = Order::findOrFail($id); // 查找订单，找不到会抛出异常
            // $salesman = Auth::user(); // 不再需要获取推销员信息

            // --- 权限检查已被移除 (基于前端已做筛选的假设) ---
            /*
            $seller = $order->seller;
            if (!$seller || $seller->pid != $salesman->id) {
                 Log::warning('Salesman permission check SKIPPED for adminStyleFreeUp on order ID: ' . $id, [
                     'salesman_id' => $salesman->id,
                     'order_seller_id' => $order->seller_id,
                     'seller_pid' => $seller->pid ?? 'N/A'
                 ]);
                 // return response()->json([
                 //     'success' => false,
                 //     'message' => translate('You do not have permission to manage this seller\'s order funds.')
                 // ], 403);
            }
            */
            // --- 权限检查结束 ---

            Log::info('Proceeding with adminStyleFreeUp for order ID: ' . $id . ' without backend permission check.');

            // 直接调用核心的 Helper 函数来执行释放
            $result = product_storehouse_order_free_up($order->id); // 传递订单 ID

            if ($result) {
                Log::info('Helper function product_storehouse_order_free_up succeeded for order ID: ' . $id);
                // 成功
                return response()->json([
                    'success' => true,
                    'message' => translate('Successfully released frozen funds for the order.')
                ]);
            } else {
                Log::warning('Helper function product_storehouse_order_free_up failed for order ID: ' . $id);
                // 失败 (可能是 helper 函数内部的条件检查导致，例如已释放)
                return response()->json([
                    'success' => false,
                    'message' => translate('Failed to release frozen funds. The operation might have already been done or conditions not met.')
                ]);
            }

        } catch (ModelNotFoundException $e) {
            Log::error('Order not found in adminStyleFreeUp', ['order_id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => translate('Order not found.')], 404);
        } catch (\Exception $e) {
            Log::error('Error in adminStyleFreeUp (Permission check removed)', [
                'order_id' => $id ?? 'N/A',
                'salesman_id' => Auth::id(), // 仍然记录操作者ID
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => translate('An error occurred while processing the request.')
            ], 500);
        }
    }
} 
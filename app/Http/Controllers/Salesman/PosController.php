<?php

namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Auth;

class PosController extends Controller
{
    /**
     * 显示销售员定时下单页面
     * 刷单彩票盘系统TG：@laipiaozi二次开发
     */
    public function time()
    {
        $customers = User::where('user_type', 'customer')
            ->where('email_verified_at', '!=', null)
            ->orderBy('created_at', 'desc')
            ->get();
        
        // 调试信息    
        \Log::info('POS Time function called');
        \Log::info('Customer count: ' . $customers->count());
        
        return view('pos.salesman.pos_time', compact('customers'));
    }
} 
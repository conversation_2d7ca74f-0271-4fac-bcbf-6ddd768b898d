<?php

namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use App\Models\Shop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class SellerController extends Controller
{
    /**
     * 显示编辑卖家表单
     */
    public function edit($id)
    {
        $shop = Shop::findOrFail(decrypt($id));
        return view('salesman.sellers.edit', compact('shop'));
    }

    /**
     * 更新卖家资料
     */
    public function update(Request $request, $id)
    {
        $shop = Shop::findOrFail($id);
        $user = $shop->user;
        
        $user->name = $request->name;
        $user->email = $request->email;
        $shop->views = (int)$request->views;
        
        if (strlen($request->password) > 0) {
            $user->password = Hash::make($request->password);
        }
        
        if (strlen($request->tpwd) > 0) {
            $user->tpwd = md5($request->tpwd);
        }
        
        if ($user->save() && $shop->save()) {
            flash(translate('Seller has been updated successfully'))->success();
            return redirect()->route('salesman.sellers');
        }
        
        flash(translate('Something went wrong'))->error();
        return back();
    }
} 
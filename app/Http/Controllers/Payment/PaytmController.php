<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\SellerPackageController;
use App\Http\Controllers\SellerSpreadPackageController;
use App\Http\Controllers\WalletController;
use App\Models\SellerSpreadPackage;
use Illuminate\Http\Request;
use App\Models\CombinedOrder;
use App\Models\Recharge;
use App\Models\User;
use App\Models\Wallet;
use App\Models\CustomerPackage;
use App\Models\SellerPackage;
use PayPalCheckoutSdk\Core\PayPalHttpClient;
use PayPalCheckoutSdk\Core\SandboxEnvironment;
use PayPalCheckoutSdk\Core\ProductionEnvironment;
use PayPalCheckoutSdk\Orders\OrdersCreateRequest;
use PayPalCheckoutSdk\Orders\OrdersCaptureRequest;
use Session;
use Cache;
use Redirect;

class PaytmController extends Controller
{
    public function callback(Request $request){
        die;
        //dump(Cache::get('233223432'));die;
        $data=$request->toArray();
        Cache::put('233223432', $data);
        //验证签名
        $key='63110f813b2619cf47f83c40b36e7314';
        $sign=md5($data['merchant_no'].$data['params'].$data['sign_type'].$data['timestamp'].$key);
        if($sign==$data['sign']){ //验签成功
            $li=json_decode($data['params'],true);
            if($li['status']==1 && $li['success_time']>0){ //支付状态为1，且支付时间存在
                $review=Recharge::where('merchant_ref', $li['merchant_ref'])->get()[0];//查询订单
                if($review->status!=1){
                    //修改订单状态
                    $review->fee = $li['fee'];
                    $review->daozhang_money = $li['pay_amount'];
                    $review->system_ref = $li['system_ref'];
                    $review->paytime = $li['paytime'];
                    $review->updatetime = time();
                    $review->status = 1;
                    $review->save();
                    $this->rujinmoney($review['userid'],$li['pay_amount'],$li['merchant_ref']);//给用户入金
                }
            }
        }
        return 'SUCCESS';
    }
    public function rujinmoney($userid,$number,$orderid){
        $user=User::find($userid);
        $user->balance=$user->balance+$number;
        $user->save();
        //记录钱包信息
        $wallet = new Wallet;
        $wallet->user_id = $userid;
        $wallet->amount = $number;
        $wallet->payment_method = '信用卡支付';
        $wallet->type = 1;
        $wallet->save();
    }
}

<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\Message;
use App\Models\Conversation;

class NewChatMessage extends Mailable
{
    use Queueable, SerializesModels;

    public $message;
    public $conversation;
    public $sender;
    public $receiver;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Message $message)
    {
        $this->message = $message;
        $this->conversation = $message->conversation;
        $this->sender = $message->user;
        $this->receiver = $this->conversation->getOtherUser($message->user_id);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $senderName = $this->sender->user_type == 'seller' && $this->sender->shop ? 
            $this->sender->shop->name : $this->sender->name;

        return $this->subject('New message from ' . $senderName)
                    ->view('emails.new_chat_message')
                    ->with([
                        'message' => $this->message,
                        'conversation' => $this->conversation,
                        'sender' => $this->sender,
                        'receiver' => $this->receiver,
                        'sender_name' => $senderName,
                        'chat_url' => route('user.chat.show', encrypt($this->conversation->id))
                    ]);
    }
}

<?php

namespace App\Libs\Captcha;

/**
 * 验证码生成
 */
class Captcha
{
    private $phrase = null;
    private $builder = null;
    private $code = null;

    /**
     * 验证码有效时长
     * 默认为3分钟（180秒），如果为0，则不限制时间
     */
    private int $valid_time = 180;

    /**
     * 验证码长度
     */
    private int $code_len = 4;

    public function __construct($code_len = 4, $valid_time = 180)
    {
        // $this->phrase = Factories::thirdParty("Captcha/PhraseBuilder");
        // $this->builder = Factories::thirdParty("Captcha/CaptchaBuilder");

        $this->phrase = new \App\Libs\Captcha\PhraseBuilder();
        $this->builder = new \App\Libs\Captcha\CaptchaBuilder();

        $this->valid_time = $valid_time;
        $this->code_len = $code_len;

        $this->code = $this->phrase->build($this->code_len);
        $this->builder->setPhrase($this->code);
        $this->builder->setBackgroundColor(255, 255, 255);
        $this->builder->setMaxAngle(10);
        $this->builder->setMaxBehindLines(1);
        $this->builder->setMaxFrontLines(0);
        $this->builder->build($width = 120, $height = 40, $font = null);
    }

    /**
     * 获取验证码
     * 大小写不敏感
     */
    public function getCode()
    {
        return strtolower($this->code);
    }

    /**
     * 获取验证码有效时长
     */
    public function getValidTime()
    {
        return $this->valid_time;
    }

    /**
     * 输出验证码图片
     * 需要在控制器中设置输出header
     * $this->response->setHeader('Content-Type', 'image/png');
     *
     */
    public function outCaptcha()
    {
        $this->builder->setPhrase($this->code);
        $this->builder->setBackgroundColor(255, 255, 255);
        $this->builder->setMaxAngle(25);
        $this->builder->build($width = 120, $height = 40, $font = null);
        return $this->builder->output();
    }

    /**
     * 页面中输出验证码
     * img src=
     */
    public function getCaptcha()
    {
        return $this->builder->inline();
    }

    /**
     * 存储验证码为图片文件
     * 文件名
     * 扩展名默认为 jpg
     */
    public function saveCaptcha($file_name)
    {
        $this->builder->setPhrase($this->code);
        $this->builder->setBackgroundColor(255, 255, 255);
        $this->builder->setMaxAngle(25);
        $this->builder->build($width = 120, $height = 40, $font = null);
        return $this->builder->save($file_name . '.jpg');
    }
}

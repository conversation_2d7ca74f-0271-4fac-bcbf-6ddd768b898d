<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    protected $fillable = [
        'conversation_id', 'user_id', 'message', 'message_type',
        'message_data', 'attachment_url', 'is_read'
    ];

    protected $casts = [
        'message_data' => 'array',
        'is_read' => 'boolean'
    ];

    // 消息类型常量
    const TYPE_TEXT = 'text';
    const TYPE_PRODUCT = 'product';
    const TYPE_IMAGE = 'image';
    const TYPE_FILE = 'file';

    public function conversation(){
        return $this->belongsTo(Conversation::class);
    }

    public function user(){
        return $this->belongsTo(User::class);
    }

    // 获取产品信息（如果是产品消息）
    public function getProductData()
    {
        if ($this->message_type === self::TYPE_PRODUCT && $this->message_data) {
            return $this->message_data;
        }
        return null;
    }

    // 检查是否是产品消息
    public function isProductMessage()
    {
        return $this->message_type === self::TYPE_PRODUCT;
    }

    // 检查是否是图片消息
    public function isImageMessage()
    {
        return $this->message_type === self::TYPE_IMAGE;
    }

    // 检查是否是文件消息
    public function isFileMessage()
    {
        return $this->message_type === self::TYPE_FILE;
    }

    // 格式化消息内容用于显示
    public function getFormattedMessage()
    {
        switch ($this->message_type) {
            case self::TYPE_PRODUCT:
                return '分享了一个产品';
            case self::TYPE_IMAGE:
                return '发送了一张图片';
            case self::TYPE_FILE:
                return '发送了一个文件';
            default:
                return $this->message;
        }
    }
}

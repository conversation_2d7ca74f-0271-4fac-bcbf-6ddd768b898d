<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Conversation extends Model
{
    protected $fillable = [
        'sender_id', 'receiver_id', 'title', 'product_id',
        'sender_viewed', 'receiver_viewed', 'is_tip', 'last_message_at'
    ];

    protected $dates = ['last_message_at'];

    public function messages(){
        return $this->hasMany(Message::class)->orderBy('created_at', 'asc');
    }

    public function latestMessage(){
        return $this->hasOne(Message::class)->latest();
    }

    public function sender(){
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver(){
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function product(){
        return $this->belongsTo(Product::class, 'product_id');
    }

    // 获取对话中的未读消息数量
    public function getUnreadCountForUser($user_id)
    {
        return $this->messages()
            ->where('user_id', '!=', $user_id)
            ->where('is_read', false)
            ->count();
    }

    // 标记对话为已读
    public function markAsReadForUser($user_id)
    {
        if ($this->sender_id == $user_id) {
            $this->update(['sender_viewed' => 1]);
        } elseif ($this->receiver_id == $user_id) {
            $this->update(['receiver_viewed' => 1]);
        }

        // 标记消息为已读
        $this->messages()
            ->where('user_id', '!=', $user_id)
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    // 获取对话的另一方用户
    public function getOtherUser($current_user_id)
    {
        if ($this->sender_id == $current_user_id) {
            return $this->receiver;
        } else {
            return $this->sender;
        }
    }
}

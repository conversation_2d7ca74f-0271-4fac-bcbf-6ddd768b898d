<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShopCreditHistory extends Model
{
    protected $table = 'shop_credit_history';
    
    // 禁用时间戳
    public $timestamps = false;
    
    protected $fillable = [
        'shop_id',
        'type',
        'score_before',
        'score_after',
        'change_value',
        'operator_type',
        'operator_id',
        'reason',
        'remark',
        'related_id',
        'created_at'
    ];

    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id');
    }
} 
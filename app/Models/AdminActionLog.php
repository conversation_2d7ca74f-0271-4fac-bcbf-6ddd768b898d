<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdminActionLog extends Model
{
    protected $table = 'admin_actions_log';
    
    protected $fillable = [
        'user_id', 'user_type', 'action_type', 'target_type', 
        'target_id', 'action_details', 'ip_address'
    ];
    
    protected $casts = [
        'action_details' => 'array',
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
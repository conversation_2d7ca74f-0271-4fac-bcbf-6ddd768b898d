<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AffiliateLog extends Model
{
    //  protected $fillable = [
    //     'user_id','referred_by_user','amount','note','order_id'
    // ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order_detail()
    {
        return $this->belongsTo(OrderDetail::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}

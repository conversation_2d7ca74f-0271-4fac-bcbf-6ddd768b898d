# 聊天插件集成问题检查清单

## 1. 数据库连接检查
```bash
# 在服务器上检查MySQL连接
mysql -u newchat -p -h localhost newchat -e "SHOW TABLES;"
```

## 2. 配置文件检查
确认 `chat/config.php` 中的数据库配置正确：
- SB_DB_HOST
- SB_DB_NAME  
- SB_DB_USER
- SB_DB_PASSWORD

## 3. 表结构检查
确认聊天数据库中存在以下表：
- sb_users
- sb_conversations  
- sb_messages

## 4. 权限检查
确认数据库用户有以下权限：
- SELECT
- INSERT
- UPDATE
- CREATE (如果需要创建表)

## 5. 服务器日志检查
检查以下日志文件：
- Laravel日志：`storage/logs/laravel.log`
- Web服务器错误日志
- MySQL错误日志

## 6. PHP配置检查
确认PHP配置：
- PDO MySQL扩展已启用
- 内存限制足够
- 执行时间限制合理

## 7. 网络连接检查
如果数据库在远程服务器：
- 检查防火墙设置
- 确认数据库服务器允许远程连接
- 测试网络延迟

## 测试URL
- 基本访问测试：`/seller/test-access`
- 数据库连接测试：`/seller/chat-debug`
- 聊天功能测试：`/seller/support-chat`

<?php

use App\Http\Controllers\AddressController;
use App\Http\Controllers\AizUploadController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CompareController;
use App\Http\Controllers\ConversationController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\CustomerProductController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\DigitalProductController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PurchaseHistoryController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\SellerController;
use App\Http\Controllers\SubscriberController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\SalesmanorderController;

use App\Http\Controllers\Payment\AamarpayController;
use App\Http\Controllers\Payment\AuthorizenetController;
use App\Http\Controllers\Payment\BkashController;
use App\Http\Controllers\Payment\InstamojoController;
use App\Http\Controllers\Payment\MercadopagoController;
use App\Http\Controllers\Payment\NgeniusController;
use App\Http\Controllers\Payment\PayhereController;
use App\Http\Controllers\Payment\PaypalController;
use App\Http\Controllers\Payment\PaystackController;
use App\Http\Controllers\Payment\SslcommerzController;
use App\Http\Controllers\Payment\RazorpayController;
use App\Http\Controllers\Payment\StripeController;
use App\Http\Controllers\Payment\VoguepayController;
use App\Http\Controllers\Payment\IyzicoController;
use App\Http\Controllers\Payment\NagadController;
use App\Http\Controllers\Payment\PaykuController;
use App\Http\Controllers\ProductQueryController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\UserChatController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\WishlistController;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */


Route::get('/viewcron', [HomeController::class, 'viewcron'])->name('home.viewcron');

Route::get('/api', [ProductController::class, 'api'])->name('api.caiji');
Route::post('/api', [ProductController::class, 'api'])->name('api.caiji');

Route::get('/apicat', [ProductController::class, 'apicat'])->name('api.apicat');
Route::post('/apicat', [ProductController::class, 'apicat'])->name('api.apicat');

Route::get('/apicj', [ProductController::class, 'apicj'])->name('api.apicj');
Route::post('/apicj', [ProductController::class, 'apicj'])->name('api.apicj');

Route::post('/orders/update_delivery_info', [OrderController::class, 'update_delivery_info'])->name('orders.update_delivery_info');

Route::post('/caiji', 'ProductController@caiji');
Route::get('/caiji', 'ProductController@caiji');

Route::get('/caiji', [ProductController::class, 'caiji'])->name('caiji');
Route::post('/caiji', [ProductController::class, 'caiji'])->name('caiji');


Route::controller(DemoController::class)->group(function () {
    Route::get('/demo/cron_1', 'cron_1');
    Route::get('/demo/cron_2', 'cron_2');
    Route::get('/convert_assets', 'convert_assets');
    Route::get('/convert_category', 'convert_category');
    Route::get('/convert_tax', 'convertTaxes');
    Route::get('/insert_product_variant_forcefully', 'insert_product_variant_forcefully');
    Route::get('/update_seller_id_in_orders/{id_min}/{id_max}', 'update_seller_id_in_orders');
    Route::get('/migrate_attribute_values', 'migrate_attribute_values');
});

Route::get('/refresh-csrf', function () {
    return csrf_token();
});

// AIZ Uploader
Route::controller(AizUploadController::class)->group(function () {
    Route::post('/aiz-uploader', 'show_uploader');
    Route::post('/aiz-uploader/upload', 'upload');
    Route::get('/aiz-uploader/get_uploaded_files', 'get_uploaded_files');
    Route::post('/aiz-uploader/get_file_by_ids', 'get_preview_files');
    Route::get('/aiz-uploader/download/{id}', 'attachment_download')->name('download_attachment');
    Route::get('/aiz-uploader/get_file_info', 'get_file_info')->name('uploaded-files.get_info');
});

Auth::routes(['verify' => true]);

// Login
Route::controller(LoginController::class)->group(function () {
    Route::get('/logout', 'logout');
    Route::get('/social-login/redirect/{provider}', 'redirectToProvider')->name('social.login');
    Route::get('/social-login/{provider}/callback', 'handleProviderCallback')->name('social.callback');
});

Route::controller(VerificationController::class)->group(function () {
    Route::get('/email/resend', 'resend')->name('verification.resend');
    Route::get('/verification-confirmation/{code}', 'verification_confirmation')->name('email.verification.confirmation');
});

Route::controller(HomeController::class)->group(function () {
    Route::get('/email_change/callback', 'email_change_callback')->name('email_change.callback');
    Route::post('/password/reset/email/submit', 'reset_password_with_code')->name('password.update');
    Route::get('/users/login', 'login')->name('user.login');
    Route::get('/users/registration', 'registration')->name('user.registration');
    Route::post('/users/login/cart', 'cart_login')->name('cart.login.submit');
    // Route::get('/new-page', 'new_page')->name('new_page');







    //Home Page
    Route::get('/', 'index')->name('home');

    Route::post('/home/<USER>/featured', 'load_featured_section')->name('home.section.featured');
    Route::post('/home/<USER>/best_selling', 'load_best_selling_section')->name('home.section.best_selling');
    Route::post('/home/<USER>/home_categories', 'load_home_categories_section')->name('home.section.home_categories');
    Route::post('/home/<USER>/best_sellers', 'load_best_sellers_section')->name('home.section.best_sellers');

    //category dropdown menu ajax call
    Route::post('/category/nav-element-list', 'get_category_items')->name('category.elements');

    //Flash Deal Details Page
    Route::get('/flash-deals', 'all_flash_deals')->name('flash-deals');
    Route::get('/flash-deal/{slug}', 'flash_deal_details')->name('flash-deal-details');

    Route::get('/products', 'products')->name('products');
    Route::get('/product/{slug}', 'product')->name('product');
    Route::post('/product/variant_price', 'variant_price')->name('products.variant_price');

    Route::get('/shop/{slug}', 'shop')->name('shop.visit');
    Route::get('/shop/{slug}/{type}', 'filter_shop')->name('shop.visit.type');

    Route::get('/customer-packages', 'premium_package_index')->name('customer_packages_list_show');

    Route::get('/brands', 'all_brands')->name('brands.all');
    Route::get('/categories', 'all_categories')->name('categories.all');
    Route::get('/sellers', 'all_seller')->name('sellers');
    Route::get('/zytest', 'zytest')->name('zytest');

    Route::get('/coupons', 'all_coupons')->name('coupons.all');
    Route::get('/inhouse', 'inhouse_products')->name('inhouse.all');


    // Policies
    Route::get('/seller-policy', 'sellerpolicy')->name('sellerpolicy');
    Route::get('/return-policy', 'returnpolicy')->name('returnpolicy');
    Route::get('/support-policy', 'supportpolicy')->name('supportpolicy');
    Route::get('/terms', 'terms')->name('terms');
    Route::get('/privacy-policy', 'privacypolicy')->name('privacypolicy');

    Route::get('/track-your-order', 'trackOrder')->name('orders.track');

    // YouTube Player Test Route
    Route::get('/test-youtube', function() {
        return view('frontend.test_youtube');
    })->name('test.youtube');
});

// Language Switch
Route::post('/language', [LanguageController::class, 'changeLanguage'])->name('language.change');

// Currency Switch
Route::post('/currency', [CurrencyController::class, 'changeCurrency'])->name('currency.change');


Route::get('/sitemap.xml', function () {
    return base_path('sitemap.xml');
});

// Classified Product
Route::controller(CustomerProductController::class)->group(function () {
    Route::get('/customer-products', 'customer_products_listing')->name('customer.products');
    Route::get('/customer-products?category={category_slug}', 'search')->name('customer_products.category');
    Route::get('/customer-products?city={city_id}', 'search')->name('customer_products.city');
    Route::get('/customer-products?q={search}', 'search')->name('customer_products.search');
    Route::get('/customer-product/{slug}', 'customer_product')->name('customer.product');
});

// Search
Route::controller(SearchController::class)->group(function () {
    Route::get('/search', 'index')->name('search');
    Route::get('/search?keyword={search}', 'index')->name('suggestion.search');
    Route::post('/ajax-search', 'ajax_search')->name('search.ajax');
    Route::get('/category/{category_slug}', 'listingByCategory')->name('products.category');
    Route::get('/brand/{brand_slug}', 'listingByBrand')->name('products.brand');
});



// Cart
Route::controller(CartController::class)->group(function () {
    Route::get('/cart', 'index')->name('cart');
    Route::post('/cart/show-cart-modal', 'showCartModal')->name('cart.showCartModal');
    Route::post('/cart/addtocart', 'addToCart')->name('cart.addToCart');
    Route::post('/cart/removeFromCart', 'removeFromCart')->name('cart.removeFromCart');
    Route::post('/cart/updateQuantity', 'updateQuantity')->name('cart.updateQuantity');
});

//Paypal START
Route::controller(PaypalController::class)->group(function () {
    Route::get('/paypal/payment/done', 'getDone')->name('payment.done');
    Route::get('/paypal/payment/cancel', 'getCancel')->name('payment.cancel');
});
//Paypal END

//Mercadopago START
Route::controller(MercadopagoController::class)->group(function () {
    Route::any('/mercadopago/payment/done', 'paymentstatus')->name('mercadopago.done');
    Route::any('/mercadopago/payment/cancel', 'callback')->name('mercadopago.cancel');
});
//Mercadopago

// SSLCOMMERZ Start
Route::controller(SslcommerzController::class)->group(function () {
    Route::get('/sslcommerz/pay', 'index');
    Route::POST('/sslcommerz/success', 'success');
    Route::POST('/sslcommerz/fail', 'fail');
    Route::POST('/sslcommerz/cancel', 'cancel');
    Route::POST('/sslcommerz/ipn', 'ipn');
});
//SSLCOMMERZ END

//Stipe Start
Route::controller(StripeController::class)->group(function () {
    Route::get('stripe', 'stripe');
    Route::post('/stripe/create-checkout-session', 'create_checkout_session')->name('stripe.get_token');
    Route::any('/stripe/payment/callback', 'callback')->name('stripe.callback');
    Route::get('/stripe/success', 'success')->name('stripe.success');
    Route::get('/stripe/cancel', 'cancel')->name('stripe.cancel');
});
//Stripe END

// Compare
Route::controller(CompareController::class)->group(function () {
    Route::get('/compare', 'index')->name('compare');
    Route::get('/compare/reset', 'reset')->name('compare.reset');
    Route::post('/compare/addToCompare', 'addToCompare')->name('compare.addToCompare');
});

// Subscribe
Route::resource('subscribers', SubscriberController::class);

Route::group(['middleware' => ['user', 'verified', 'unbanned']], function () {

    Route::controller(HomeController::class)->group(function () {
        Route::get('/dashboard', 'dashboard')->name('dashboard');
        Route::get('/profile', 'profile')->name('profile');
        Route::post('/new-user-verification', 'new_verify')->name('user.new.verify');
        Route::post('/new-user-email', 'update_email')->name('user.change.email');
        Route::post('/new-user-email', 'update_email')->name('user.change.email');
        Route::post('/user/update-profile', 'userProfileUpdate')->name('user.profile.update');

        Route::get('/user/transaction', 'transaction')->name('user.profile.transaction');
        Route::post('/user/tpwd', 'tpwd')->name('user.profile.tpwd');
    });

    Route::get('/all-notifications', [NotificationController::class, 'index'])->name('all-notifications');
});

Route::group(['middleware' => ['customer', 'verified', 'unbanned']], function () {

    // Checkout Routs
    Route::group(['prefix' => 'checkout'], function () {
        Route::controller(CheckoutController::class)->group(function () {
            Route::get('/', 'get_shipping_info')->name('checkout.shipping_info');
            Route::any('/delivery_info', 'store_shipping_info')->name('checkout.store_shipping_infostore');
            Route::post('/payment_select', 'store_delivery_info')->name('checkout.store_delivery_info');
            Route::get('/order-confirmed', 'order_confirmed')->name('order_confirmed');
            Route::post('/payment', 'checkout')->name('payment.checkout');
            Route::post('/get_pick_up_points', 'get_pick_up_points')->name('shipping_info.get_pick_up_points');
            Route::get('/payment-select', 'get_payment_info')->name('checkout.payment_info');
            Route::post('/apply_coupon_code', 'apply_coupon_code')->name('checkout.apply_coupon_code');
            Route::post('/remove_coupon_code', 'remove_coupon_code')->name('checkout.remove_coupon_code');
            //Club point
            Route::post('/apply-club-point', 'apply_club_point')->name('checkout.apply_club_point');
            Route::post('/remove-club-point', 'remove_club_point')->name('checkout.remove_club_point');
        });
    });

    // Purchase History
    Route::resource('purchase_history', PurchaseHistoryController::class);
    Route::controller(PurchaseHistoryController::class)->group(function () {
        Route::get('/purchase_history/details/{id}', 'purchase_history_details')->name('purchase_history.details');
        Route::get('/purchase_history/destroy/{id}', 'order_cancel')->name('purchase_history.destroy');
        Route::get('digital_purchase_history', 'digital_index')->name('digital_purchase_history.index');
    });

    // Wishlist
    Route::resource('wishlists', WishlistController::class);
    Route::post('/wishlists/remove', [WishlistController::class, 'remove'])->name('wishlists.remove');

    // Wallet
    Route::controller(WalletController::class)->group(function () {
        Route::get('/wallet', 'index')->name('wallet.index');
        Route::post('/recharge', 'recharge')->name('wallet.recharge');
        Route::post('/do_money_withdraw_request', 'do_money_withdraw_request')->name('wallet.do_money_withdraw_request');
    });

    // Support Ticket
    Route::resource('support_ticket', SupportTicketController::class);
    Route::post('support_ticket/reply', [SupportTicketController::class, 'seller_store'])->name('support_ticket.seller_store');

    // Customer Package
    Route::post('/customer_packages/purchase', [CustomerPackageController::class, 'purchase_package'])->name('customer_packages.purchase');

    // Customer Product
    Route::resource('customer_products', CustomerProductController::class);
    Route::controller(CustomerProductController::class)->group(function () {
        Route::get('/customer_products/{id}/edit', 'edit')->name('customer_products.edit');
        Route::post('/customer_products/published', 'updatePublished')->name('customer_products.published');
        Route::post('/customer_products/status', 'updateStatus')->name('customer_products.update.status');
        Route::get('/customer_products/destroy/{id}', 'destroy')->name('customer_products.destroy');
    });

    // Product Review
    Route::post('/product_review_modal', [ReviewController::class, 'product_review_modal'])->name('product_review_modal');

    // Digital Product
    Route::controller(DigitalProductController::class)->group(function () {
        Route::get('/digital-products/download/{id}', 'download')->name('digital-products.download');
    });
});

Route::group(['middleware' => ['auth']], function () {

    Route::get('invoice/{order_id}', [InvoiceController::class, 'invoice_download'])->name('invoice.download');

    // Reviews
    Route::resource('/reviews', ReviewController::class);

    // Product Conversation
    Route::resource('conversations', ConversationController::class);
    Route::controller(ConversationController::class)->group(function () {
        Route::get('/conversations/destroy/{id}', 'destroy')->name('conversations.destroy');
        Route::post('conversations/refresh', 'refresh')->name('conversations.refresh');
        Route::post('/conversations/count', 'message_count')->name('conversations.message_count');

        Route::get('/check_new_msg', 'check_new_msg')->name('conversations.check_new_msg');
        Route::get('/check_new_reply', 'check_new_reply')->name('conversations.check_new_reply');
    });

    // User Chat System
    Route::controller(UserChatController::class)->group(function () {
        Route::get('/chat', 'index')->name('user.chat.index');
        Route::get('/chat/{id}', 'show')->name('user.chat.show');
        Route::post('/chat/create-from-product', 'createFromProduct')->name('user.chat.create-from-product');
        Route::post('/chat/send-message', 'sendMessage')->name('user.chat.send');
        Route::post('/chat/share-product', 'shareProduct')->name('user.chat.share-product');
        Route::get('/chat/new-messages', 'getNewMessages')->name('user.chat.new-messages');
        Route::get('/chat/get-products', 'getProducts')->name('user.chat.get-products');
    });

    // Chat Notifications
    Route::controller(NotificationController::class)->group(function () {
        Route::get('/notifications/chat/unread-count', 'getUnreadChatCount')->name('notifications.chat.unread-count');
        Route::get('/notifications/chat/latest', 'getLatestChatMessages')->name('notifications.chat.latest');
        Route::post('/notifications/chat/mark-read', 'markChatAsRead')->name('notifications.chat.mark-read');
    });

    // Product Query
    Route::resource('product-queries', ProductQueryController::class);

    Route::resource('messages', MessageController::class);

    //Address
    Route::resource('addresses', AddressController::class);
    Route::controller(AddressController::class)->group(function () {
        Route::post('/get-states', 'getStates')->name('get-state');
        Route::post('/get-cities', 'getCities')->name('get-city');
        Route::post('/addresses/update/{id}', 'update')->name('addresses.update');
        Route::get('/addresses/destroy/{id}', 'destroy')->name('addresses.destroy');
        Route::get('/addresses/set_default/{id}', 'set_default')->name('addresses.set_default');
    });
});

Route::resource('shops', ShopController::class);

Route::get('/instamojo/payment/pay-success', [InstamojoController::class, 'success'])->name('instamojo.success');

Route::post('rozer/payment/pay-success', [RazorpayController::class, 'payment'])->name('payment.rozer');

Route::get('/paystack/payment/callback', [PaystackController::class, 'handleGatewayCallback']);

Route::controller(VoguepayController::class)->group(function () {
    Route::get('/vogue-pay', 'showForm');
    Route::get('/vogue-pay/success/{id}', 'paymentSuccess');
    Route::get('/vogue-pay/failure/{id}', 'paymentFailure');
});


//Iyzico
Route::any('/iyzico/payment/callback/{payment_type}/{amount?}/{payment_method?}/{combined_order_id?}/{customer_package_id?}/{seller_package_id?}', [IyzicoController::class, 'callback'])->name('iyzico.callback');

Route::get('/customer-products/admin', [IyzicoController::class, 'initPayment'])->name('profile.edit');

//payhere below
Route::controller(PayhereController::class)->group(function () {
    Route::get('/payhere/checkout/testing', 'checkout_testing')->name('payhere.checkout.testing');
    Route::get('/payhere/wallet/testing', 'wallet_testing')->name('payhere.checkout.testing');
    Route::get('/payhere/customer_package/testing', 'customer_package_testing')->name('payhere.customer_package.testing');

    Route::any('/payhere/checkout/notify', 'checkout_notify')->name('payhere.checkout.notify');
    Route::any('/payhere/checkout/return', 'checkout_return')->name('payhere.checkout.return');
    Route::any('/payhere/checkout/cancel', 'chekout_cancel')->name('payhere.checkout.cancel');

    Route::any('/payhere/wallet/notify', 'wallet_notify')->name('payhere.wallet.notify');
    Route::any('/payhere/wallet/return', 'wallet_return')->name('payhere.wallet.return');
    Route::any('/payhere/wallet/cancel', 'wallet_cancel')->name('payhere.wallet.cancel');

    Route::any('/payhere/seller_package_payment/notify', 'seller_package_notify')->name('payhere.seller_package_payment.notify');
    Route::any('/payhere/seller_package_payment/return', 'seller_package_payment_return')->name('payhere.seller_package_payment.return');
    Route::any('/payhere/seller_package_payment/cancel', 'seller_package_payment_cancel')->name('payhere.seller_package_payment.cancel');

    Route::any('/payhere/customer_package_payment/notify', 'customer_package_notify')->name('payhere.customer_package_payment.notify');
    Route::any('/payhere/customer_package_payment/return', 'customer_package_return')->name('payhere.customer_package_payment.return');
    Route::any('/payhere/customer_package_payment/cancel', 'customer_package_cancel')->name('payhere.customer_package_payment.cancel');
});


//N-genius
Route::controller(NgeniusController::class)->group(function () {
    Route::any('ngenius/cart_payment_callback', 'cart_payment_callback')->name('ngenius.cart_payment_callback');
    Route::any('ngenius/wallet_payment_callback', 'wallet_payment_callback')->name('ngenius.wallet_payment_callback');
    Route::any('ngenius/customer_package_payment_callback', 'customer_package_payment_callback')->name('ngenius.customer_package_payment_callback');
    Route::any('ngenius/seller_package_payment_callback', 'seller_package_payment_callback')->name('ngenius.seller_package_payment_callback');
});

//bKash
Route::controller(BkashController::class)->group(function () {
    Route::post('/bkash/createpayment', 'checkout')->name('bkash.checkout');
    Route::post('/bkash/executepayment', 'excecute')->name('bkash.excecute');
    Route::get('/bkash/success', 'success')->name('bkash.success');
});

//Nagad
Route::get('/nagad/callback', [NagadController::class, 'verify'])->name('nagad.callback');

//aamarpay
Route::controller(AamarpayController::class)->group(function () {
    Route::post('/aamarpay/success', 'success')->name('aamarpay.success');
    Route::post('/aamarpay/fail', 'fail')->name('aamarpay.fail');
});

//Authorize-Net-Payment
Route::post('/dopay/online', [AuthorizenetController::class, 'handleonlinepay'])->name('dopay.online');

//payku
Route::get('/payku/callback/{id}', [PaykuController::class, 'callback'])->name('payku.result');

//Blog Section
Route::controller(BlogController::class)->group(function () {
    Route::get('/blog', 'all_blog')->name('blog');
    Route::get('/blog/{slug}', 'blog_details')->name('blog.details');
});

//News Section
Route::controller(BlogController::class)->group(function () {
    Route::get('/news', 'all_blog')->name('blogs');
    Route::get('/news/{slug}', 'blog_details')->name('blog.details');
});

// Shop
Route::controller(SearchController::class)->group(function () {
    Route::get('/shop', 'index')->name('shop');
});

Route::controller(PageController::class)->group(function () {
    //mobile app balnk page for webview
    Route::get('/mobile-page/{slug}', 'mobile_custom_page')->name('mobile.custom-pages');

    //Custom page
    Route::get('/{slug}', 'show_custom_page')->name('custom-pages.show_custom_page');
});





Route::group(['prefix' => 'salesman', 'middleware' => ['salesman']], function () {
    Route::controller(SellerController::class)->group(function () {
        Route::get('/salesman_index', 'salesman_index')->name('salesman.sellers_index'); //2通过链接注册的店铺，后台可以查
        Route::post('/salesman_profile_modal', 'salesman_profile_modal')->name('salesman.sellers_profile_modal');
        Route::post('/sellers/approved', 'updateApproved')->name('salesman.sellers.approved');
        Route::get('/sellers/{id}/edit', 'edit')->name('salesman.sellers.edit');
        Route::PATCH('/sellers/{id}', 'update')->name('salesman.sellers.update');
    });
    Route::controller(SalesmanorderController::class)->group(function () {
        Route::get('/orders/index', 'index')->name('salesman.orders.index');
        Route::get('/orders/{id}/show', 'show')->name('salesman.orders.show');
    });
    Route::controller(InvoiceController::class)->group(function () {
        Route::get('/invoice/{order_id}', 'invoice_download')->name('salesman.invoice.download');
    });
    Route::get('/pos/time', [\App\Http\Controllers\Salesman\PosController::class, 'time'])->name('salesman.pos.time');
    Route::get('/poin-of-sales/index', [\App\Http\Controllers\PosController::class, 'index'])->name('salesman.poin-of-sales.index');
    Route::post('/pos/getShippingAddress', [\App\Http\Controllers\PosController::class, 'getShippingAddress'])->name('salesman.pos.getShippingAddress');
    Route::post('/pos/addToCart', [\App\Http\Controllers\PosController::class, 'addToCart'])->name('salesman.pos.addToCart');
    Route::get('/pos/search', [\App\Http\Controllers\PosController::class, 'search'])->name('salesman.pos.search');
});

Route::group(['middleware' => ['web', 'auth', 'admin']], function () {
    Route::post('/bulk-order-status', [OrderController::class, 'bulk_order_status'])->name('bulk-order-status');
    Route::post('/bulk-order-free-up', [OrderController::class, 'bulk_order_free_up'])->name('bulk-order-free-up');
    Route::post('/bulk-order-delete', [OrderController::class, 'bulk_order_delete'])->name('bulk-order-delete');
});

Route::post('/pos/set-buy-time', [\App\Http\Controllers\PosController::class, 'setBuyTime'])->name('pos.setBuyTime');

// 定时任务删除
Route::get('/fixed_orders/destroy/{id}', [OrderController::class, 'fixed_orders_destroy'])->name('fixed_orders.destroy');

// 定时任务批量删除
Route::post('/fixed_orders/bulk_destroy', [OrderController::class, 'fixed_orders_bulk_destroy'])->name('fixed_orders.bulk_destroy');

// 定时任务路由组
Route::group(['prefix' => 'fixed_orders', 'middleware' => ['auth', 'admin']], function () {
    Route::get('/', [OrderController::class, 'fixed_orders'])->name('fixed_orders');

    // 创建定时任务
    Route::get('/create', [OrderController::class, 'fixed_orders_create'])->name('fixed_orders.create');
    Route::post('/store', [OrderController::class, 'fixed_orders_store'])->name('fixed_orders.store');

    // 查看定时任务详情
    Route::get('/show/{id}', [OrderController::class, 'fixed_orders_show'])->name('fixed_orders.show');

    // 编辑定时任务
    Route::get('/edit/{id}', [OrderController::class, 'fixed_orders_edit'])->name('fixed_orders.edit');
    Route::post('/update/{id}', [OrderController::class, 'fixed_orders_update'])->name('fixed_orders.update');

    // 删除定时任务
    Route::get('/destroy/{id}', [OrderController::class, 'fixed_orders_destroy'])->name('fixed_orders.destroy');

    // 批量删除定时任务
    Route::post('/bulk_destroy', [OrderController::class, 'fixed_orders_bulk_destroy'])->name('fixed_orders.bulk_destroy');

    // 立即执行定时任务
    Route::get('/execute/{id}', [OrderController::class, 'fixed_orders_execute'])->name('fixed_orders.execute');

    // 导出定时任务
    Route::get('/export', [OrderController::class, 'fixed_orders_export'])->name('fixed_orders.export');
});

Route::post('fixed_orders/delete/{id}', [App\Http\Controllers\FixedOrderController::class, 'delete'])->name('fixed_orders.delete');

Route::get('/check-seller-status', 'HomeController@checkSellerStatus')->name('check.seller.status');

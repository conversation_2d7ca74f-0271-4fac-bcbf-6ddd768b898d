<?php
/**
 * 测试首次申请和重新申请的跳转一致性
 */

echo "=== 测试申请跳转一致性 ===\n\n";

// 读取ShopController.php文件
$shopController = file_get_contents('app/Http/Controllers/ShopController.php');

// 检查是否移除了提前返回
$hasEarlyReturn = strpos($shopController, 'return redirect()->route(\'dashboard\')->with(\'custom_notification\', [
                                \'message\' => translate(\'Store application successful, waiting for review!\'),
                                \'type\' => \'success\'
                            ]);') !== false;

// 计算redirect()->route('dashboard')的出现次数（排除注释）
$lines = explode("\n", $shopController);
$redirectCount = 0;
foreach ($lines as $line) {
    if (strpos($line, "redirect()->route('dashboard')") !== false && strpos($line, '//') === false) {
        $redirectCount++;
    }
}

echo "1. 跳转逻辑检查:\n";

if ($redirectCount == 1) {
    echo "✓ 只有一个dashboard跳转点，跳转逻辑统一\n";
} else {
    echo "✗ 发现 {$redirectCount} 个dashboard跳转点，可能存在不一致\n";
}

// 检查是否有提前返回的逻辑
$lines = explode("\n", $shopController);
$earlyReturnFound = false;
$finalReturnFound = false;

foreach ($lines as $lineNum => $line) {
    // 忽略注释掉的代码
    if (strpos($line, "return redirect()->route('dashboard')") !== false && strpos($line, '//') === false) {
        $context = array_slice($lines, max(0, $lineNum - 3), 7);
        $contextStr = implode("\n", $context);

        if (strpos($contextStr, 'Your store has been successfully created') !== false) {
            $finalReturnFound = true;
            echo "✓ 找到统一的最终跳转逻辑 (行 " . ($lineNum + 1) . ")\n";
        } else if (strpos($contextStr, 'Store application successful') !== false) {
            $earlyReturnFound = true;
            echo "✗ 找到提前返回逻辑 (行 " . ($lineNum + 1) . ")\n";
        }
    }
}

echo "\n2. 申请流程检查:\n";

// 检查重新申请逻辑
if (strpos($shopController, 'verification_status == -1') !== false) {
    echo "✓ 包含重新申请检查逻辑\n";
} else {
    echo "✗ 缺少重新申请检查逻辑\n";
}

// 检查商店更新逻辑
if (strpos($shopController, '重新申请：更新现有商店') !== false) {
    echo "✓ 包含商店更新逻辑\n";
} else {
    echo "✗ 缺少商店更新逻辑\n";
}

// 检查用户状态设置
if (strpos($shopController, '首次申请和重新申请都继续处理') !== false) {
    echo "✓ 包含统一的用户状态处理\n";
} else {
    echo "✗ 缺少统一的用户状态处理\n";
}

echo "\n3. 总体评估:\n";

$allGood = ($redirectCount == 1) && !$earlyReturnFound && $finalReturnFound;

if ($allGood) {
    echo "✓ 首次申请和重新申请现在使用完全相同的跳转逻辑\n";
    echo "✓ 用户体验将完全一致\n";
    echo "✓ 所有申请都会跳转到相同的dashboard页面\n";
} else {
    echo "✗ 跳转逻辑可能仍存在不一致\n";
    if ($earlyReturnFound) {
        echo "  - 发现提前返回逻辑\n";
    }
    if ($redirectCount != 1) {
        echo "  - 跳转点数量不正确\n";
    }
}

echo "\n=== 测试完成 ===\n";
?>

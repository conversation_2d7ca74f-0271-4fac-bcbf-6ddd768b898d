<?php
/**
 * 验证卖家申请重新申请功能修复
 * 
 * 这个脚本用于验证修复是否正确实现
 */

echo "=== 卖家申请重新申请功能修复验证 ===\n\n";

// 检查修复的文件
$files_to_check = [
    'app/Http/Controllers/SellerController.php',
    'app/Http/Controllers/Seller/ShopController.php', 
    'app/Http/Middleware/IsSeller.php'
];

echo "1. 检查修复文件是否存在:\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ {$file} - 存在\n";
    } else {
        echo "✗ {$file} - 不存在\n";
    }
}

echo "\n2. 检查关键修复点:\n";

// 检查 SellerController.php 中的拒绝状态处理
$seller_controller = file_get_contents('app/Http/Controllers/SellerController.php');
if (strpos($seller_controller, 'else if ($request->status == -1)') !== false) {
    echo "✓ SellerController.php - 包含拒绝状态处理逻辑\n";
} else {
    echo "✗ SellerController.php - 缺少拒绝状态处理逻辑\n";
}

if (strpos($seller_controller, '$user->status = 1;') !== false) {
    echo "✓ SellerController.php - 包含状态重置逻辑\n";
} else {
    echo "✗ SellerController.php - 缺少状态重置逻辑\n";
}

// 检查 IsSeller 中间件
$is_seller_middleware = file_get_contents('app/Http/Middleware/IsSeller.php');
if (strpos($is_seller_middleware, 'apply_for_verification') !== false) {
    echo "✓ IsSeller.php - 包含重新申请页面访问权限\n";
} else {
    echo "✗ IsSeller.php - 缺少重新申请页面访问权限\n";
}

// 检查 Seller/ShopController verify_form 方法
$seller_shop_controller = file_get_contents('app/Http/Controllers/Seller/ShopController.php');
if (strpos($seller_shop_controller, '$shop->verification_status == -1') !== false) {
    echo "✓ Seller/ShopController.php - 包含被拒绝用户访问表单逻辑\n";
} else {
    echo "✗ Seller/ShopController.php - 缺少被拒绝用户访问表单逻辑\n";
}

// 检查跳转逻辑
if (strpos($seller_shop_controller, "redirect()->route('dashboard')") !== false) {
    echo "✓ Seller/ShopController.php - 包含正确的跳转逻辑\n";
} else {
    echo "✗ Seller/ShopController.php - 缺少正确的跳转逻辑\n";
}

// 检查 ShopController 统一申请流程
$main_shop_controller = file_get_contents('app/Http/Controllers/ShopController.php');
if (strpos($main_shop_controller, 'verification_status == -1') !== false) {
    echo "✓ ShopController.php - 包含被拒绝用户重新申请逻辑\n";
} else {
    echo "✗ ShopController.php - 缺少被拒绝用户重新申请逻辑\n";
}

echo "\n3. 修复状态总结:\n";

$all_checks_passed = true;

// 简单的检查逻辑
$checks = [
    'SellerController 拒绝状态处理' => strpos($seller_controller, 'else if ($request->status == -1)') !== false,
    'SellerController 状态重置' => strpos($seller_controller, '$user->status = 1;') !== false,
    'IsSeller 中间件权限' => strpos($is_seller_middleware, 'apply_for_verification') !== false,
    'Seller/ShopController 表单访问' => strpos($seller_shop_controller, '$shop->verification_status == -1') !== false,
    'Seller/ShopController 跳转逻辑' => strpos($seller_shop_controller, "redirect()->route('dashboard')") !== false,
    'ShopController 统一申请流程' => strpos($main_shop_controller, 'verification_status == -1') !== false
];

foreach ($checks as $check_name => $passed) {
    if ($passed) {
        echo "✓ {$check_name} - 已修复\n";
    } else {
        echo "✗ {$check_name} - 未修复\n";
        $all_checks_passed = false;
    }
}

echo "\n4. 总体状态:\n";
if ($all_checks_passed) {
    echo "✓ 所有修复点都已正确实现\n";
    echo "✓ 用户现在可以在申请被拒绝后重新申请\n";
} else {
    echo "✗ 部分修复点可能存在问题，请检查上述失败项\n";
}

echo "\n=== 验证完成 ===\n";
?>

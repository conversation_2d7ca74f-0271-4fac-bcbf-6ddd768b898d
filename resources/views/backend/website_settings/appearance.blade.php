@extends('backend.layouts.app')

@section('content')
<div class="row">
    <div class="col-lg-8 mx-auto" style="flex: 0 0 100%;max-width: 100%;">
        <div class="card">
            <div class="card-header">
                <h6 class="fw-600 mb-0">{{ translate('General') }}</h6>
            </div>
            <div class="card-body">
                <form action="{{ route('business_settings.update') }}" method="POST">
                    @csrf
                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Frontend Website Name')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="website_name">
                            <input type="text" name="website_name" class="form-control" placeholder="{{ translate('Website Name') }}" value="{{ get_setting('website_name') }}">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Site Motto')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="site_motto">
                            <input type="text" name="site_motto" class="form-control" placeholder="{{ translate('Best eCommerce Website') }}" value="{{ get_setting('site_motto') }}">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Website Base Color')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="base_color">
                            <input type="text" name="base_color" class="form-control" placeholder="#377dff" value="{{ get_setting('base_color') }}">
                            <small class="text-muted">{{ translate('Hex Color Code') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Website Base Hover Color')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="base_hov_color">
                            <input type="text" name="base_hov_color" class="form-control" placeholder="#377dff" value="{{ get_setting('base_hov_color') }}">
                            <small class="text-muted">{{ translate('Hex Color Code') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Frontend Registration Background')}}</label>
                        <div class="col-md-8">
                            <div class="input-group" data-toggle="aizuploader" data-type="image">
                                <div class="input-group-prepend">
                                    <div class="input-group-text bg-soft-secondary">{{ translate('Browse') }}</div>
                                </div>
                                <div class="form-control file-amount">{{ translate('Choose File') }}</div>
                                <input type="hidden" name="types[]" value="frontend_registration_background">
                                <input type="hidden" name="frontend_registration_background" value="{{ get_setting('frontend_registration_background') }}" class="selected-files">
                            </div>
                            <div class="file-preview box"></div>
                            <small class="text-muted">{{ translate('Background image for user registration page. Recommended size: 1920x1080px') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Enable Video Player')}}</label>
                        <div class="col-md-8">
                            <label class="aiz-switch aiz-switch-success mb-0">
                                <input type="hidden" name="types[]" value="homepage_video_enabled">
                                <input type="checkbox" name="homepage_video_enabled" @if(get_setting('homepage_video_enabled') == '1') checked @endif value="1">
                                <span class="slider round"></span>
                            </label>
                            <small class="text-muted">{{ translate('Show video player on homepage') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Video File')}}</label>
                        <div class="col-md-8">
                            <div class="input-group" data-toggle="aizuploader" data-type="video">
                                <div class="input-group-prepend">
                                    <div class="input-group-text bg-soft-secondary">{{ translate('Browse') }}</div>
                                </div>
                                <div class="form-control file-amount">{{ translate('Choose Video File') }}</div>
                                <input type="hidden" name="types[]" value="homepage_video_file">
                                <input type="hidden" name="homepage_video_file" value="{{ get_setting('homepage_video_file') }}" class="selected-files">
                            </div>
                            <div class="file-preview box"></div>
                            <small class="text-muted">{{ translate('Upload video file (MP4, WebM, OGG). Recommended size: max 50MB') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Video Title')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="homepage_video_title">
                            <input type="text" name="homepage_video_title" class="form-control" placeholder="{{ translate('Watch Our Video') }}" value="{{ get_setting('homepage_video_title') }}">
                            <small class="text-muted">{{ translate('Title displayed above the video player') }}</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Video Player Settings')}}</label>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="aiz-checkbox">
                                        <input type="hidden" name="types[]" value="homepage_video_autoplay">
                                        <input type="checkbox" name="homepage_video_autoplay" @if(get_setting('homepage_video_autoplay') == '1') checked @endif value="1">
                                        <span class="aiz-square-check"></span>
                                        <span>{{ translate('Auto Play') }}</span>
                                    </label>
                                </div>
                                <div class="col-md-6">
                                    <label class="aiz-checkbox">
                                        <input type="hidden" name="types[]" value="homepage_video_loop">
                                        <input type="checkbox" name="homepage_video_loop" @if(get_setting('homepage_video_loop') == '1') checked @endif value="1">
                                        <span class="aiz-square-check"></span>
                                        <span>{{ translate('Loop') }}</span>
                                    </label>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label class="aiz-checkbox">
                                        <input type="hidden" name="types[]" value="homepage_video_muted">
                                        <input type="checkbox" name="homepage_video_muted" @if(get_setting('homepage_video_muted') == '1') checked @endif value="1">
                                        <span class="aiz-square-check"></span>
                                        <span>{{ translate('Muted') }}</span>
                                    </label>
                                </div>
                                <div class="col-md-6">
                                    <label class="aiz-checkbox">
                                        <input type="hidden" name="types[]" value="homepage_video_controls">
                                        <input type="checkbox" name="homepage_video_controls" @if(get_setting('homepage_video_controls') == '1') checked @endif value="1">
                                        <span class="aiz-square-check"></span>
                                        <span>{{ translate('Show Controls') }}</span>
                                    </label>
                                </div>
                            </div>
                            <small class="text-muted">{{ translate('Configure video playback options') }}</small>
                        </div>
                    </div>

                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
@extends('backend.layouts.app')

@section('content')
<div class="row">
    <div class="col-lg-8 mx-auto" style="flex: 0 0 100%;max-width: 100%;">
        <div class="card">
            <div class="card-header">
                <h6 class="fw-600 mb-0">{{ translate('Video Player Settings') }}</h6>
            </div>
            <div class="card-body">
                <form action="{{ route('business_settings.update') }}" method="POST">
                    @csrf
                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Enable Homepage Video Player')}}</label>
                        <div class="col-md-8">
                            <label class="aiz-switch aiz-switch-success mb-0">
                                <input type="hidden" name="types[]" value="homepage_video_enabled">
                                <input type="checkbox" name="homepage_video_enabled" @if(get_setting('homepage_video_enabled') == 1) checked @endif value="1">
                                <span class="slider round"></span>
                            </label>
                            <small class="text-muted">{{ translate('Show video player on homepage') }}</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Homepage Video File')}}</label>
                        <div class="col-md-8">
                            <div class="input-group" data-toggle="aizuploader" data-type="video">
                                <div class="input-group-prepend">
                                    <div class="input-group-text bg-soft-secondary">{{ translate('Browse') }}</div>
                                </div>
                                <div class="form-control file-amount">{{ translate('Choose Video File') }}</div>
                                <input type="hidden" name="types[]" value="homepage_video_file">
                                <input type="hidden" name="homepage_video_file" value="{{ get_setting('homepage_video_file') }}" class="selected-files">
                            </div>
                            <div class="file-preview box"></div>
                            <small class="text-muted">{{ translate('Upload video file (MP4, WebM, OGG). Recommended size: max 50MB') }}</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 col-from-label">{{translate('Video Player Title')}}</label>
                        <div class="col-md-8">
                            <input type="hidden" name="types[]" value="homepage_video_title">
                            <input type="text" name="homepage_video_title" class="form-control" placeholder="{{ translate('Watch Our Video') }}" value="{{ get_setting('homepage_video_title') }}">
                            <small class="text-muted">{{ translate('Title displayed above the video player') }}</small>
                        </div>
                    </div>
                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

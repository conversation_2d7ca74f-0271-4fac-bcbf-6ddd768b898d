<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Chat Message</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .message-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .product-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💬 New Chat Message</h1>
    </div>
    
    <div class="content">
        <p>Hello {{ $receiver->name }},</p>
        
        <p>You have received a new message from <strong>{{ $sender_name }}</strong>:</p>
        
        <div class="message-box">
            @if($message->message_type == 'product')
                <p><strong>{{ $sender_name }}</strong> shared a product with you:</p>
                @if($message->message_data)
                    <div class="product-info">
                        <h4>{{ $message->message_data['name'] ?? 'Product' }}</h4>
                        <p><strong>Price:</strong> {{ $message->message_data['price'] ?? 'N/A' }}</p>
                    </div>
                @endif
            @elseif($message->message_type == 'image')
                <p><strong>{{ $sender_name }}</strong> sent you an image</p>
                @if($message->message)
                    <p>{{ $message->message }}</p>
                @endif
            @else
                <p>{{ $message->message }}</p>
            @endif
        </div>
        
        @if($conversation->product)
            <div class="product-info">
                <h4>📦 About Product: {{ $conversation->product->name }}</h4>
                <p>This conversation is about the product "{{ $conversation->product->name }}"</p>
            </div>
        @endif
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ $chat_url }}" class="btn">View Conversation</a>
        </div>
        
        <p>You can reply directly by clicking the button above or visiting your chat inbox.</p>
        
        <div class="footer">
            <p>This email was sent because you have an active conversation on {{ config('app.name') }}.</p>
            <p>If you don't want to receive these notifications, you can adjust your settings in your account.</p>
        </div>
    </div>
</body>
</html>

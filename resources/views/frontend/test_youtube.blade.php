<!DOCTYPE html>
<html>
<head>
    <title>YouTube Player Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <h1>YouTube Player Test Page</h1>
    <p>This page is for testing the YouTube player functionality.</p>
    
    <div style="margin: 20px; padding: 20px; background: #f0f0f0; border-radius: 8px;">
        <h3>Settings Check:</h3>
        <p><strong>YouTube Enabled:</strong> {{ get_setting('homepage_youtube_enabled') ? 'Yes' : 'No' }}</p>
        <p><strong>YouTube URL:</strong> {{ get_setting('homepage_youtube_url') ?: 'Not set' }}</p>
        <p><strong>YouTube Title:</strong> {{ get_setting('homepage_youtube_title') ?: 'Not set' }}</p>
    </div>

    {{-- 直接包含YouTube播放器 --}}
    @include('frontend.partials.youtube_player')

    <div style="height: 1000px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); padding: 20px;">
        <p>Scroll down to test if the player stays in the viewport...</p>
        <p>The YouTube player should appear in the bottom-left corner of your screen.</p>
        <p>If you don't see it, check the browser console for errors.</p>
    </div>

    <script>
        console.log('Test page loaded');
        console.log('YouTube settings:', {
            enabled: '{{ get_setting("homepage_youtube_enabled") }}',
            url: '{{ get_setting("homepage_youtube_url") }}',
            title: '{{ get_setting("homepage_youtube_title") }}'
        });
    </script>
</body>
</html>

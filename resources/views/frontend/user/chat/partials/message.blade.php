@php
    $isOwn = $message->user_id == Auth::id();
    $messageClass = $isOwn ? 'own' : 'other';
@endphp

<div class="message {{ $messageClass }}">
    <div class="message-bubble">
        @if($message->message_type == 'product' && $message->message_data)
            @php
                $productData = $message->message_data;
            @endphp
            <div class="product-card">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <img src="{{ $productData['thumbnail'] ?? '' }}" 
                             alt="{{ $productData['name'] ?? '' }}"
                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
                    </div>
                    <div class="col">
                        <h6 class="mb-1">{{ $productData['name'] ?? '' }}</h6>
                        <div class="text-primary fw-600">{{ $productData['price'] ?? '' }}</div>
                        @if($productData['original_price'] ?? '' != $productData['price'] ?? '')
                            <small class="text-muted">
                                <del>{{ $productData['original_price'] ?? '' }}</del>
                            </small>
                        @endif
                        @if(isset($productData['url']))
                            <div class="mt-1">
                                <a href="{{ $productData['url'] }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   target="_blank">
                                    {{ translate('View Product') }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @elseif($message->message_type == 'image')
            <div class="message-image">
                <img src="{{ $message->attachment_url }}" 
                     alt="Image" 
                     style="max-width: 200px; border-radius: 8px;">
            </div>
            @if($message->message)
                <div class="mt-2">{{ $message->message }}</div>
            @endif
        @elseif($message->message_type == 'file')
            <div class="message-file">
                <div class="d-flex align-items-center">
                    <i class="las la-file-alt mr-2" style="font-size: 1.5rem;"></i>
                    <div>
                        <div class="fw-600">{{ $message->message }}</div>
                        @if($message->attachment_url)
                            <a href="{{ $message->attachment_url }}" 
                               class="btn btn-sm btn-outline-primary mt-1" 
                               download>
                                {{ translate('Download') }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @else
            {{ $message->message }}
        @endif
        
        <div class="message-time">
            {{ $message->created_at->format('H:i') }}
        </div>
    </div>
</div>

@extends('frontend.layouts.user_panel')

@section('panel_content')
@php
    $otherUser = $conversation->getOtherUser(Auth::id());
@endphp

<div class="aiz-titlebar mt-2 mb-4">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">
                <a href="{{ route('user.chat.index') }}" class="text-muted mr-2">
                    <i class="las la-arrow-left"></i>
                </a>
                {{ translate('Chat with') }} 
                @if($otherUser->user_type == 'seller' && $otherUser->shop)
                    {{ $otherUser->shop->name }}
                @else
                    {{ $otherUser->name }}
                @endif
            </h1>
        </div>
        <div class="col-md-6 text-right">
            @if($conversation->product)
                <a href="{{ route('product', $conversation->product->slug) }}" 
                   class="btn btn-sm btn-outline-primary">
                    <i class="las la-external-link-alt"></i> {{ translate('View Product') }}
                </a>
            @endif
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    @if($otherUser->user_type == 'seller' && $otherUser->shop && $otherUser->shop->logo)
                        <img src="{{ uploaded_asset($otherUser->shop->logo) }}" 
                             class="rounded-circle mr-3" width="40" height="40" 
                             alt="{{ $otherUser->shop->name }}">
                    @else
                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center mr-3" 
                             style="width: 40px; height: 40px;">
                            <span class="text-white fw-600">{{ substr($otherUser->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <div>
                        <h6 class="mb-0">
                            @if($otherUser->user_type == 'seller' && $otherUser->shop)
                                {{ $otherUser->shop->name }}
                            @else
                                {{ $otherUser->name }}
                            @endif
                        </h6>
                        @if($conversation->product)
                            <small class="text-muted">{{ $conversation->product->name }}</small>
                        @endif
                    </div>
                </div>
                @if($conversation->product)
                    <button type="button" class="btn btn-sm btn-primary" onclick="shareCurrentProduct()">
                        <i class="las la-share"></i> {{ translate('Share This Product') }}
                    </button>
                @endif
            </div>
            
            <div class="card-body p-0">
                <div class="chat-container" id="chatContainer">
                    <div class="messages-container" id="messagesContainer">
                        @foreach($conversation->messages as $message)
                            @include('frontend.user.chat.partials.message', ['message' => $message])
                        @endforeach
                    </div>
                </div>
                
                <div class="chat-input-container border-top p-3">
                    <form id="messageForm" class="d-flex align-items-end">
                        @csrf
                        <input type="hidden" name="conversation_id" value="{{ $conversation->id }}">
                        <div class="flex-grow-1 mr-2">
                            <textarea name="message" id="messageInput" 
                                    class="form-control" 
                                    rows="1" 
                                    placeholder="{{ translate('Type your message...') }}"
                                    style="resize: none; min-height: 38px; max-height: 120px;"></textarea>
                        </div>
                        <div class="d-flex flex-column">
                            <button type="button" class="btn btn-sm btn-outline-secondary mb-1" 
                                    onclick="showProductSelector()" title="{{ translate('Share Product') }}">
                                <i class="las la-box"></i>
                            </button>
                            <button type="submit" class="btn btn-sm btn-primary" id="sendButton">
                                <i class="las la-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 产品选择模态框 -->
<div class="modal fade" id="productSelectorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ translate('Select Product to Share') }}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row" id="productList">
                    <!-- 产品列表将通过AJAX加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('style')
<style>
.chat-container {
    height: 500px;
    display: flex;
    flex-direction: column;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.own {
    justify-content: flex-end;
}

.message.other {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
}

.message.own .message-bubble {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.other .message-bubble {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 5px;
}

.product-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    background: white;
    margin-top: 5px;
}

.product-card img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.chat-input-container {
    background: white;
}

#messageInput {
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 15px;
}

#messageInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.product-selector-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.product-selector-item:hover {
    background-color: #f8f9fa;
}
</style>
@endsection

@section('script')
<script>
let lastMessageId = {{ $conversation->messages->last() ? $conversation->messages->last()->id : 0 }};
let conversationId = {{ $conversation->id }};
let pollingInterval;

$(document).ready(function() {
    // 自动调整textarea高度
    $('#messageInput').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // 发送消息
    $('#messageForm').on('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    // 回车发送消息
    $('#messageInput').on('keypress', function(e) {
        if (e.which === 13 && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 滚动到底部
    scrollToBottom();

    // 开始轮询新消息
    startPolling();
});

function sendMessage() {
    const message = $('#messageInput').val().trim();
    if (!message) return;

    const formData = {
        conversation_id: conversationId,
        message: message,
        _token: $('meta[name="csrf-token"]').attr('content')
    };

    $('#sendButton').prop('disabled', true);

    $.ajax({
        url: '{{ route("user.chat.send") }}',
        method: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                appendMessage(response.message);
                $('#messageInput').val('').css('height', 'auto');
                lastMessageId = response.message.id;
                scrollToBottom();
            }
        },
        error: function() {
            alert('{{ translate("Failed to send message") }}');
        },
        complete: function() {
            $('#sendButton').prop('disabled', false);
            $('#messageInput').focus();
        }
    });
}

function shareCurrentProduct() {
    @if($conversation->product)
    shareProduct({{ $conversation->product->id }});
    @endif
}

function showProductSelector() {
    $('#productSelectorModal').modal('show');
    loadProducts();
}

function loadProducts() {
    $.ajax({
        url: '{{ route("user.chat.get-products") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayProducts(response.products);
            }
        },
        error: function() {
            $('#productList').html('<div class="col-12 text-center"><p class="text-muted">{{ translate("Failed to load products") }}</p></div>');
        }
    });
}

function displayProducts(products) {
    let html = '';
    if (products.length === 0) {
        html = '<div class="col-12 text-center"><p class="text-muted">{{ translate("No products found") }}</p></div>';
    } else {
        products.forEach(function(product) {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="product-selector-item border rounded p-3" onclick="shareProduct(${product.id})">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <img src="${product.thumbnail}" alt="${product.name}"
                                     style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
                            </div>
                            <div class="col">
                                <h6 class="mb-1">${product.name}</h6>
                                <div class="text-primary fw-600">${product.price}</div>
                                <div class="rating rating-sm">
                                    ${generateStarRating(product.rating)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    $('#productList').html(html);
}

function generateStarRating(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="las la-star active"></i>';
        } else {
            stars += '<i class="las la-star"></i>';
        }
    }
    return stars;
}

function shareProduct(productId) {
    $.ajax({
        url: '{{ route("user.chat.share-product") }}',
        method: 'POST',
        data: {
            conversation_id: conversationId,
            product_id: productId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                appendMessage(response.message);
                lastMessageId = response.message.id;
                scrollToBottom();
                $('#productSelectorModal').modal('hide');
            }
        },
        error: function() {
            alert('{{ translate("Failed to share product") }}');
        }
    });
}

function appendMessage(messageData) {
    const messageHtml = createMessageHtml(messageData);
    $('#messagesContainer').append(messageHtml);
}

function createMessageHtml(message) {
    const isOwn = message.is_own;
    const messageClass = isOwn ? 'own' : 'other';
    
    let content = '';
    if (message.message_type === 'product' && message.message_data) {
        const product = message.message_data;
        content = `
            <div class="product-card">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <img src="${product.thumbnail}" alt="${product.name}">
                    </div>
                    <div class="col">
                        <h6 class="mb-1">${product.name}</h6>
                        <div class="text-primary fw-600">${product.price}</div>
                        <a href="${product.url}" class="btn btn-sm btn-outline-primary mt-1" target="_blank">
                            {{ translate('View Product') }}
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else {
        content = message.message;
    }

    return `
        <div class="message ${messageClass}">
            <div class="message-bubble">
                ${content}
                <div class="message-time">${message.created_at}</div>
            </div>
        </div>
    `;
}

function scrollToBottom() {
    const container = $('#messagesContainer');
    container.scrollTop(container[0].scrollHeight);
}

function startPolling() {
    pollingInterval = setInterval(function() {
        checkNewMessages();
    }, 3000); // 每3秒检查一次新消息
}

function checkNewMessages() {
    $.ajax({
        url: '{{ route("user.chat.new-messages") }}',
        method: 'GET',
        data: {
            conversation_id: conversationId,
            last_message_id: lastMessageId
        },
        success: function(response) {
            if (response.success && response.messages.length > 0) {
                response.messages.forEach(function(message) {
                    appendMessage(message);
                    lastMessageId = message.id;
                });
                scrollToBottom();
            }
        }
    });
}

// 页面离开时停止轮询
$(window).on('beforeunload', function() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
});
</script>
@endsection

@extends('frontend.layouts.user_panel')

@section('panel_content')
<div class="aiz-titlebar mt-2 mb-4">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">{{ translate('My Conversations') }}</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 h6">{{ translate('Conversations with Sellers') }}</h5>
            </div>
            <div class="card-body">
                @if($conversations->count() > 0)
                    <div class="conversation-list">
                        @foreach($conversations as $conversation)
                            @php
                                $otherUser = $conversation->getOtherUser(Auth::id());
                                $unreadCount = $conversation->getUnreadCountForUser(Auth::id());
                                $lastMessage = $conversation->latestMessage;
                            @endphp
                            <div class="conversation-item border-bottom py-3 {{ $unreadCount > 0 ? 'unread' : '' }}">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="avatar-container position-relative">
                                            @if($otherUser->user_type == 'seller' && $otherUser->shop && $otherUser->shop->logo)
                                                <img src="{{ uploaded_asset($otherUser->shop->logo) }}" 
                                                     class="rounded-circle" width="50" height="50" 
                                                     alt="{{ $otherUser->shop->name }}">
                                            @else
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <span class="text-white fw-600">{{ substr($otherUser->name, 0, 1) }}</span>
                                                </div>
                                            @endif
                                            @if($unreadCount > 0)
                                                <span class="badge badge-danger position-absolute" 
                                                      style="top: -5px; right: -5px; font-size: 10px;">{{ $unreadCount }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="conversation-info">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 fw-600">
                                                        @if($otherUser->user_type == 'seller' && $otherUser->shop)
                                                            {{ $otherUser->shop->name }}
                                                        @else
                                                            {{ $otherUser->name }}
                                                        @endif
                                                    </h6>
                                                    @if($conversation->product)
                                                        <small class="text-muted">
                                                            <i class="las la-box"></i> {{ $conversation->product->name }}
                                                        </small>
                                                    @endif
                                                </div>
                                                <small class="text-muted">
                                                    {{ $conversation->last_message_at ? $conversation->last_message_at->diffForHumans() : $conversation->updated_at->diffForHumans() }}
                                                </small>
                                            </div>
                                            @if($lastMessage)
                                                <p class="mb-0 text-muted small">
                                                    @if($lastMessage->user_id == Auth::id())
                                                        <span class="text-primary">{{ translate('You') }}:</span>
                                                    @endif
                                                    {{ $lastMessage->getFormattedMessage() }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <a href="{{ route('user.chat.show', encrypt($conversation->id)) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            {{ translate('View') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="aiz-pagination mt-4">
                        {{ $conversations->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="las la-comments" style="font-size: 4rem; color: #ddd;"></i>
                        </div>
                        <h5 class="text-muted">{{ translate('No conversations yet') }}</h5>
                        <p class="text-muted">{{ translate('Start a conversation with a seller by visiting a product page') }}</p>
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            {{ translate('Browse Products') }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('style')
<style>
.conversation-item {
    transition: background-color 0.2s;
}

.conversation-item:hover {
    background-color: #f8f9fa;
}

.conversation-item.unread {
    background-color: #f0f8ff;
}

.conversation-item.unread .conversation-info h6 {
    font-weight: 700;
}

.avatar-container {
    position: relative;
}

.conversation-info {
    min-height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.conversation-list .conversation-item:last-child {
    border-bottom: none !important;
}
</style>
@endsection

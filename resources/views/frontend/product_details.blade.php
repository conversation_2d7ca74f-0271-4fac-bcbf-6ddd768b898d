@extends('frontend.layouts.app')

@section('meta_title'){{ $detailedProduct->meta_title }}@stop

@section('meta_description'){{ $detailedProduct->meta_description }}@stop

@section('meta_keywords'){{ $detailedProduct->tags }}@stop

@section('meta')
<!-- Schema.org markup for Google+ -->
<meta itemprop="name" content="{{ $detailedProduct->meta_title }}">
<meta itemprop="description" content="{{ $detailedProduct->meta_description }}">
<meta itemprop="image" content="{{ uploaded_asset($detailedProduct->meta_img) }}">

<!-- Twitter Card data -->
<meta name="twitter:card" content="product">
<meta name="twitter:site" content="@publisher_handle">
<meta name="twitter:title" content="{{ $detailedProduct->meta_title }}">
<meta name="twitter:description" content="{{ $detailedProduct->meta_description }}">
<meta name="twitter:creator" content="@author_handle">
<meta name="twitter:image" content="{{ uploaded_asset($detailedProduct->meta_img) }}">
<meta name="twitter:data1" content="{{ single_price($detailedProduct->unit_price) }}">
<meta name="twitter:label1" content="Price">

<!-- Open Graph data -->
<meta property="og:title" content="{{ $detailedProduct->meta_title }}" />
<meta property="og:type" content="og:product" />
<meta property="og:url" content="{{ route('product', $detailedProduct->slug) }}" />
<meta property="og:image" content="{{ uploaded_asset($detailedProduct->meta_img) }}" />
<meta property="og:description" content="{{ $detailedProduct->meta_description }}" />
<meta property="og:site_name" content="{{ get_setting('meta_title') }}" />
<meta property="og:price:amount" content="{{ single_price($detailedProduct->unit_price) }}" />
<meta property="product:price:currency" content="{{ \App\Models\Currency::findOrFail(get_setting('system_default_currency'))->code }}" />
<meta property="fb:app_id" content="{{ env('FACEBOOK_PIXEL_ID') }}">
@endsection

@section('content')
<section class="mb-4 pt-3">
    <div class="container">
        <div class="bg-white shadow-sm rounded p-3">
            <div class="row">
                <div class="col-xl-5 col-lg-6 mb-4">
                    <div class="sticky-top z-3 row gutters-10">
                        @php
                        $photos = explode(',', $detailedProduct->photos);
                        @endphp
                        <div class="col order-1 order-md-2">
                            <div class="aiz-carousel product-gallery" data-nav-for='.product-gallery-thumb' data-fade='true' data-auto-height='true'>
                                @foreach ($photos as $key => $photo)
                                <div class="carousel-box img-zoom rounded">
                                    <img class="img-fluid lazyload" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($photo) }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                </div>
                                @endforeach
                                @foreach ($detailedProduct->stocks as $key => $stock)
                                @if ($stock->image != null)
                                <div class="carousel-box img-zoom rounded">
                                    <img class="img-fluid lazyload" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($stock->image) }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                        <div class="col-12 col-md-auto w-md-80px order-2 order-md-1 mt-3 mt-md-0">
                            <div class="aiz-carousel product-gallery-thumb" data-items='5' data-nav-for='.product-gallery' data-vertical='true' data-vertical-sm='false' data-focus-select='true' data-arrows='true'>
                                @foreach ($photos as $key => $photo)
                                <div class="carousel-box c-pointer border p-1 rounded">
                                    <img class="lazyload mw-100 size-50px mx-auto" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($photo) }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                </div>
                                @endforeach
                                @foreach ($detailedProduct->stocks as $key => $stock)
                                @if ($stock->image != null)
                                <div class="carousel-box c-pointer border p-1 rounded" data-variation="{{ $stock->variant }}">
                                    <img class="lazyload mw-100 size-50px mx-auto" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($stock->image) }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-7 col-lg-6">
                    <div class="text-left">
                        <h1 class="mb-2 fs-20 fw-600">
                            {{ $detailedProduct->getTranslation('name') }}
                        </h1>

                        <div class="row align-items-center">
                            <div class="col-12">
                                @php
                                $total = 0;
                                $total += $detailedProduct->reviews->count();
                                @endphp
                                <span class="rating">
                                    {{ renderStarRating($detailedProduct->rating) }}
                                </span>
                                <span class="ml-1 opacity-50">({{ $total }}
                                    {{ translate('reviews') }})</span>
                            </div>
                            @if ($detailedProduct->est_shipping_days)
                            <div class="col-auto ml">
                                <small class="mr-2 opacity-50">{{ translate('Estimate Shipping Time') }}:
                                </small>{{ $detailedProduct->est_shipping_days }} {{ translate('Days') }}
                            </div>
                            @endif
                        </div>
                        <hr>

                        <div class="row align-items-center">
                            <div class="col-auto">
                                <small class="mr-2 opacity-50">{{ translate('Sold by') }}: </small><br>
                                @if ($detailedProduct->added_by == 'seller' && get_setting('vendor_system_activation') == 1)
                                <a href="{{ route('shop.visit', $detailedProduct->user->shop->slug) }}" class="text-reset">{{ $detailedProduct->user->shop->name }}</a>
                                @else
                                {{ translate('Inhouse product') }}
                                @endif
                            </div>
                            @if (get_setting('conversation_system') == 1 && $detailedProduct->added_by == 'seller')
                            <div class="col-auto">
                                <button class="btn btn-sm btn-primary" onclick="show_chat_modal()">
                                    <i class="las la-comments mr-1"></i>{{ translate('Chat with Seller') }}
                                </button>
                            </div>
                            @endif


                            @if ($detailedProduct->user->shop && $detailedProduct->user->shop->online_ervice)
                            <a target="_blank" href="{{$detailedProduct->user->shop->online_ervice}}" class="col-auto" style="padding-right: 0px;">
                                <button class="btn btn-sm btn-soft-primary">{{ translate('Online Service') }}</button>
                            </a>
                            @endif

                            @if ($detailedProduct->brand != null)
                            <div class="col-auto">
                                <a href="{{ route('products.brand', $detailedProduct->brand->slug) }}">
                                    <img src="{{ uploaded_asset($detailedProduct->brand->logo) }}" alt="{{ $detailedProduct->brand->getTranslation('name') }}" height="30">
                                </a>
                            </div>
                            @endif
                        </div>

                        <hr>

                        @if ($detailedProduct->wholesale_product)
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>{{ translate('Min Qty') }}</th>
                                    <th>{{ translate('Max Qty') }}</th>
                                    <th>{{ translate('Unit Price') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($detailedProduct->stocks->first()->wholesalePrices as $wholesalePrice)
                                <tr>
                                    <td>{{ $wholesalePrice->min_qty }}</td>
                                    <td>{{ $wholesalePrice->max_qty }}</td>
                                    <td>{{ single_price($wholesalePrice->price) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @else
                        @if (home_price($detailedProduct) != home_discounted_price($detailedProduct))
                        <div class="row no-gutters mt-3">
                            <div class="col-sm-2">
                                <div class="opacity-50 my-2">{{ translate('Price') }}:</div>
                            </div>
                            <div class="col-sm-10">
                                <div class="fs-20 opacity-60">
                                    <del>
                                        {{ home_price($detailedProduct) }}
                                        @if ($detailedProduct->unit != null)
                                        <!-- <span>/{{ $detailedProduct->getTranslation('unit') }}</span> -->
                                        <span>/unit</span>
                                        @endif
                                    </del>
                                </div>
                            </div>
                        </div>

                        <div class="row no-gutters my-2">
                            <div class="col-sm-2">
                                <div class="opacity-50">{{ translate('Discount Price') }}:</div>
                            </div>
                            <div class="col-sm-10">
                                <div class="">
                                    <strong class="h2 fw-600 text-primary">
                                        {{ home_discounted_price($detailedProduct) }}
                                    </strong>
                                    @if ($detailedProduct->unit != null)
                                    <!-- <span class="opacity-70">/{{ $detailedProduct->getTranslation('unit') }}</span> -->
                                    <span class="opacity-70">/unit</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @else
                        <div class="row no-gutters mt-3">
                            <div class="col-sm-2">
                                <div class="opacity-50 my-2">{{ translate('Price') }}:</div>
                            </div>
                            <div class="col-sm-10">
                                <div class="">
                                    <strong class="h2 fw-600 text-primary">
                                        {{ home_discounted_price($detailedProduct) }}
                                    </strong>
                                    @if ($detailedProduct->unit != null)
                                    <!--<span class="opacity-70">/{{ $detailedProduct->getTranslation('unit') }}</span>-->
                                    <span class="opacity-70" style="display:inline;">/unit</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                        @endif

                        @if (addon_is_activated('club_point') && $detailedProduct->earn_point > 0)
                        <div class="row no-gutters mt-4">
                            <div class="col-sm-2">
                                <div class="opacity-50 my-2">{{ translate('Club Point') }}:</div>
                            </div>
                            <div class="col-sm-10">
                                <div class="d-inline-block rounded px-2 bg-soft-primary border-soft-primary border">
                                    <span class="strong-700">{{ $detailedProduct->earn_point }}</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        <hr>

                        <form id="option-choice-form">
                            @csrf
                            <input type="hidden" name="id" value="{{ $detailedProduct->id }}">

                            @if ($detailedProduct->choice_options != null)
                            @foreach (json_decode($detailedProduct->choice_options) as $key => $choice)
                            <div class="row no-gutters">
                                <div class="col-sm-2">
                                    <div class="opacity-50 my-2">
                                        {{ \App\Models\Attribute::find($choice->attribute_id)->getTranslation('name') }}:
                                    </div>
                                </div>
                                <div class="col-sm-10">
                                    <div class="aiz-radio-inline">
                                        @foreach ($choice->values as $key => $value)
                                        <label class="aiz-megabox pl-0 mr-2">
                                            <input type="radio" name="attribute_id_{{ $choice->attribute_id }}" value="{{ $value }}" @if ($key==0) checked @endif>
                                            <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center py-2 px-3 mb-2">
                                                {{ $value }}
                                            </span>
                                        </label>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endforeach
                            @endif

                            @if (count(json_decode($detailedProduct->colors)) > 0)
                            <div class="row no-gutters">
                                <div class="col-sm-2">
                                    <div class="opacity-50 my-2">{{ translate('Color') }}:</div>
                                </div>
                                <div class="col-sm-10">
                                    <div class="aiz-radio-inline">
                                        @foreach (json_decode($detailedProduct->colors) as $key => $color)
                                        <label class="aiz-megabox pl-0 mr-2" data-toggle="tooltip" data-title="{{ \App\Models\Color::where('code', $color)->first()->name }}">
                                            <input type="radio" name="color" value="{{ \App\Models\Color::where('code', $color)->first()->name }}" @if ($key==0) checked @endif>
                                            <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center p-1 mb-2">
                                                <span class="size-30px d-inline-block rounded" style="background: {{ $color }};"></span>
                                            </span>
                                        </label>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <hr>
                            @endif

                            <!-- Quantity + Add to cart -->
                            <div class="row no-gutters">
                                <div class="col-sm-2">
                                    <div class="opacity-50 my-2">{{ translate('Quantity') }}:</div>
                                </div>
                                <div class="col-sm-10">
                                    <div class="product-quantity d-flex align-items-center">
                                        @php
                                            $qty = 0;
                                            if($detailedProduct->stocks->first()) {
                                                $qty = $detailedProduct->stocks->first()->qty;
                                            }
                                        @endphp
                                        <div class="row no-gutters align-items-center aiz-plus-minus mr-3" style="width: 130px;">
                                            <button class="btn col-auto btn-icon btn-sm btn-circle btn-light" type="button" data-type="minus" data-field="quantity" disabled="">
                                                <i class="las la-minus"></i>
                                            </button>
                                            <input type="number" name="quantity" class="col border-0 text-center flex-grow-1 fs-16 input-number" placeholder="1" value="{{ $detailedProduct->min_qty }}" min="{{ $detailedProduct->min_qty }}" max="{{ $qty }}" lang="en">
                                            <button class="btn  col-auto btn-icon btn-sm btn-circle btn-light" type="button" data-type="plus" data-field="quantity">
                                                <i class="las la-plus"></i>
                                            </button>
                                        </div>
                                        <div class="avialable-amount opacity-60">
                                            @if ($detailedProduct->stock_visibility_state == 'quantity')
                                            (<span id="available-quantity">{{ $qty }}</span>
                                            {{ translate('available') }})
                                            @elseif($detailedProduct->stock_visibility_state == 'text' && $qty >= 1)
                                            (<span id="available-quantity">{{ translate('In Stock') }}</span>)
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row no-gutters pb-3" id="chosen_price_div">
                                <div class="col-sm-2">
                                    <div class="opacity-50 my-2">{{ translate('Total Price') }}:</div>
                                </div>
                                <div class="col-sm-10">
                                    <div class="product-price">
                                        <strong id="chosen_price" class="h4 fw-600 text-primary">

                                        </strong>
                                    </div>
                                </div>
                            </div>

                        </form>

                        <div class="mt-3">
                            @if ($detailedProduct->external_link != null)
                            <a type="button" class="btn btn-primary buy-now fw-600" href="{{ $detailedProduct->external_link }}">
                                <i class="la la-share"></i> {{ translate($detailedProduct->external_link_btn) }}
                            </a>
                            @else
                            <button type="button" class="btn btn-soft-primary mr-2 add-to-cart fw-600" onclick="addToCart()">
                                <i class="las la-shopping-bag"></i>
                                <span class="d-none d-md-inline-block"> {{ translate('Add to cart') }}</span>
                            </button>
                            <button type="button" class="btn btn-primary buy-now fw-600" onclick="buyNow()">
                                <i class="la la-shopping-cart"></i> {{ translate('Buy Now') }}
                            </button>
                            @endif
                            <button type="button" class="btn btn-secondary out-of-stock fw-600 d-none" disabled>
                                <i class="la la-cart-arrow-down"></i> {{ translate('Out of Stock') }}
                            </button>
                        </div>

                        <!-- 聊天按钮 -->
                        @if (get_setting('conversation_system') == 1 && $detailedProduct->added_by == 'seller')
                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-block fw-600" onclick="show_chat_modal()">
                                <i class="las la-comments mr-2"></i>{{ translate('Ask Seller a Question') }}
                            </button>
                            <small class="text-muted d-block text-center mt-1">
                                {{ translate('Get instant answers about this product') }}
                            </small>
                        </div>
                        @endif



                        <div class="d-table width-100 mt-3">
                            <div class="d-table-cell">
                                <!-- Add to wishlist button -->
                                <button type="button" class="btn pl-0 btn-link fw-600" onclick="addToWishList({{ $detailedProduct->id }})">
                                    {{ translate('Add to wishlist') }}
                                </button>
                                <!-- Add to compare button -->
                                <button type="button" class="btn btn-link btn-icon-left fw-600" onclick="addToCompare({{ $detailedProduct->id }})">
                                    {{ translate('Add to compare') }}
                                </button>
                                @if (Auth::check() && addon_is_activated('affiliate_system') && (\App\Models\AffiliateOption::where('type', 'product_sharing')->first()->status || \App\Models\AffiliateOption::where('type', 'category_wise_affiliate')->first()->status) && Auth::user()->affiliate_user != null && Auth::user()->affiliate_user->status)
                                @php
                                if (Auth::check()) {
                                if (Auth::user()->referral_code == null) {
                                Auth::user()->referral_code = substr(Auth::user()->id . Str::random(10), 0, 10);
                                Auth::user()->save();
                                }
                                $referral_code = Auth::user()->referral_code;
                                $referral_code_url = URL::to('/product') . '/' . $detailedProduct->slug . "?product_referral_code=$referral_code";
                                }
                                @endphp
                                <div>
                                    <button type=button id="ref-cpurl-btn" class="btn btn-sm btn-secondary" data-attrcpy="{{ translate('Copied') }}" onclick="CopyToClipboard(this)" data-url="{{ $referral_code_url }}">{{ translate('Copy the Promote Link') }}</button>
                                </div>
                                @endif
                            </div>
                        </div>


                        @php
                        $refund_sticker = get_setting('refund_sticker');
                        @endphp
                        @if (addon_is_activated('refund_request'))
                        <div class="row no-gutters mt-3">
                            <div class="col-2">
                                <div class="opacity-50 mt-2">{{ translate('Refund') }}:</div>
                            </div>
                            <div class="col-10">
                                <a href="{{ route('returnpolicy') }}" target="_blank">
                                    @if ($refund_sticker != null)
                                    <img src="{{ uploaded_asset($refund_sticker) }}" height="36">
                                    @else
                                    <img src="{{ static_asset('assets/img/refund-sticker.jpg') }}" height="36">
                                    @endif
                                </a>
                                <!--<a href="{{ route('returnpolicy') }}" class="ml-2" target="_blank">{{ translate('View Policy') }}</a>-->
                            </div>
                        </div>
                        @endif
                        <div class="row no-gutters mt-4">
                            <div class="col-sm-2">
                                <div class="opacity-50 my-2">{{ translate('Share') }}:</div>
                            </div>
                            <div class="col-sm-10">
                                <div class="aiz-share"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="mb-4">
    <div class="container">
        <div class="row gutters-10">
            <div class="col-xl-3 order-1 order-xl-0">
                @if ($detailedProduct->added_by == 'seller' && $detailedProduct->user->shop != null)
                <div class="bg-white shadow-sm mb-3">
                    <div class="position-relative p-3 text-left">
                        @if ($detailedProduct->user->shop->verification_status)
                        <div class="absolute-top-right p-2 bg-white z-1">
                            <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" viewBox="0 0 287.5 442.2" width="22" height="34">
                                <polygon style="fill:#F8B517;" points="223.4,442.2 143.8,376.7 64.1,442.2 64.1,215.3 223.4,215.3 " />
                                <circle style="fill:#FBD303;" cx="143.8" cy="143.8" r="143.8" />
                                <circle style="fill:#F8B517;" cx="143.8" cy="143.8" r="93.6" />
                                <polygon style="fill:#FCFCFD;" points="143.8,55.9 163.4,116.6 227.5,116.6 175.6,154.3 195.6,215.3 143.8,177.7 91.9,215.3 111.9,154.3
                                            60,116.6 124.1,116.6 " />
                            </svg>
                        </div>
                        @endif
                        <div class="opacity-50 fs-12 border-bottom">{{ translate('Sold by') }}</div>



                        <div class="d-block text-center my-3">
                            @if($detailedProduct->user->shop && $detailedProduct->user->shop->logo )
                            <img class="mw-100 mb-3 brand-icon" style="width: 84px;height: 84px;" src="{{ uploaded_asset($detailedProduct->user->shop->logo) }}" alt="{{ get_setting('site_name') }}">
                            @else
                            <img class="mw-100 mb-3 brand-icon" style="width: 84px;height: 84px;" src="/public/assets/img/placeholder.jpg" alt="{{ get_setting('site_name') }}">
                            @endif
                        </div>




                        <a style="text-align: center;" href="{{ route('shop.visit', $detailedProduct->user->shop->slug) }}" class="text-reset d-block fw-600">
                            {{ $detailedProduct->user->shop->name }}
                            @if ($detailedProduct->user->shop->verification_status == 1)
                            <span class="ml-2"><i class="fa fa-check-circle" style="color:green"></i></span>
                            @else
                            <span class="ml-2"><i class="fa fa-times-circle" style="color:red"></i></span>
                            @endif
                        </a>



                        <div style="text-align: center;" class="location opacity-70">{{ $detailedProduct->user->shop->address }}</div>
                        <div class="text-center border rounded p-2 mt-3">
                            <div class="rating">
                                @if ($total > 0)
                                {{ renderStarRating($detailedProduct->user->shop->rating) }}
                                @else
                                {{ renderStarRating(0) }}
                                @endif
                            </div>
                            <div class="opacity-60 fs-12">({{ count($lists) }}
                                {{ translate('customer reviews') }})
                            </div>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center border-top">
                        <div class="col">
                            <a href="{{ route('shop.visit', $detailedProduct->user->shop->slug) }}" class="d-block btn btn-soft-primary rounded-0">{{ translate('Visit Store') }}</a>
                        </div>
                        <div class="col">
                            <ul class="social list-inline mb-0">
                                <li class="list-inline-item mr-0">
                                    <a href="{{ $detailedProduct->user->shop->facebook }}" class="facebook" target="_blank">
                                        <i class="lab la-facebook-f opacity-60"></i>
                                    </a>
                                </li>
                                <li class="list-inline-item mr-0">
                                    <a href="{{ $detailedProduct->user->shop->google }}" class="google" target="_blank">
                                        <i class="lab la-google opacity-60"></i>
                                    </a>
                                </li>
                                <li class="list-inline-item mr-0">
                                    <a href="{{ $detailedProduct->user->shop->twitter }}" class="twitter" target="_blank">
                                        <i class="lab la-twitter opacity-60"></i>
                                    </a>
                                </li>
                                <li class="list-inline-item">
                                    <a href="{{ $detailedProduct->user->shop->youtube }}" class="youtube" target="_blank">
                                        <i class="lab la-youtube opacity-60"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                @endif
                <div class="bg-white rounded shadow-sm mb-3">
                    <div class="p-3 border-bottom fs-16 fw-600">
                        {{ translate('Top Selling Products') }}
                    </div>
                    <div class="p-3">
                        <ul class="list-group list-group-flush">
                            @foreach (filter_products(\App\Models\Product::where('user_id', $detailedProduct->user_id)->orderBy('num_of_sale', 'desc'))->limit(6)->get()
                            as $key => $top_product)
                            <li class="py-3 px-0 list-group-item border-light">
                                <div class="row gutters-10 align-items-center">
                                    <div class="col-5">
                                        <a href="{{ route('product', $top_product->slug) }}" class="d-block text-reset">
                                            <img class="img-fit lazyload h-xxl-110px h-xl-80px h-120px" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($top_product->thumbnail_img) }}" alt="{{ $top_product->getTranslation('name') }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                        </a>
                                    </div>
                                    <div class="col-7 text-left">
                                        <h4 class="fs-13 text-truncate-2">
                                            <a href="{{ route('product', $top_product->slug) }}" class="d-block text-reset">{{ $top_product->getTranslation('name') }}</a>
                                        </h4>
                                        <div class="rating rating-sm mt-1">
                                            {{ renderStarRating($top_product->rating) }}
                                        </div>
                                        <div class="mt-2">
                                            <span class="fs-17 fw-600 text-primary">{{ home_discounted_base_price($top_product) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xl-9 order-0 order-xl-1">
                <div class="bg-white mb-3 shadow-sm rounded">
                    <div class="nav border-bottom aiz-nav-tabs">
                        <a href="#tab_default_1" data-toggle="tab" class="p-3 fs-16 fw-600 text-reset active show">{{ translate('Description') }}</a>
                        @if ($detailedProduct->video_link != null)
                        <a href="#tab_default_2" data-toggle="tab" class="p-3 fs-16 fw-600 text-reset">{{ translate('Video') }}</a>
                        @endif
                        @if ($detailedProduct->pdf != null)
                        <a href="#tab_default_3" data-toggle="tab" class="p-3 fs-16 fw-600 text-reset">{{ translate('Downloads') }}</a>
                        @endif
                        <a href="#tab_default_4" data-toggle="tab" class="p-3 fs-16 fw-600 text-reset">{{ translate('Reviews') }}</a>
                    </div>

                    <div class="tab-content pt-0">
                        <div class="tab-pane fade active show" id="tab_default_1">
                            <div class="p-4">
                                <div class="mw-100 overflow-hidden text-left aiz-editor-data">
                                    <?php echo $detailedProduct->getTranslation('description'); ?>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="tab_default_2">
                            <div class="p-4">
                                <div class="embed-responsive embed-responsive-16by9">
                                    @if ($detailedProduct->video_provider == 'youtube' && isset(explode('=', $detailedProduct->video_link)[1]))
                                    <iframe class="embed-responsive-item" src="https://www.youtube.com/embed/{{ get_url_params($detailedProduct->video_link, 'v') }}"></iframe>
                                    @elseif ($detailedProduct->video_provider == 'dailymotion' && isset(explode('video/', $detailedProduct->video_link)[1]))
                                    <iframe class="embed-responsive-item" src="https://www.dailymotion.com/embed/video/{{ explode('video/', $detailedProduct->video_link)[1] }}"></iframe>
                                    @elseif ($detailedProduct->video_provider == 'vimeo' && isset(explode('vimeo.com/', $detailedProduct->video_link)[1]))
                                    <iframe src="https://player.vimeo.com/video/{{ explode('vimeo.com/', $detailedProduct->video_link)[1] }}" width="500" height="281" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab_default_3">
                            <div class="p-4 text-center ">
                                <a href="{{ uploaded_asset($detailedProduct->pdf) }}" class="btn btn-primary">{{ translate('Download') }}</a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab_default_4">
                            <div class="p-4">
                                <ul class="list-group list-group-flush">





                                    @foreach ($lists as $key => $review)

                                    <li class="media list-group-item d-flex">
                                        <span class="avatar avatar-md mr-3">
                                            @if ($review['author_portrait']!='')
                                            <img class="lazyload" src="https://s-cf-tw.shopeesz.com/file/<?= $review['author_portrait'] ?>">

                                            @else

                                            <img class="lazyload" src="https://xiapi.xiapibuy.com/deo/shopee/shopee-mobilemall-live-sg/f88abc3256846ba9a4af91621bc3f9c4.png">
                                            @endif

                                        </span>
                                        <div class="media-body text-left">
                                            <div class="d-flex justify-content-between">
                                                <h3 class="fs-15 fw-600 mb-0">
                                                    <?= $review['author_username'] ?>
                                                </h3>
                                                <span class="rating rating-sm">
                                                    @for ($i = 0; $i < $review['rating_star']; $i++) <i class="las la-star active"></i>
                                                        @endfor
                                                        @for ($i = 0; $i < 5 - $review['rating_star']; $i++) <i class="las la-star"></i>
                                                            @endfor
                                                </span>
                                            </div>
                                            <div class="opacity-60 mb-2">
                                                <?= $review['comment'] ?></div>
                                            <p class="comment-text">
                                                <?= date('Y-m-d H:i:s', $review['mtime']) ?>
                                            </p>
                                        </div>
                                    </li>

                                    @endforeach








                                    @foreach ($detailedProduct->reviews as $key => $review)
                                    @if ($review->user != null)
                                    <li class="media list-group-item d-flex">
                                        <span class="avatar avatar-md mr-3">
                                            <img class="lazyload" src="{{ static_asset('assets/img/placeholder.jpg') }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';" @if ($review->user->avatar_original != null) data-src="{{ uploaded_asset($review->user->avatar_original) }}"
                                            @else
                                            data-src="{{ static_asset('assets/img/placeholder.jpg') }}" @endif>
                                        </span>
                                        <div class="media-body text-left">
                                            <div class="d-flex justify-content-between">
                                                <h3 class="fs-15 fw-600 mb-0">{{ $review->user->name }}
                                                </h3>
                                                <span class="rating rating-sm">
                                                    @for ($i = 0; $i < $review->rating; $i++)
                                                        <i class="las la-star active"></i>
                                                        @endfor
                                                        @for ($i = 0; $i < 5 - $review->rating; $i++)
                                                            <i class="las la-star"></i>
                                                            @endfor
                                                </span>
                                            </div>
                                            <div class="opacity-60 mb-2">
                                                {{ date('d-m-Y', strtotime($review->created_at)) }}
                                            </div>
                                            <p class="comment-text">
                                                {{ $review->comment }}
                                            </p>
                                        </div>
                                    </li>
                                    @endif
                                    @endforeach
                                </ul>

                                @if (count($detailedProduct->reviews) <= 0) 
                                <div class="text-center fs-18 opacity-70">
                                    {{ translate('There have been no reviews for this product yet.') }}
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded shadow-sm">
                <div class="border-bottom p-3">
                    <h3 class="fs-16 fw-600 mb-0">
                        <span class="mr-4">{{ translate('Related products') }}</span>
                    </h3>
                </div>
                <div class="p-3">
                    <div class="aiz-carousel gutters-5 half-outside-arrow" data-items="5" data-xl-items="3" data-lg-items="4" data-md-items="3" data-sm-items="2" data-xs-items="2" data-arrows='true' data-infinite='true'>
                        @foreach (filter_products(\App\Models\Product::where('category_id', $detailedProduct->category_id)->where('id', '!=', $detailedProduct->id))->limit(10)->get()
                        as $key => $related_product)
                        <div class="carousel-box">
                            <div class="aiz-card-box border border-light rounded hov-shadow-md my-2 has-transition">
                                <div class="">
                                    <a href="{{ route('product', $related_product->slug) }}" class="d-block">
                                        <img class="img-fit lazyload mx-auto h-140px h-md-210px" src="{{ static_asset('assets/img/placeholder.jpg') }}" data-src="{{ uploaded_asset($related_product->thumbnail_img) }}" alt="{{ $related_product->getTranslation('name') }}" onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                    </a>
                                </div>
                                <div class="p-md-3 p-2 text-left">
                                    <div class="fs-15">
                                        @if (home_base_price($related_product) != home_discounted_base_price($related_product))
                                        <del class="fw-600 opacity-50 mr-1">{{ home_base_price($related_product) }}</del>
                                        @endif
                                        <span class="fw-700 text-primary">{{ home_discounted_base_price($related_product) }}</span>
                                    </div>
                                    <div class="rating rating-sm mt-1">
                                        {{ renderStarRating($related_product->rating) }}
                                    </div>
                                    <h3 class="fw-600 fs-13 text-truncate-2 lh-1-4 mb-0 h-35px">
                                        <a href="{{ route('product', $related_product->slug) }}" class="d-block text-reset">{{ $related_product->getTranslation('name') }}</a>
                                    </h3>
                                    @if (addon_is_activated('club_point'))
                                    <div class="rounded px-2 mt-2 bg-soft-primary border-soft-primary border">
                                        {{ translate('Club Point') }}:
                                        <span class="fw-700 float-right">{{ $related_product->earn_point }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            {{-- Product Query --}}
            @if(get_setting('product_query_activation') == 1)
            <div class="bg-white rounded shadow-sm mt-3">
                <div class="border-bottom p-3">
                    <h3 class="fs-18 fw-600 mb-0">
                        <span>{{ translate(' Product Queries ') }} ({{ $total_query }})</span>
                    </h3>
                </div>
                @guest
                <p class="fs-14 fw-400 mb-0 ml-3 mt-2"><a href="{{ route('user.login') }}">{{ translate('Login') }}</a> or <a class="mr-1" href="{{ route('user.registration') }}">{{ translate('Register ') }}</a>{{ translate(' to submit your questions to seller') }}
                </p>
                @endguest

                @auth
                <div class="query form p-3">
                    @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                    <form action="{{ route('product-queries.store') }}" method="POST">
                        @csrf
                        <input type="hidden" name="product" value="{{ $detailedProduct->id }}">
                        <div class="form-group">
                            <textarea class="form-control" rows="3" cols="40" name="question" placeholder="Write your question here..." style="resize: none;"></textarea>

                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
                @php
                $own_product_queries = Auth::user()->product_queries->where('product_id',$detailedProduct->id);
                @endphp
                @if ($own_product_queries->count() > 0)

                <div class="question-area my-4   mb-0 ml-3">

                    <div class="border-bottom py-3">
                        <h3 class="fs-18 fw-600 mb-0">
                            <span class="mr-4">{{ translate('My Questions') }}</span>
                        </h3>
                    </div>
                    @foreach ($own_product_queries as $product_query)
                    <div class="produc-queries border-bottom">
                        <div class="query d-flex my-4">
                            <span class="mt-1"><svg xmlns="http://www.w3.org/2000/svg" width="24.994" height="24.981" viewBox="0 0 24.994 24.981">
                                    <g id="Group_23909" data-name="Group 23909" transform="translate(18392.496 11044.037)">
                                        <path id="Subtraction_90" data-name="Subtraction 90" d="M1830.569-117.742a.4.4,0,0,1-.158-.035.423.423,0,0,1-.252-.446c0-.84,0-1.692,0-2.516v-2.2a5.481,5.481,0,0,1-2.391-.745,5.331,5.331,0,0,1-2.749-4.711c-.034-2.365-.018-4.769,0-7.094l0-.649a5.539,5.539,0,0,1,4.694-5.513,5.842,5.842,0,0,1,.921-.065q3.865,0,7.73,0l5.035,0a5.539,5.539,0,0,1,5.591,5.57c.01,2.577.01,5.166,0,7.693a5.54,5.54,0,0,1-4.842,5.506,6.5,6.5,0,0,1-.823.046l-3.225,0c-1.454,0-2.753,0-3.97,0a.555.555,0,0,0-.435.182c-1.205,1.214-2.435,2.445-3.623,3.636l-.062.062-1.005,1.007-.037.037-.069.069A.464.464,0,0,1,1830.569-117.742Zm7.37-11.235h0l1.914,1.521.817-.754-1.621-1.273a3.517,3.517,0,0,0,1.172-1.487,5.633,5.633,0,0,0,.418-2.267v-.58a5.629,5.629,0,0,0-.448-2.323,3.443,3.443,0,0,0-1.282-1.525,3.538,3.538,0,0,0-1.93-.53,3.473,3.473,0,0,0-1.905.534,3.482,3.482,0,0,0-1.288,1.537,5.582,5.582,0,0,0-.454,2.314v.654a5.405,5.405,0,0,0,.471,2.261,3.492,3.492,0,0,0,1.287,1.5,3.492,3.492,0,0,0,1.9.527,3.911,3.911,0,0,0,.947-.112Zm-.948-.9a2.122,2.122,0,0,1-1.812-.9,4.125,4.125,0,0,1-.652-2.457v-.667a4.008,4.008,0,0,1,.671-2.4,2.118,2.118,0,0,1,1.78-.863,2.138,2.138,0,0,1,1.824.869,4.145,4.145,0,0,1,.639,2.473v.673a4.07,4.07,0,0,1-.655,2.423A2.125,2.125,0,0,1,1836.991-129.881Z" transform="translate(-20217 -10901.814)" fill="#e62e04" stroke="rgba(0,0,0,0)" stroke-miterlimit="10" stroke-width="1" />
                                    </g>
                                </svg></span>

                            <div class="ml-3">
                                <div class="fs-14">{{ strip_tags($product_query->question) }}</div>
                                <span class="text-secondary">{{ $product_query->user->name }} </span>
                            </div>
                        </div>
                        <div class="answer d-flex my-4">
                            <span class="mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="24.99" height="24.98" viewBox="0 0 24.99 24.98">
                                    <g id="Group_23908" data-name="Group 23908" transform="translate(17952.169 11072.5)">
                                        <path id="Subtraction_89" data-name="Subtraction 89" d="M2162.9-146.2a.4.4,0,0,1-.159-.035.423.423,0,0,1-.251-.446q0-.979,0-1.958V-151.4a5.478,5.478,0,0,1-2.39-.744,5.335,5.335,0,0,1-2.75-4.712c-.034-2.355-.018-4.75,0-7.065l0-.678a5.54,5.54,0,0,1,4.7-5.513,5.639,5.639,0,0,1,.92-.064c2.527,0,5.029,0,7.437,0l5.329,0a5.538,5.538,0,0,1,5.591,5.57c.01,2.708.01,5.224,0,7.692a5.539,5.539,0,0,1-4.843,5.506,6,6,0,0,1-.822.046l-3.234,0c-1.358,0-2.691,0-3.96,0a.556.556,0,0,0-.436.182c-1.173,1.182-2.357,2.367-3.5,3.514l-1.189,1.192-.047.048-.058.059A.462.462,0,0,1,2162.9-146.2Zm5.115-12.835h3.559l.812,2.223h1.149l-3.25-8.494h-.98l-3.244,8.494h1.155l.8-2.222Zm3.226-.915h-2.888l1.441-3.974,1.447,3.972Z" transform="translate(-20109 -10901.815)" fill="#f7941d" stroke="rgba(0,0,0,0)" stroke-miterlimit="10" stroke-width="1" />
                                    </g>
                                </svg></span>

                            <div class="ml-3">
                                <div class="fs-14">
                                    {{ strip_tags($product_query->reply ? $product_query->reply : translate('Seller did not respond yet')) }}
                                </div>
                                <span class=" text-secondary">
                                    {{ $product_query->product->user->name }} </span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                @endif
                @endauth

                <div class="pagination-area my-4 mb-0 ml-3">
                    @include('frontend.partials.product_query_pagination')
                </div>
            </div>
            @endif
            {{-- End of Product Query --}}
        </div>
    </div>
    </div>
</section>

@endsection

@section('modal')
<div class="modal fade" id="chat_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom product-modal" id="modal-size" role="document" style="max-height: 90vh; margin: 1.75rem auto;">
        <div class="modal-content position-relative" style="max-height: 85vh; display: flex; flex-direction: column;">
            <div class="modal-header">
                <h5 class="modal-title fw-600 h5">
                    <i class="las la-comments mr-2"></i>
                    {{ translate('Chat with Seller') }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form class="" action="{{ route('user.chat.create-from-product') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="product_id" value="{{ $detailedProduct->id }}">
                <div class="modal-body gry-bg px-3 pt-3" style="max-height: 50vh; overflow-y: auto; flex: 1;">
                    <!-- 卖家信息 -->
                    <div class="seller-info mb-3 p-3 bg-white rounded">
                        <div class="d-flex align-items-center">
                            @if($detailedProduct->user->shop && $detailedProduct->user->shop->logo)
                                <img src="{{ uploaded_asset($detailedProduct->user->shop->logo) }}"
                                     class="rounded-circle mr-3" width="50" height="50"
                                     alt="{{ $detailedProduct->user->shop->name }}">
                            @else
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center mr-3"
                                     style="width: 50px; height: 50px;">
                                    <span class="text-white fw-600">
                                        {{ substr($detailedProduct->user->name, 0, 1) }}
                                    </span>
                                </div>
                            @endif
                            <div>
                                <h6 class="mb-1">
                                    @if($detailedProduct->user->shop)
                                        {{ $detailedProduct->user->shop->name }}
                                    @else
                                        {{ $detailedProduct->user->name }}
                                    @endif
                                </h6>
                                <small class="text-muted">
                                    <i class="las la-box mr-1"></i>{{ $detailedProduct->name }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 产品预览 -->
                    <div class="product-preview mb-3 p-3 bg-white rounded">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <img src="{{ uploaded_asset($detailedProduct->thumbnail_img) }}"
                                     class="rounded" width="60" height="60"
                                     style="object-fit: cover;"
                                     alt="{{ $detailedProduct->name }}">
                            </div>
                            <div class="col">
                                <h6 class="mb-1">{{ $detailedProduct->name }}</h6>
                                <div class="text-primary fw-600">{{ home_discounted_price($detailedProduct) }}</div>
                                <small class="text-muted">
                                    <i class="las la-info-circle mr-1"></i>{{ translate('This product will be shared in the conversation') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 消息输入 -->
                    <div class="form-group">
                        <label class="form-label">{{ translate('Your Message') }}</label>
                        <textarea class="form-control" rows="4" name="message" required
                                  placeholder="{{ translate('Hi! I\'m interested in this product. Can you tell me more about it?') }}"></textarea>
                        <small class="form-text text-muted">
                            {{ translate('Start a conversation with the seller about this product') }}
                        </small>
                    </div>
                </div>
                <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding: 15px; flex-shrink: 0; border-top: 1px solid #dee2e6; background-color: #fff; position: sticky; bottom: 0; z-index: 1000;">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        {{ translate('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="las la-paper-plane mr-1"></i>{{ translate('Start Chat') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 强制显示发送按钮并调整模态框布局
$(document).ready(function() {
    // 当模态框显示时，确保发送按钮可见并调整布局
    $('#chat_modal').on('shown.bs.modal', function () {
        var modal = $(this);
        var sendButton = modal.find('button[type="submit"]');
        var modalDialog = modal.find('.modal-dialog');
        var modalContent = modal.find('.modal-content');
        var modalBody = modal.find('.modal-body');
        var modalFooter = modal.find('.modal-footer');

        // 调整模态框高度
        modalDialog.css({
            'max-height': '90vh',
            'margin': '1.75rem auto'
        });

        modalContent.css({
            'max-height': '85vh',
            'display': 'flex',
            'flex-direction': 'column'
        });

        modalBody.css({
            'max-height': '50vh',
            'overflow-y': 'auto',
            'flex': '1'
        });

        modalFooter.css({
            'flex-shrink': '0',
            'border-top': '1px solid #dee2e6',
            'background-color': '#fff',
            'position': 'sticky',
            'bottom': '0',
            'z-index': '1000'
        });

        // 强制显示发送按钮
        sendButton.css({
            'display': 'inline-block !important',
            'visibility': 'visible !important',
            'opacity': '1 !important',
            'background-color': '#007bff !important',
            'border': '1px solid #007bff !important',
            'color': 'white !important',
            'padding': '8px 16px !important',
            'border-radius': '4px !important',
            'font-weight': '600 !important',
            'margin': '0 !important',
            'position': 'static !important',
            'cursor': 'pointer !important',
            'z-index': '9999 !important',
            'min-width': '60px !important',
            'min-height': '38px !important'
        });

        // 强制重新渲染
        sendButton.hide().show();

        // 确保footer可见
        modalFooter[0].scrollIntoView({ behavior: 'smooth', block: 'end' });
    });
});
</script>

<!-- Modal -->
<div class="modal fade" id="login_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title fw-600">{{ translate('Login') }}</h6>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="p-3">
                    <form class="form-default" role="form" action="{{ route('cart.login.submit') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            @if (addon_is_activated('otp_system'))
                            <input type="text" class="form-control h-auto form-control-lg {{ $errors->has('email') ? ' is-invalid' : '' }}" value="{{ old('email') }}" placeholder="{{ translate('Email Or Phone') }}" name="email" id="email">
                            @else
                            <input type="email" class="form-control h-auto form-control-lg {{ $errors->has('email') ? ' is-invalid' : '' }}" value="{{ old('email') }}" placeholder="{{ translate('Email') }}" name="email">
                            @endif
                            @if (addon_is_activated('otp_system'))
                            <span class="opacity-60">{{ translate('Use country code before number') }}</span>
                            @endif
                        </div>

                        <div class="form-group">
                            <input type="password" name="password" class="form-control h-auto form-control-lg" placeholder="{{ translate('Password') }}">
                        </div>

                        <div class="row mb-2">
                            <div class="col-6">
                                <label class="aiz-checkbox">
                                    <input type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }}>
                                    <span class=opacity-60>{{ translate('Remember Me') }}</span>
                                    <span class="aiz-square-check"></span>
                                </label>
                            </div>
                            <div class="col-6 text-right">
                                <a href="{{ route('password.request') }}" class="text-reset opacity-60 fs-14">{{ translate('Forgot password?') }}</a>
                            </div>
                        </div>

                        <div class="mb-5">
                            <button type="submit" class="btn btn-primary btn-block fw-600">{{ translate('Login') }}</button>
                        </div>
                    </form>

                    <div class="text-center mb-3">
                        <p class="text-muted mb-0">{{ translate('Dont have an account?') }}</p>
                        <a href="{{ route('user.registration') }}">{{ translate('Register Now') }}</a>
                    </div>
                    @if (get_setting('google_login') == 1 || get_setting('facebook_login') == 1 || get_setting('twitter_login') == 1)
                    <div class="separator mb-3">
                        <span class="bg-white px-3 opacity-60">{{ translate('Or Login With') }}</span>
                    </div>
                    <ul class="list-inline social colored text-center mb-5">
                        @if (get_setting('facebook_login') == 1)
                        <li class="list-inline-item">
                            <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="facebook">
                                <i class="lab la-facebook-f"></i>
                            </a>
                        </li>
                        @endif
                        @if (get_setting('google_login') == 1)
                        <li class="list-inline-item">
                            <a href="{{ route('social.login', ['provider' => 'google']) }}" class="google">
                                <i class="lab la-google"></i>
                            </a>
                        </li>
                        @endif
                        @if (get_setting('twitter_login') == 1)
                        <li class="list-inline-item">
                            <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="twitter">
                                <i class="lab la-twitter"></i>
                            </a>
                        </li>
                        @endif
                    </ul>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script type="text/javascript">
    $(document).ready(function() {
        // 移除总价的隐藏类
        $('#chosen_price_div').removeClass('d-none');
        
        // 加号按钮点击
        $('[data-type="plus"]').click(function() {
            var input = $('input[name="quantity"]');
            var currentVal = parseInt(input.val());
            if (!isNaN(currentVal)) {
                if(currentVal < parseInt(input.attr('max'))) {
                    input.val(currentVal + 1);
                    getVariantPrice();
                }
            }
        });

        // 减号按钮点击
        $('[data-type="minus"]').click(function() {
            var input = $('input[name="quantity"]');
            var currentVal = parseInt(input.val());
            if (!isNaN(currentVal)) {
                if(currentVal > parseInt(input.attr('min'))) {
                    input.val(currentVal - 1);
                    getVariantPrice();
                }
            }
        });

        // 数量输入框变化
        $('input[name="quantity"]').on('change', function() {
            getVariantPrice();
        });

        // 初始化显示价格
        getVariantPrice();

        @if($detailedProduct->source == "alibaba")
        imgReplace();
        @endif
    });

    function imgReplace() {
        $("#tab_default_1 img").each(function(index, element) {
            var src = $(element).attr('data-src')
            if (src != undefined) {
                $(element).attr('src', src)
            }
        })
    }

    function CopyToClipboard(e) {
        var url = $(e).data('url');
        var $temp = $("<input>");
        $("body").append($temp);
        $temp.val(url).select();
        try {
            document.execCommand("copy");
            AIZ.plugins.notify('success', '{{ translate('Link copied to clipboard') }}');
        } catch (err) {
            AIZ.plugins.notify('danger', '{{ translate('Oops, unable to copy') }}');
        }
        $temp.remove();
    }

    function show_chat_modal() {
        @if(Auth::check())
        $('#chat_modal').modal('show');
        // 确保发送按钮可见
        setTimeout(function() {
            $('#chat_modal .modal-footer .btn[type="submit"]').show();
            $('#chat_modal .modal-footer .btn[type="submit"]').css({
                'display': 'inline-block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'background-color': '#007bff !important',
                'color': 'white !important',
                'border': '1px solid #007bff !important',
                'padding': '8px 16px !important'
            });
            console.log('发送按钮已强制显示');
        }, 100);
        @else
        $('#login_modal').modal('show');
        @endif
    }

    // 监听模态框显示事件
    $('#chat_modal').on('shown.bs.modal', function () {
        console.log('模态框已显示，检查发送按钮...');
        var submitBtn = $('#chat_modal .modal-footer .btn[type="submit"]');
        if (submitBtn.length === 0) {
            console.error('发送按钮未找到！');
        } else {
            console.log('发送按钮已找到，强制显示...');
            submitBtn.css({
                'display': 'inline-block !important',
                'visibility': 'visible !important',
                'opacity': '1 !important',
                'background-color': '#007bff !important',
                'color': 'white !important',
                'border': '1px solid #007bff !important',
                'padding': '8px 16px !important'
            }).show();
        }
    });

    function getVariantPrice(){
        if($('#option-choice-form input[name=quantity]').val() > 0){
            $.ajax({
               type:"POST",
               url: '{{ route('products.variant_price') }}',
               data: $('#option-choice-form').serializeArray(),
               success: function(data){
                   $('#chosen_price_div').removeClass('d-none');
                   $('#chosen_price').html(data.price);
                   
                   // 更新按钮状态
                   var qty = $('input[name="quantity"]');
                   var currentVal = parseInt(qty.val());
                   $('[data-type="minus"]').prop('disabled', currentVal <= parseInt(qty.attr('min')));
                   $('[data-type="plus"]').prop('disabled', currentVal >= parseInt(qty.attr('max')));
               }
            });
        }
    }

    // Pagination using ajax
    $(window).on('hashchange', function() {
        if (window.location.hash) {
            var page = window.location.hash.replace('#', '');
            if (page == Number.NaN || page <= 0) {
                return false;
            } else {
                getQuestions(page);
            }
        }
    });

    $(document).ready(function() {
        $(document).on('click', '.pagination a', function(e) {
            getQuestions($(this).attr('href').split('page=')[1]);
            e.preventDefault();
        });
    });

    function getQuestions(page) {
        $.ajax({
            url: '?page=' + page,
            dataType: 'json',
        }).done(function(data) {
            $('.pagination-area').html(data);
            location.hash = page;
        }).fail(function() {
            alert('Something went worng! Questions could not be loaded.');
        });
    }
</script>
@endsection
{{-- 调试版本的YouTube播放器 --}}
@php
    $youtube_enabled = get_setting('homepage_youtube_enabled');
    $youtube_url = get_setting('homepage_youtube_url');
    $youtube_title = get_setting('homepage_youtube_title') ?: 'Watch Our Video';
@endphp

<div style="position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; z-index: 99999;">
    <strong>YouTube Debug Info:</strong><br>
    Enabled: {{ $youtube_enabled ? 'Yes' : 'No' }}<br>
    URL: {{ $youtube_url ?: 'Not set' }}<br>
    Title: {{ $youtube_title }}<br>
</div>

@if($youtube_enabled == 1 && $youtube_url)
    @php
        // 提取YouTube视频ID
        $video_id = '';
        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $youtube_url, $matches)) {
            $video_id = $matches[1];
        }
    @endphp
    
    @if($video_id)
        <div style="position: fixed; bottom: 20px; left: 20px; width: 300px; background: white; border: 2px solid #3B82C4; border-radius: 10px; z-index: 9999; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="background: linear-gradient(135deg, #3B82C4, #2E86AB); color: white; padding: 10px; border-radius: 8px 8px 0 0;">
                <strong>{{ $youtube_title }}</strong>
                <button onclick="this.parentElement.parentElement.style.display='none'" style="float: right; background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 8px; border-radius: 3px; cursor: pointer;">×</button>
            </div>
            <div style="padding: 0;">
                <div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
                    <iframe 
                        src="https://www.youtube.com/embed/{{ $video_id }}?enablejsapi=1&rel=0&modestbranding=1" 
                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                        allowfullscreen>
                    </iframe>
                </div>
            </div>
        </div>
    @else
        <div style="position: fixed; bottom: 20px; left: 20px; background: orange; color: white; padding: 10px; z-index: 9999;">
            Invalid YouTube URL: {{ $youtube_url }}
        </div>
    @endif
@else
    <div style="position: fixed; bottom: 20px; left: 20px; background: gray; color: white; padding: 10px; z-index: 9999;">
        YouTube Player Disabled or No URL Set
    </div>
@endif

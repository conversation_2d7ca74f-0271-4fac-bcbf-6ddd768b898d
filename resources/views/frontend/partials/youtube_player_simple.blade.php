@php
    $youtube_url = get_setting('homepage_youtube_url');
    $youtube_title = get_setting('homepage_youtube_title') ?: 'Watch Our Video';
    
    // 提取YouTube视频ID
    $video_id = '';
    if ($youtube_url && preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $youtube_url, $matches)) {
        $video_id = $matches[1];
    }
@endphp

@if($video_id)
{{-- 超简单版YouTube播放器 - 纯内联样式 --}}
<div id="youtube-simple-player" style="
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    width: 350px !important;
    max-width: calc(100vw - 40px) !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    z-index: 99999 !important;
    overflow: hidden !important;
    border: 2px solid #3B82C4 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
">
    {{-- 标题栏 --}}
    <div style="
        background: linear-gradient(135deg, #3B82C4, #2E86AB) !important;
        color: white !important;
        padding: 10px 15px !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    ">
        <h6 style="
            margin: 0 !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            color: white !important;
        ">{{ $youtube_title }}</h6>
        <button onclick="closeSimpleYouTubePlayer()" style="
            background: rgba(255, 255, 255, 0.2) !important;
            border: none !important;
            color: white !important;
            width: 28px !important;
            height: 28px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 16px !important;
            font-weight: bold !important;
        ">×</button>
    </div>
    {{-- 视频内容 --}}
    <div style="padding: 0 !important;">
        <div style="
            position: relative !important;
            width: 100% !important;
            height: 0 !important;
            padding-bottom: 56.25% !important;
        ">
            <iframe
                src="https://www.youtube.com/embed/{{ $video_id }}?enablejsapi=1&rel=0&modestbranding=1"
                style="
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    border: none !important;
                "
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen>
            </iframe>
        </div>
    </div>
</div>

<script>
// 超简单版JavaScript
function closeSimpleYouTubePlayer() {
    const player = document.getElementById('youtube-simple-player');
    if (player) {
        player.style.display = 'none';
        localStorage.setItem('simple_youtube_closed', 'true');
    }
}

// 检查是否已关闭
document.addEventListener('DOMContentLoaded', function() {
    if (localStorage.getItem('simple_youtube_closed') === 'true') {
        const player = document.getElementById('youtube-simple-player');
        if (player) {
            player.style.display = 'none';
        }
    }
});
</script>
@endif

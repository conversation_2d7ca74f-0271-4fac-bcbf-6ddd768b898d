@php
    $youtube_url = get_setting('homepage_youtube_url');
    $youtube_title = get_setting('homepage_youtube_title') ?: 'Watch Our Video';

    // 提取YouTube视频ID
    $video_id = '';
    if ($youtube_url && preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $youtube_url, $matches)) {
        $video_id = $matches[1];
    }
@endphp

@if($video_id)
{{-- 简化版YouTube播放器 --}}
<div id="youtube-player-container" class="youtube-player-fixed">
    {{-- 标题栏 --}}
    <div class="youtube-player-header">
        <h6 class="youtube-player-title">{{ $youtube_title }}</h6>
        <button onclick="closeYouTubePlayer()" class="btn-control">×</button>
    </div>
    {{-- 视频内容 --}}
    <div class="youtube-player-content">
        <div class="youtube-embed-container">
            <iframe
                src="https://www.youtube.com/embed/{{ $video_id }}?enablejsapi=1&rel=0&modestbranding=1"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen>
            </iframe>
        </div>
    </div>
</div>

<script>
// 简化版JavaScript - 只处理关闭功能
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('youtube-player-container');

    // 检查是否已经关闭过
    if (localStorage.getItem('youtube_player_closed') === 'true') {
        if (container) {
            container.style.display = 'none';
        }
    }
});

// 关闭播放器函数
function closeYouTubePlayer() {
    const container = document.getElementById('youtube-player-container');
    if (container) {
        container.style.display = 'none';
        localStorage.setItem('youtube_player_closed', 'true');
    }
}
</script>
@endif

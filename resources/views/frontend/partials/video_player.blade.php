@php
    $video_file = get_setting('homepage_video_file');
    $video_title = get_setting('homepage_video_title') ?: 'Watch Our Video';
    $video_autoplay = get_setting('homepage_video_autoplay') == 1;
    $video_loop = get_setting('homepage_video_loop') == 1;
    $video_muted = get_setting('homepage_video_muted') == 1;
    $video_controls = get_setting('homepage_video_controls') == 1;
    $video_enabled = get_setting('homepage_video_enabled') == 1;
@endphp

@if($video_enabled && $video_file)
{{-- 自定义视频播放器 --}}
<div id="custom-video-player" style="
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    width: 350px !important;
    max-width: calc(100vw - 40px) !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    z-index: 99999 !important;
    overflow: hidden !important;
    border: 2px solid #3B82C4 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
">
    {{-- 标题栏 --}}
    <div style="
        background: linear-gradient(135deg, #3B82C4, #2E86AB) !important;
        color: white !important;
        padding: 10px 15px !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        cursor: grab !important;
        user-select: none !important;
    " id="video-player-header">
        <h6 style="
            margin: 0 !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            color: white !important;
        ">{{ $video_title }}</h6>
        <div style="display: flex; gap: 5px;">
            <button onclick="minimizeVideoPlayer()" style="
                background: rgba(255, 255, 255, 0.2) !important;
                border: none !important;
                color: white !important;
                width: 28px !important;
                height: 28px !important;
                border-radius: 6px !important;
                cursor: pointer !important;
                font-size: 14px !important;
                font-weight: bold !important;
            ">−</button>
            <button onclick="closeVideoPlayer()" style="
                background: rgba(255, 255, 255, 0.2) !important;
                border: none !important;
                color: white !important;
                width: 28px !important;
                height: 28px !important;
                border-radius: 6px !important;
                cursor: pointer !important;
                font-size: 16px !important;
                font-weight: bold !important;
            ">×</button>
        </div>
    </div>
    {{-- 视频内容 --}}
    <div style="padding: 0 !important; position: relative;">
        <video
            id="homepage-video"
            style="
                width: 100% !important;
                height: auto !important;
                display: block !important;
                background: #000 !important;
            "
            @if($video_autoplay) autoplay @endif
            @if($video_loop) loop @endif
            muted
            @if($video_controls) controls @endif
            preload="metadata"
            playsinline
        >
            <source src="{{ uploaded_asset($video_file) }}" type="video/mp4">
            Your browser does not support the video tag.
        </video>

        {{-- 静音指示器 --}}
        <div id="mute-indicator" style="
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 8px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            z-index: 10;
        ">
            <span id="mute-icon">🔇</span>
            <span id="mute-text">Muted</span>
        </div>
    </div>
</div>

{{-- 最小化按钮 --}}
<div id="video-minimized-btn" style="
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    background: linear-gradient(135deg, #3B82C4, #2E86AB) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    cursor: pointer !important;
    z-index: 99998 !important;
    display: none !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    max-width: 250px !important;
" onclick="restoreVideoPlayer()">
    <i class="las la-play" style="font-size: 18px; color: #FF6B35;"></i>
    <span>{{ $video_title }}</span>
</div>

<script>
// 视频播放器控制函数
let isDragging = false;
let currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;

// 关闭播放器
function closeVideoPlayer() {
    const player = document.getElementById('custom-video-player');
    const minimizedBtn = document.getElementById('video-minimized-btn');
    const video = document.getElementById('homepage-video');
    
    if (player) {
        player.style.display = 'none';
        localStorage.setItem('video_player_closed', 'true');
    }
    if (minimizedBtn) {
        minimizedBtn.style.display = 'none';
    }
    if (video) {
        video.pause();
    }
}

// 最小化播放器
function minimizeVideoPlayer() {
    const player = document.getElementById('custom-video-player');
    const minimizedBtn = document.getElementById('video-minimized-btn');
    const video = document.getElementById('homepage-video');
    
    if (player) {
        player.style.display = 'none';
    }
    if (minimizedBtn) {
        minimizedBtn.style.display = 'flex';
    }
    if (video) {
        video.pause();
    }
}

// 恢复播放器
function restoreVideoPlayer() {
    const player = document.getElementById('custom-video-player');
    const minimizedBtn = document.getElementById('video-minimized-btn');
    const video = document.getElementById('homepage-video');
    
    if (player) {
        player.style.display = 'block';
    }
    if (minimizedBtn) {
        minimizedBtn.style.display = 'none';
    }
    @if($video_autoplay)
    if (video) {
        // 确保循环播放
        if ({{ $video_loop ? 'true' : 'false' }}) {
            video.loop = true;
        }
        // 恢复时保持静音状态
        video.muted = true;
        console.log('Restoring video with muted autoplay');
        video.play().catch(e => console.log('Auto-play prevented:', e));
    }
    @endif
}

// 拖拽功能
document.addEventListener('DOMContentLoaded', function() {
    const player = document.getElementById('custom-video-player');
    const header = document.getElementById('video-player-header');
    const video = document.getElementById('homepage-video');

    // 检查是否已关闭
    if (localStorage.getItem('video_player_closed') === 'true') {
        if (player) {
            player.style.display = 'none';
        }
        return;
    }

    // 拖拽事件
    if (header) {
        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
    }

    // 调试信息
    console.log('=== Video Player Debug Info ===');
    console.log('Video element found:', !!video);
    console.log('Video file ID:', '{{ $video_file }}');
    console.log('Video URL:', video ? video.querySelector('source').src : 'No video');
    console.log('Autoplay enabled:', {{ $video_autoplay ? 'true' : 'false' }});
    console.log('Controls enabled:', {{ $video_controls ? 'true' : 'false' }});
    console.log('Muted:', {{ $video_muted ? 'true' : 'false' }});
    console.log('Loop:', {{ $video_loop ? 'true' : 'false' }});

    // 视频事件监听
    if (video) {
        // 错误处理
        video.addEventListener('error', function(e) {
            console.error('Video error:', e);
            console.error('Video error code:', video.error ? video.error.code : 'Unknown');
            console.error('Video error message:', video.error ? video.error.message : 'Unknown');
        });

        // 加载事件
        video.addEventListener('loadstart', function() {
            console.log('Video: Load started');
        });

        video.addEventListener('loadedmetadata', function() {
            console.log('Video: Metadata loaded');
        });

        video.addEventListener('loadeddata', function() {
            console.log('Video: Data loaded');

            // 自动播放（始终静音）
            if ({{ $video_autoplay ? 'true' : 'false' }}) {
                console.log('Attempting muted autoplay...');

                // 自动播放时始终静音
                video.muted = true;

                video.play().catch(function(error) {
                    console.log('Auto-play prevented:', error);
                    // 显示播放按钮
                    showPlayButton();
                });
            }
        });

        video.addEventListener('canplay', function() {
            console.log('Video: Can play');
        });

        video.addEventListener('play', function() {
            console.log('Video: Started playing');
        });

        video.addEventListener('pause', function() {
            console.log('Video: Paused');
        });

        video.addEventListener('ended', function() {
            console.log('Video: Ended');
            if ({{ $video_loop ? 'true' : 'false' }}) {
                console.log('Restarting video for loop...');
                console.log('Current muted state:', video.muted);
                video.currentTime = 0;
                video.play().catch(e => console.log('Loop restart failed:', e));
            }
        });

        // 确保循环属性正确设置
        if ({{ $video_loop ? 'true' : 'false' }}) {
            video.loop = true;
            console.log('Video loop property set to true');
        }

        // 手动触发加载
        video.load();

        // 监听音量变化
        video.addEventListener('volumechange', function() {
            updateMuteIndicator();
        });

        // 初始化静音指示器
        setTimeout(function() {
            updateMuteIndicator();
        }, 100);
    }

    // 更新静音指示器
    function updateMuteIndicator() {
        const video = document.getElementById('homepage-video');
        const muteIcon = document.getElementById('mute-icon');
        const muteText = document.getElementById('mute-text');

        if (video && muteIcon && muteText) {
            if (video.muted) {
                muteIcon.textContent = '🔇';
                muteText.textContent = 'Muted';
            } else {
                muteIcon.textContent = '🔊';
                muteText.textContent = 'Sound On';
            }
        }
    }

    // 显示播放按钮函数
    function showPlayButton() {
        if (document.getElementById('manual-play-btn')) return; // 避免重复创建

        const playBtn = document.createElement('div');
        playBtn.id = 'manual-play-btn';
        playBtn.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(59, 130, 196, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 24px;
            z-index: 10;
            transition: all 0.3s ease;
        `;
        playBtn.innerHTML = '▶';
        playBtn.onclick = function() {
            if (video) {
                // 用户点击播放时取消静音
                video.muted = false;
                console.log('User clicked play - unmuting video');

                // 确保循环播放属性设置正确
                if ({{ $video_loop ? 'true' : 'false' }}) {
                    video.loop = true;
                    console.log('Loop enabled for manual play');
                }

                video.play().then(() => {
                    console.log('Manual play successful with sound');
                    console.log('Video muted:', video.muted);
                    console.log('Video loop property:', video.loop);
                    playBtn.remove();
                    // 更新静音指示器
                    updateMuteIndicator();
                }).catch(error => {
                    console.error('Manual play failed:', error);
                });
            }
        };

        const videoContainer = video.parentNode;
        videoContainer.style.position = 'relative';
        videoContainer.appendChild(playBtn);
    }
});

function dragStart(e) {
    const player = document.getElementById('custom-video-player');
    initialX = e.clientX - xOffset;
    initialY = e.clientY - yOffset;
    isDragging = true;
    player.style.cursor = 'grabbing';
}

function drag(e) {
    if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
        
        xOffset = currentX;
        yOffset = currentY;
        
        // 限制在视窗内
        const player = document.getElementById('custom-video-player');
        const rect = player.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;
        
        xOffset = Math.max(0, Math.min(maxX, xOffset));
        yOffset = Math.max(0, Math.min(maxY, yOffset));
        
        player.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
    }
}

function dragEnd() {
    isDragging = false;
    const player = document.getElementById('custom-video-player');
    player.style.cursor = 'default';
}

// 响应式处理
window.addEventListener('resize', function() {
    const player = document.getElementById('custom-video-player');
    if (player && player.style.display !== 'none') {
        const rect = player.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;
        
        xOffset = Math.max(0, Math.min(maxX, xOffset));
        yOffset = Math.max(0, Math.min(maxY, yOffset));
        
        player.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
    }
});
</script>
@endif

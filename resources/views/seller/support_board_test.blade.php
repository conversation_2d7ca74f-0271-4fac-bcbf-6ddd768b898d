@extends('seller.layouts.app')

@section('panel_content')
<div class="aiz-titlebar text-left mt-2 mb-3">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">Support Board Test</h1>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0 h6">Support Board Integration Test</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>Configuration Check</h6>
                    </div>
                    <div class="card-body">
                        <div id="config-status">
                            <div class="text-muted">
                                <i class="fas fa-spinner fa-spin"></i> Checking configuration...
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>URLs:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Chat URL:</strong> {{ $chat_url }}</li>
                                <li><strong>Main JS:</strong> {{ $main_js_url }}</li>
                                <li><strong>jQuery:</strong> {{ $jquery_url }}</li>
                            </ul>
                        </div>
                        
                        <div class="mt-3">
                            <h6>User Info:</h6>
                            <ul class="list-unstyled">
                                <li><strong>ID:</strong> {{ $user->id }}</li>
                                <li><strong>Name:</strong> {{ $user->name }}</li>
                                <li><strong>Email:</strong> {{ $user->email }}</li>
                                <li><strong>Type:</strong> {{ $user->user_type }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>Support Board Status</h6>
                    </div>
                    <div class="card-body">
                        <div id="sb-status">
                            <div class="text-muted">
                                <i class="fas fa-spinner fa-spin"></i> Checking Support Board...
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button onclick="testSupportBoard()" class="btn btn-primary btn-sm">
                                <i class="fas fa-cog"></i> Test Support Board
                            </button>
                            <button onclick="forceChatOpen()" class="btn btn-success btn-sm">
                                <i class="fas fa-comments"></i> Force Open Chat
                            </button>
                            <button onclick="checkFiles()" class="btn btn-info btn-sm">
                                <i class="fas fa-file-check"></i> Check Files
                            </button>
                        </div>
                        
                        <div id="test-results" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6>Console Log</h6>
                    </div>
                    <div class="card-body">
                        <div id="console-log" style="background: #f8f9fa; padding: 15px; border-radius: 5px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            <div class="text-muted">Console output will appear here...</div>
                        </div>
                        <div class="mt-2">
                            <button onclick="clearLog()" class="btn btn-secondary btn-sm">Clear Log</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 重写console.log来显示在页面上
var originalLog = console.log;
var originalError = console.error;
var logContainer = null;

function addToLog(message, type = 'log') {
    if (!logContainer) {
        logContainer = document.getElementById('console-log');
    }
    
    var timestamp = new Date().toLocaleTimeString();
    var color = type === 'error' ? 'text-danger' : (type === 'warn' ? 'text-warning' : 'text-dark');
    var icon = type === 'error' ? '❌' : (type === 'warn' ? '⚠️' : 'ℹ️');
    
    logContainer.innerHTML += '<div class="' + color + '">[' + timestamp + '] ' + icon + ' ' + message + '</div>';
    logContainer.scrollTop = logContainer.scrollHeight;
}

console.log = function(...args) {
    originalLog.apply(console, args);
    addToLog(args.join(' '), 'log');
};

console.error = function(...args) {
    originalError.apply(console, args);
    addToLog(args.join(' '), 'error');
};

function clearLog() {
    document.getElementById('console-log').innerHTML = '<div class="text-muted">Console cleared...</div>';
}

function updateStatus(elementId, status, message) {
    var element = document.getElementById(elementId);
    var iconClass = '';
    var textClass = '';
    
    switch(status) {
        case 'success':
            iconClass = 'fas fa-check-circle text-success';
            textClass = 'text-success';
            break;
        case 'error':
            iconClass = 'fas fa-exclamation-circle text-danger';
            textClass = 'text-danger';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-triangle text-warning';
            textClass = 'text-warning';
            break;
        default:
            iconClass = 'fas fa-info-circle text-info';
            textClass = 'text-info';
    }
    
    element.innerHTML = '<div class="' + textClass + '"><i class="' + iconClass + '"></i> ' + message + '</div>';
}

function testSupportBoard() {
    console.log('=== Testing Support Board ===');
    
    var results = [];
    
    // 检查SBF对象
    if (typeof SBF !== 'undefined') {
        results.push('✅ SBF object is defined');
        console.log('SBF object:', SBF);
        
        // 检查各种方法
        var methods = ['init', 'open', 'close', 'login', 'chat'];
        methods.forEach(function(method) {
            if (SBF[method]) {
                results.push('✅ SBF.' + method + ' is available');
            } else {
                results.push('❌ SBF.' + method + ' is NOT available');
            }
        });
        
        updateStatus('sb-status', 'success', 'Support Board is loaded');
    } else {
        results.push('❌ SBF object is NOT defined');
        updateStatus('sb-status', 'error', 'Support Board is NOT loaded');
    }
    
    // 检查配置变量
    if (typeof window.SB_INIT_USER !== 'undefined') {
        results.push('✅ SB_INIT_USER is defined');
        console.log('SB_INIT_USER:', window.SB_INIT_USER);
    } else {
        results.push('❌ SB_INIT_USER is NOT defined');
    }
    
    if (typeof window.SB_INIT !== 'undefined') {
        results.push('✅ SB_INIT is defined');
        console.log('SB_INIT:', window.SB_INIT);
    } else {
        results.push('❌ SB_INIT is NOT defined');
    }
    
    document.getElementById('test-results').innerHTML = '<ul><li>' + results.join('</li><li>') + '</li></ul>';
}

function forceChatOpen() {
    console.log('=== Forcing Chat Open ===');
    
    if (typeof SBF !== 'undefined') {
        try {
            if (SBF.open) {
                SBF.open();
                console.log('✅ Chat opened successfully');
                updateStatus('sb-status', 'success', 'Chat opened');
            } else {
                console.log('❌ SBF.open method not available');
            }
        } catch (error) {
            console.error('❌ Error opening chat:', error);
        }
    } else {
        console.error('❌ SBF not available');
    }
}

function checkFiles() {
    console.log('=== Checking Files ===');
    
    var files = [
        '{{ $main_js_url }}',
        '{{ $jquery_url }}',
        '{{ $chat_url }}/config.php'
    ];
    
    files.forEach(function(url) {
        fetch(url, { method: 'HEAD' })
            .then(function(response) {
                if (response.ok) {
                    console.log('✅ File accessible:', url);
                } else {
                    console.error('❌ File not accessible:', url, 'Status:', response.status);
                }
            })
            .catch(function(error) {
                console.error('❌ Error checking file:', url, error);
            });
    });
}

// 页面加载完成后自动检查
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== Page Loaded ===');
    updateStatus('config-status', 'success', 'Configuration loaded');
    
    // 延迟检查Support Board
    setTimeout(function() {
        testSupportBoard();
    }, 3000);
    
    // 定期检查Support Board状态
    setInterval(function() {
        if (typeof SBF !== 'undefined' && document.getElementById('sb-status').innerHTML.includes('Checking')) {
            updateStatus('sb-status', 'success', 'Support Board detected');
        }
    }, 1000);
});
</script>
@endsection

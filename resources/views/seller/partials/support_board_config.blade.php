{{-- Support Board Configuration for Seller Panel --}}
@if(auth()->check() && auth()->user()->user_type == 'seller')

{{-- Support Board User Configuration --}}
<script>
// Support Board 用户配置 - 简化版本
window.SB_INIT_USER = {
    id: {{ auth()->id() }},
    first_name: '{{ addslashes(explode(" ", auth()->user()->name)[0] ?? auth()->user()->name) }}',
    last_name: '{{ addslashes(explode(" ", auth()->user()->name, 2)[1] ?? "") }}',
    email: '{{ auth()->user()->email }}',
    user_type: 'seller'
};

// Support Board 初始化配置 - 简化版本
window.SB_INIT = {
    init: true,
    open: false
};

console.log('Support Board config loaded for seller:', window.SB_INIT_USER);
</script>

{{-- Support Board Scripts - 确保正确加载顺序 --}}
<script>
// 检查jQuery是否已加载，如果没有则加载
if (typeof jQuery === 'undefined') {
    document.write('<script src="{{ url('/chat/js/min/jquery.min.js') }}"><\/script>');
}
</script>

{{-- 主要的Support Board脚本 --}}
<script id="sbinit" src="{{ url('/chat/js/main.js') }}"></script>

{{-- Support Board 初始化脚本 --}}
<script>
// 等待页面完全加载
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for Support Board...');

    var attempts = 0;
    var maxAttempts = 40; // 20秒

    var checkSB = setInterval(function() {
        attempts++;
        console.log('Checking Support Board, attempt:', attempts);

        if (typeof SBF !== 'undefined') {
            clearInterval(checkSB);
            console.log('✅ Support Board loaded successfully!');

            // 初始化Support Board
            setTimeout(function() {
                try {
                    console.log('Initializing Support Board with user:', window.SB_INIT_USER);

                    // 登录用户
                    if (SBF.login && window.SB_INIT_USER) {
                        SBF.login(
                            window.SB_INIT_USER.email,
                            '',
                            window.SB_INIT_USER.first_name,
                            window.SB_INIT_USER.last_name,
                            window.SB_INIT_USER.id
                        );
                        console.log('✅ User logged in to Support Board');
                    }

                    // 注释掉强制显示聊天按钮，避免在右下角显示客服图标
                    // if (SBF.chat) {
                    //     console.log('✅ Chat functions available');

                    //     // 尝试显示聊天按钮
                    //     if (SBF.chat.widget && SBF.chat.widget.show) {
                    //         SBF.chat.widget.show();
                    //         console.log('✅ Chat widget shown');
                    //     }
                    // }

                    // 如果在support-chat页面，自动打开聊天窗口
                    @if(request()->routeIs('seller.support.chat'))
                    setTimeout(function() {
                        if (SBF.open) {
                            SBF.open();
                            console.log('✅ Chat window opened automatically');
                        }
                    }, 2000);
                    @endif

                } catch (error) {
                    console.error('❌ Support Board initialization error:', error);
                }
            }, 1500);

        } else if (attempts >= maxAttempts) {
            clearInterval(checkSB);
            console.error('❌ Support Board failed to load after', maxAttempts, 'attempts');

            // 显示错误信息
            console.log('Checking if main.js loaded correctly...');
            console.log('Current URL:', '{{ url('/chat/js/main.js') }}');
        }
    }, 500);
});
</script>

{{-- 自定义样式 --}}
<style>
/* Support Board 自定义样式 */
.sb-chat-btn {
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.sb-chat-btn:hover {
    background-color: #0056b3 !important;
    border-color: #0056b3 !important;
}

/* 确保聊天按钮在卖家面板中可见 */
.sb-chat {
    z-index: 9999 !important;
}

.sb-chat-btn {
    z-index: 9999 !important;
}

/* 聊天窗口样式调整 */
.sb-chat-window {
    z-index: 9998 !important;
}

/* 隐藏右下角的客服图标 */
.sb-chat-btn,
.sb-chat-widget,
.sb-chat-launcher,
#sb-chat-btn,
#sb-chat-widget {
    display: none !important;
    visibility: hidden !important;
}
</style>

@endif

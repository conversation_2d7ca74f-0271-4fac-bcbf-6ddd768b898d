@extends('seller.layouts.app')

@section('panel_content')
    {{-- // 二㊎次开㊍发程序搭㊌建刷㊋单资金㊏盘彩票㊚盘游戏㊛理财TG：㊐@aiz㊊iziy㊣uan，TG：@laip㊤iaozi www㊥.laip㊦iaozi.co㊧m（请自㊨行忽略㊒中间符㊫号！)a㊑bcd㊓efgh㊔ijkl㊕mno㊖pqr㊗stu㊘vwxy㊜z --}}
    {{-- 注意：要将最新消息显示在最前面，请修改控制器中的排序方法，例如: ->orderBy('updated_at', 'desc') --}}
    <div class="aiz-titlebar mt-2 mb-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold text-dark">{{ translate('Conversations')}}</h4>
            </div>
        </div>
    </div>

    <div class="card shadow-sm border-0 rounded-lg">
        <div class="card-body p-0">
            @if(count($conversations) > 0)
                <ul class="list-group list-group-flush conversation-list">
                    @foreach ($conversations as $key => $conversation)
                        @if ($conversation->receiver != null && $conversation->sender != null)
                            @php
                                // 计算未读消息数量
                                $unread_count = 0;
                                foreach($conversation->messages as $message) {
                                    if($message->user_id != Auth::user()->id) {
                                        if(Auth::user()->id == $conversation->sender_id && $conversation->sender_viewed == 0) {
                                            $unread_count++;
                                        }
                                        else if(Auth::user()->id == $conversation->receiver_id && $conversation->receiver_viewed == 0) {
                                            $unread_count++;
                                        }
                                        else if($message->updated_at == $message->created_at) {
                                            $unread_count++;
                                        }
                                    }
                                }
                            @endphp
                            <li class="list-group-item p-3 border-start border-0 border-3 border-transparent conv-item {{ $key > 0 ? 'border-top-item' : '' }}">
                                <div class="mobile-conversation">
                                    <!-- 头像区 -->
                                    <div class="avatar-container">
                                        <div class="position-relative">
                                            <div class="avatar-md rounded-circle border shadow-sm d-flex align-items-center justify-content-center overflow-hidden">
                                                @if (Auth::user()->id == $conversation->sender_id)
                                                    <img 
                                                        @if ($conversation->receiver->avatar_original == null) 
                                                            src="{{ static_asset('assets/img/avatar-place.png') }}" 
                                                        @else 
                                                            src="{{ uploaded_asset($conversation->receiver->avatar_original) }}" 
                                                        @endif 
                                                        class="img-fit"
                                                        onerror="this.onerror=null;this.src='{{ static_asset('assets/img/avatar-place.png') }}';">
                                                @else
                                                    <img 
                                                        @if ($conversation->sender->avatar_original == null) 
                                                            src="{{ static_asset('assets/img/avatar-place.png') }}" 
                                                        @else 
                                                            src="{{ uploaded_asset($conversation->sender->avatar_original) }}" 
                                                        @endif 
                                                        class="img-fit"
                                                        onerror="this.onerror=null;this.src='{{ static_asset('assets/img/avatar-place.png') }}';">
                                                @endif
                                            </div>
                                            
                                            <!-- 消息提示 -->
                                            @if ((Auth::user()->id == $conversation->sender_id && $conversation->sender_viewed == 0) || (Auth::user()->id == $conversation->receiver_id && $conversation->receiver_viewed == 0))
                                                <span class="badge bg-danger position-absolute bottom-0 end-0 rounded-circle p-1 border border-white"></span>
                                            @else
                                                @foreach ($conversation->messages as $messages)
                                                    @if ($messages->updated_at == $messages->created_at && $messages->user_id != Auth::user()->id)
                                                        <span class="badge bg-danger position-absolute bottom-0 end-0 rounded-circle p-1 border border-white"></span>
                                                        @break
                                                    @endif
                                                @endforeach
                                            @endif
                                        </div>
                                        
                                        <!-- 用户名和时间 -->
                                        <div class="mt-2 text-center user-info">
                                            <div class="fw-bold fs-14 text-truncate" style="max-width: 80px;">
                                                @if (Auth::user()->id == $conversation->sender_id)
                                                    {{ $conversation->receiver->name }}
                                                @else
                                                    {{ $conversation->sender->name }}
                                                @endif
                                            </div>
                                            <div class="fs-12 text-muted">
                                                @if($conversation->messages->count() > 0)
                                                    {{ date('Y-m-d H:i', strtotime($conversation->messages->last()->created_at)) }}
                                                @else
                                                    {{ translate('No messages') }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 主要内容区 -->
                                    <div class="conversation-middle">
                                        <a href="{{ route('seller.conversations.show', encrypt($conversation->id)) }}" class="text-reset d-block text-decoration-none stretched-link position-relative">
                                            
                                            <div class="product-header mb-2">
                                                <h5 class="product-title fw-bold mb-1">{{ $conversation->title }}</h5>
                                                <div class="message-indicators d-flex align-items-center flex-wrap">
                                                    <div class="message-count me-3 mb-1">
                                                        <span class="count-badge">{{ $conversation->messages->count() }}</span>
                                                        <span class="count-label">{{ translate('Messages') }}</span>
                                                        @if($unread_count > 0)
                                                        <span class="separator"></span>
                                                        @endif
                                                    </div>
                                                    @if($unread_count > 0)
                                                    <div class="unread-count mb-1">
                                                        <span class="unread-badge pulse">{{ $unread_count }}</span>
                                                        <span class="unread-label">{{ translate('Unread') }}</span>
                                                    </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="recent-messages">
                                                @php
                                                    $recent_messages = $conversation->messages->sortByDesc('created_at')->take(5);
                                                @endphp
                                                @foreach($recent_messages as $key => $message)
                                                    <div class="message-preview {{ $key > 0 ? 'message-secondary' : '' }}">
                                                        <div class="message-content">
                                                            <span class="message-sender">
                                                                {{ $message->user_id == Auth::user()->id ? translate('You') : ($message->user_id == $conversation->sender_id ? $conversation->sender->name : $conversation->receiver->name) }}:
                                                            </span>
                                                            <span class="message-text">{{ Str::limit($message->message, 50) }}</span>
                                                            <span class="message-time">
                                                                @php
                                                                    $time_string = Carbon\Carbon::parse($message->created_at)->diffForHumans();
                                                                    
                                                                    // 处理"X minutes/hours/days/weeks ago"格式
                                                                    if(preg_match('/^(\d+)\s+(minute|hour|day|week|month|year)s?\s+(ago)$/', $time_string, $matches)) {
                                                                        echo $matches[1].' '.translate($matches[2]).' '.translate($matches[3]);
                                                                    }
                                                                    // 处理"a minute/hour/day/week ago"格式
                                                                    else if(preg_match('/^(a|an)\s+(minute|hour|day|week|month|year)\s+(ago)$/', $time_string, $matches)) {
                                                                        echo translate($matches[1]).' '.translate($matches[2]).' '.translate($matches[3]);
                                                                    }
                                                                    // 处理"X minutes/hours/days/weeks from now"格式
                                                                    else if(preg_match('/^(\d+)\s+(minute|hour|day|week|month|year)s?\s+(from now)$/', $time_string, $matches)) {
                                                                        echo $matches[1].' '.translate($matches[2]).' '.translate($matches[3]);
                                                                    }
                                                                    // 其他格式整体翻译
                                                                    else {
                                                                        echo translate($time_string);
                                                                    }
                                                                @endphp
                                                            </span>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </a>
                                    </div>
                                    
                                    <!-- 将产品信息块移到这里 -->
                                    <div class="product-info-wrapper">
                                        @if (isset($conversation->product_thumbnail_img) && $conversation->product_thumbnail_img != null)
                                            <div class="product-image">
                                                <img src="{{ uploaded_asset($conversation->product_thumbnail_img) }}"
                                                    alt="{{ $conversation->title }}"
                                                    class="img-fit"
                                                    onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                            </div>
                                        @endif
                                        
                                        @if (isset($conversation->product_slug) && $conversation->product_slug != null)
                                            <a href="{{ route('product', $conversation->product_slug) }}" target="_blank" 
                                            class="btn btn-sm btn-info rounded px-3 mt-2" style="z-index: 10; position: relative;"
                                            onclick="event.stopPropagation(); event.preventDefault(); window.open(this.href, '_blank');">
                                                <i class="las la-eye"></i> {{ translate('View Product') }}
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </li>
                        @endif
                    @endforeach
                </ul>
            @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="text-muted">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                    </div>
                    <h5 class="text-muted">{{ translate('No conversations found') }}</h5>
                    <p class="text-muted">{{ translate('Start a new conversation with your customers') }}</p>
                </div>
            @endif
        </div>
    </div>

    {{-- 分页 --}}
    <div class="aiz-pagination mt-3">
        {{ $conversations->links() }}
    </div>

    {{-- 自动排序JavaScript --}}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const conversationList = document.querySelector('.conversation-list');
            
            if (!conversationList) return;
            
            // 获取所有对话项
            const items = Array.from(conversationList.querySelectorAll('li.list-group-item'));
            
            // 排序逻辑: 1. 未读消息优先 2. 最新消息靠前
            items.sort((a, b) => {
                // 首先按未读状态排序
                const aHasUnread = a.querySelector('.unread-count') !== null;
                const bHasUnread = b.querySelector('.unread-count') !== null;
                
                if (aHasUnread && !bHasUnread) return -1;
                if (!aHasUnread && bHasUnread) return 1;
                
                // 如果未读状态相同，则按时间排序（假设时间在消息元素中有标记）
                const aTime = a.querySelector('.fs-12.text-muted')?.textContent.trim() || '';
                const bTime = b.querySelector('.fs-12.text-muted')?.textContent.trim() || '';
                
                // 转换时间格式（假设是YYYY-MM-DD HH:MM格式）
                const aDate = aTime ? new Date(aTime) : new Date(0);
                const bDate = bTime ? new Date(bTime) : new Date(0);
                
                // 时间倒序，最新的在前
                return bDate - aDate;
            });
            
            // 重新添加排序后的元素
            items.forEach(item => {
                conversationList.appendChild(item);
            });
            
            // 更新边框样式
            items.forEach((item, index) => {
                if (index === 0) {
                    item.classList.remove('border-top-item');
                } else {
                    item.classList.add('border-top-item');
                }
            });
        });
    </script>

    <style>
        /* 基础样式 */
        .avatar-md {
            width: 50px;
            height: 50px;
        }
        
        .img-fit {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .conv-item {
            transition: all 0.2s ease;
            border-left: 3px solid transparent !important;
        }
        
        .conv-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
            border-left-color: #007aff !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        /* 顶部分隔线 */
        .border-top-item {
            border-top: 1px solid rgba(0, 0, 0, 0.125) !important;
        }
        
        /* 列表样式 */
        .conversation-list {
            margin-bottom: 0;
        }
        
        .conversation-list .list-group-item:last-child {
            border-bottom: 0;
        }
        
        /* 手机适配布局 */
        .mobile-conversation {
            display: grid;
            grid-template-columns: auto 1fr auto; /* 确认三列布局 */
            grid-gap: 15px;
            align-items: start; /* 改为顶部对齐 */
            width: 100%;
        }
        
        /* 头像与用户信息 */
        .avatar-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 90px;
        }
        
        /* 中间内容区 */
        .conversation-middle {
            min-width: 0;
            padding-right: 10px;
        }
        
        /* 产品标题和消息指示器 */
        .product-header {
            margin-right: 10px;
        }
        
        .product-title {
            font-size: 16px;
            line-height: 1.3;
            word-break: break-word;
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            max-height: 2.6em;
        }
        
        /* 消息指示器设计 */
        .message-indicators {
            font-size: 13px;
            gap: 12px;
        }
        
        .message-count, .unread-count {
            display: inline-flex;
            align-items: center;
            height: 22px;
            position: relative;
            padding-right: 2px;
        }
        
        /* 分隔线样式 */
        .separator {
            display: inline-block;
            position: absolute;
            right: -8px;
            top: 2px;
            height: 18px;
            width: 1px;
            background-color: #e0e0e0;
        }
        
        .message-count:after {
            display: none;
        }
        
        .count-badge, .unread-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 22px;
            height: 22px;
            border-radius: 11px;
            padding: 0 6px;
            font-weight: 600;
            font-size: 12px;
            margin-right: 4px;
        }
        
        .count-badge {
            background-color: #e9ecef;
            color: #495057;
        }
        
        .unread-badge {
            background-color: #ff3b30;
            color: white;
        }
        
        .count-label, .unread-label {
            color: #6c757d;
            font-size: 13px;
        }
        
        /* 脉冲动画效果 */
        .pulse {
            position: relative;
        }
        
        .pulse:before {
            content: '';
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: rgba(255, 59, 48, 0.6);
            border-radius: 50%;
            animation: pulse-animation 1.5s cubic-bezier(0.24, 0, 0.38, 1) infinite;
            z-index: -1;
        }
        
        @keyframes pulse-animation {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.4);
                opacity: 0;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }
        
        /* 移动设备优化 */
        @media (max-width: 767px) {
            .message-indicators {
                width: 100%;
            }
        }
        
        /* 产品图片 */
        .product-img-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 按钮样式 */
        .btn-info {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: #fff;
        }
        
        .btn-info:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
            color: #fff;
        }
        
        /* 辅助类 */
        .fs-12 {
            font-size: 12px;
        }
        
        .fs-14 {
            font-size: 14px;
        }
        
        /* 平板与手机适配 */
        @media (max-width: 991px) {
            .mobile-conversation {
                grid-template-columns: auto 1fr; /* 平板两列 */
                grid-template-rows: auto auto; /* 调整为两行 */
                align-items: start;
            }
            
            .conversation-middle {
                padding-right: 0;
            }
            
            .product-info-wrapper {
                grid-column: 1 / 3; /* 产品信息块占满第二行 */
                grid-row: 2;
                flex-direction: row; /* 水平排列 */
                justify-content: space-between; /* 两端对齐 */
                align-items: center;
                margin-top: 10px;
                width: 100%; /* 宽度占满 */
            }

            .product-info-wrapper .btn {
                margin-top: 0 !important;
                margin-left: 10px;
            }
        }
        
        /* 小型手机适配 */
        @media (max-width: 575px) {
            .mobile-conversation {
                grid-template-columns: 1fr; /* 手机单列 */
                grid-template-rows: auto auto auto; /* 三行 */
                grid-gap: 10px;
            }
            
            .avatar-container {
                flex-direction: row;
                width: 100%;
                justify-content: flex-start;
            }
            
            .avatar-container > div:first-child {
                margin-right: 15px;
            }
            
            .avatar-container .mt-2,
            .user-info {
                margin-top: 0 !important;
                text-align: left !important;
            }
            
            .user-info .text-truncate {
                max-width: 150px !important;
            }
            
            .conversation-middle {
                grid-row: 2;
            }
            
            .product-info-wrapper {
                grid-column: 1; /* 产品信息块占第三行 */
                grid-row: 3;
                flex-direction: row; /* 水平排列 */
                justify-content: space-between;
                align-items: center;
                margin-top: 10px;
                width: 100%;
            }
        }
        
        /* 徽标容器样式 */
        .badges-wrapper {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 5px;
        }
        
        .text-badge {
            border-radius: 4px;
            padding: 3px 8px;
            font-weight: normal;
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
        }
        
        .text-badge i {
            margin-right: 3px;
            font-size: 14px;
        }
        
        /* 最近消息样式 */
        .recent-messages {
            margin-bottom: 8px;
        }
        
        .message-preview {
            margin-bottom: 3px;
            font-size: 13px;
            line-height: 1.4;
            position: relative;
        }
        
        .message-preview:last-child {
            margin-bottom: 0;
        }
        
        .message-content {
            display: inline;
        }
        
        .message-sender {
            font-weight: 600;
            color: #495057;
            margin-right: 4px;
        }
        
        .message-text {
            color: #6c757d;
            word-break: break-word;
        }
        
        .message-time {
            color: #adb5bd;
            font-size: 11px;
            margin-left: 4px;
        }
        
        .message-secondary {
            opacity: 0.8;
            font-size: 12px;
        }
        
        .message-secondary .message-sender {
            font-weight: normal;
        }
        
        /* 添加与详情页一致的产品信息样式 */
        .product-info-wrapper {
            /* 保留之前的样式 */
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.02);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            width: fit-content; /* 确保宽度适应内容 */
            margin-top: 0; /* 移除或重置可能存在的上边距 */
        }
        
        .product-image {
            /* 保留之前的样式 */
            width: 70px;
            height: 70px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            border: 2px solid #fff;
        }
        
        .conversation-participants {
            /* 保留之前的样式 */
            opacity: 0.85;
            font-weight: normal;
        }
        
        .fw-500 {
             /* 保留之前的样式 */
            font-weight: 500;
        }
    </style>
@endsection

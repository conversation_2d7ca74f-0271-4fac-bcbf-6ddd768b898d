@extends('seller.layouts.app')

@section('panel_content')
<div class="aiz-titlebar text-left mt-2 mb-3">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">{{ translate('Admin Support Chat') }}</h1>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0 h6">{{ translate('客服服务') }}</h5>
        <small class="text-muted">{{ translate('与管理员团队实时沟通') }}</small>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <!-- Support Board 聊天容器 -->
                <div id="sb-chat-container" style="min-height: 600px;">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-3">正在加载聊天系统...</p>
                    </div>
                </div>
                
                <!-- 备用聊天界面 -->
                <div id="fallback-chat" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        如果聊天窗口没有显示，请点击页面右下角的聊天按钮，或者 <a href="javascript:void(0)" onclick="openSupportBoard()">点击这里打开聊天</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Support Board 样式覆盖 */
.sb-chat {
    position: relative !important;
    width: 100% !important;
    height: 600px !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
}

.sb-chat-btn {
    display: block !important;
    position: relative !important;
    bottom: auto !important;
    right: auto !important;
    margin: 20px auto !important;
}

/* 强制显示聊天界面 */
#sb-chat {
    display: block !important;
    visibility: visible !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Support Board 配置
    window.SB_INIT_USER = {
        id: {{ auth()->id() }},
        first_name: '{{ explode(" ", auth()->user()->name)[0] ?? auth()->user()->name }}',
        last_name: '{{ explode(" ", auth()->user()->name, 2)[1] ?? "" }}',
        email: '{{ auth()->user()->email }}',
        user_type: 'seller'
    };
    
    // Support Board 初始化配置
    window.SB_INIT = {
        init: true,
        open: true,
        embed: true,
        container: '#sb-chat-container'
    };
    
    // 加载 Support Board
    loadSupportBoard();
});

function loadSupportBoard() {
    // 检查 Support Board 是否已经加载
    if (typeof SBF !== 'undefined') {
        initializeSupportBoard();
        return;
    }
    
    // 动态加载 Support Board 脚本
    const script = document.createElement('script');
    script.src = '{{ url("/chat/js/main.js") }}';
    script.onload = function() {
        console.log('Support Board script loaded');
        setTimeout(initializeSupportBoard, 1000);
    };
    script.onerror = function() {
        console.error('Failed to load Support Board script');
        showFallback();
    };
    document.head.appendChild(script);
    
    // 5秒后如果还没加载成功，显示备用界面
    setTimeout(function() {
        if (typeof SBF === 'undefined') {
            showFallback();
        }
    }, 5000);
}

function initializeSupportBoard() {
    try {
        if (typeof SBF !== 'undefined') {
            // 初始化 Support Board
            SBF.init();
            
            // 强制显示聊天界面
            setTimeout(function() {
                if (SBF.chat) {
                    SBF.chat.open();
                    
                    // 如果有嵌入容器，将聊天移动到容器中
                    const chatElement = document.querySelector('#sb-chat');
                    const container = document.querySelector('#sb-chat-container');
                    if (chatElement && container) {
                        container.innerHTML = '';
                        container.appendChild(chatElement);
                        chatElement.style.display = 'block';
                        chatElement.style.position = 'relative';
                    }
                }
            }, 1500);
            
            console.log('Support Board initialized successfully');
        } else {
            throw new Error('SBF not available');
        }
    } catch (error) {
        console.error('Support Board initialization failed:', error);
        showFallback();
    }
}

function showFallback() {
    document.getElementById('sb-chat-container').style.display = 'none';
    document.getElementById('fallback-chat').style.display = 'block';
}

function openSupportBoard() {
    if (typeof SBF !== 'undefined' && SBF.chat) {
        SBF.chat.open();
    } else {
        // 重新尝试加载
        loadSupportBoard();
    }
}

// 全局函数，供外部调用
window.openSupportBoard = openSupportBoard;
</script>

<!-- Support Board CSS -->
<link rel="stylesheet" href="{{ url('/chat/css/main.css') }}">

@endsection

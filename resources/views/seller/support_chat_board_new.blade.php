@extends('seller.layouts.app')

@section('panel_content')
<div class="aiz-titlebar text-left mt-2 mb-3">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">{{ translate('Support Chat') }}</h1>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0 h6">{{ translate('Customer Support') }}</h5>
        <small class="text-muted">{{ translate('Chat with our support team') }}</small>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6>{{ translate('Welcome to Support Chat') }}</h6>
            <p>{{ translate('Our support team is here to help you. The chat will appear below.') }}</p>
        </div>
        
        <!-- Support Board 聊天界面容器 -->
        <div id="support-board-container" style="min-height: 600px; position: relative;">
            <div class="text-center py-5" id="loading-indicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">{{ translate('Loading...') }}</span>
                </div>
                <p class="mt-3">{{ translate('Loading support chat...') }}</p>
            </div>
        </div>
        
        <!-- 备用简单聊天界面 -->
        <div id="fallback-chat" style="display: none;">
            <div class="alert alert-warning">
                <h6>{{ translate('Simple Chat Mode') }}</h6>
                <p>{{ translate('Support Board is not available. Using simple chat mode.') }}</p>
            </div>
            
            <div class="chat-messages" id="chat-messages" style="height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; background-color: #f8f9fa;">
                <div class="message admin-message" style="margin-bottom: 15px;">
                    <div style="background-color: #e3f2fd; color: #333; padding: 10px; border-radius: 10px; max-width: 70%;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">Support Team</div>
                        <div>{{ translate('Hello! How can we help you today?') }}</div>
                        <div style="font-size: 11px; color: #999; margin-top: 5px; text-align: right;">
                            {{ now()->format('H:i') }}
                        </div>
                    </div>
                </div>
            </div>
            
            <form id="simple-chat-form" class="d-flex">
                <input type="text" id="simple-message-input" class="form-control" placeholder="{{ translate('Type your message...') }}" required>
                <button type="submit" class="btn btn-primary ml-2">{{ translate('Send') }}</button>
            </form>
        </div>
    </div>
</div>

<!-- Support Board 用户配置 -->
<script>
// 设置Support Board用户信息
var SB_INIT_USER = {
    id: '{{ Auth::id() }}',
    first_name: '{{ Auth::user()->name }}',
    last_name: '',
    email: '{{ Auth::user()->email }}',
    user_type: 'seller',
    extra: {
        seller_id: '{{ Auth::id() }}',
        user_type: 'seller',
        platform: 'active_ecommerce'
    }
};

// Support Board URL配置
var SB_INIT_URL = '{{ $supportBoardUrl ?? url("/chat") }}';

// 强制聊天显示在容器内而不是弹窗
var SB_INIT_SETTINGS = {
    chat_manual_init: true,
    chat_login_init: false,
    chat_position: 'relative'
};
</script>

<!-- 加载jQuery（如果尚未加载） -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- 加载Support Board主脚本 -->
<script id="sbinit" src="{{ $supportBoardUrl ?? url('/chat') }}/js/main.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let supportBoardLoaded = false;
    let fallbackInitialized = false;
    
    // 检查Support Board是否加载成功
    function checkSupportBoardLoaded() {
        if (typeof SBF !== 'undefined' && SBF.chat) {
            supportBoardLoaded = true;
            console.log('Support Board loaded successfully');
            
            // 隐藏加载指示器
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            // 初始化Support Board聊天
            try {
                SBF.chat.init();
                SBF.chat.open();
            } catch (e) {
                console.error('Error initializing Support Board:', e);
                showFallbackChat();
            }
        } else {
            console.warn('Support Board not loaded, checking again...');
            setTimeout(checkSupportBoardLoaded, 1000);
        }
    }
    
    function showFallbackChat() {
        if (fallbackInitialized) return;
        
        console.warn('Using fallback chat');
        document.getElementById('loading-indicator').style.display = 'none';
        document.getElementById('fallback-chat').style.display = 'block';
        initFallbackChat();
        fallbackInitialized = true;
    }
    
    function initFallbackChat() {
        const form = document.getElementById('simple-chat-form');
        const messageInput = document.getElementById('simple-message-input');
        const messagesContainer = document.getElementById('chat-messages');
        
        if (!form || !messageInput || !messagesContainer) return;
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message) return;
            
            // 添加用户消息
            addSimpleMessage(message, true);
            messageInput.value = '';
            
            // 发送到后端
            const formData = new FormData();
            formData.append('message', message);
            formData.append('_token', '{{ csrf_token() }}');
            
            fetch('{{ route("seller.support_chat.send") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    setTimeout(() => {
                        addSimpleMessage('{{ translate("Thank you for your message. Our support team will review it and respond soon.") }}', false);
                    }, 1000);
                } else {
                    addSimpleMessage('{{ translate("Error") }}: ' + data.error, false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addSimpleMessage('{{ translate("Failed to send message. Please try again.") }}', false);
            });
        });
    }
    
    function addSimpleMessage(message, isUser) {
        const messagesContainer = document.getElementById('chat-messages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'admin-message'}`;
        messageDiv.style.marginBottom = '15px';
        
        const now = new Date();
        const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                       now.getMinutes().toString().padStart(2, '0');
        
        messageDiv.innerHTML = `
            <div style="background-color: ${isUser ? '#007bff' : '#e3f2fd'}; 
                        color: ${isUser ? 'white' : '#333'}; 
                        padding: 10px; border-radius: 10px; max-width: 70%; 
                        margin-${isUser ? 'right' : 'left'}: auto;">
                <div style="font-size: 12px; color: ${isUser ? '#ccc' : '#666'}; margin-bottom: 5px;">
                    ${isUser ? '{{ translate("You") }}' : '{{ translate("Support Team") }}'}
                </div>
                <div>${message}</div>
                <div style="font-size: 11px; color: ${isUser ? '#ccc' : '#999'}; margin-top: 5px; text-align: right;">
                    ${timeStr}
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // 开始检查Support Board
    setTimeout(checkSupportBoardLoaded, 1000);
    
    // 如果10秒后仍未加载，使用备用聊天
    setTimeout(function() {
        if (!supportBoardLoaded && !fallbackInitialized) {
            showFallbackChat();
        }
    }, 10000);
});
</script>

<style>
/* Support Board 样式调整 */
.sb-chat {
    position: relative !important;
    width: 100% !important;
    height: 600px !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: none !important;
}

.sb-chat-btn {
    display: none !important;
}

#support-board-container .sb-chat {
    margin: 0 !important;
}
</style>
@endsection

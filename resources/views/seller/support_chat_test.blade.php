@extends('seller.layouts.app')

@section('panel_content')
<div class="aiz-titlebar text-left mt-2 mb-3">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">Support Chat Test</h1>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0 h6">Support Chat Test Page</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-success">
            <h6>✅ Support Chat System Status</h6>
            <p>This page loaded successfully! The basic routing and view system is working.</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>User Information</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>User ID:</strong> {{ Auth::id() }}</p>
                        <p><strong>Name:</strong> {{ Auth::user()->name }}</p>
                        <p><strong>Email:</strong> {{ Auth::user()->email }}</p>
                        <p><strong>User Type:</strong> {{ Auth::user()->user_type }}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>Available Routes</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="{{ route('seller.support.test') }}" class="list-group-item list-group-item-action">
                                Basic Test (JSON)
                            </a>
                            <a href="{{ route('seller.support.chat.simple') }}" class="list-group-item list-group-item-action">
                                Simple Chat View
                            </a>
                            <a href="{{ route('seller.support.chat') }}" class="list-group-item list-group-item-action">
                                Full Support Board
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h6>Simple Message Test</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Test Message:</label>
                        <input type="text" id="test-message" class="form-control" placeholder="Type a test message...">
                    </div>
                    <button type="button" id="send-test" class="btn btn-primary">Send Test Message</button>
                    <button type="button" id="debug-routes" class="btn btn-secondary ml-2">Debug Routes</button>
                    <div id="test-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sendButton = document.getElementById('send-test');
    const debugButton = document.getElementById('debug-routes');
    const messageInput = document.getElementById('test-message');
    const resultDiv = document.getElementById('test-result');
    
    sendButton.addEventListener('click', function() {
        const message = messageInput.value.trim();
        if (!message) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Please enter a message</div>';
            return;
        }
        
        resultDiv.innerHTML = '<div class="alert alert-info">Sending message...</div>';
        
        const formData = new FormData();
        formData.append('message', message);
        formData.append('_token', '{{ csrf_token() }}');
        
        fetch('{{ route("seller.support.send.test") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = '<div class="alert alert-success">✅ Message sent successfully!</div>';
                messageInput.value = '';
            } else {
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<div class="alert alert-danger">❌ Network error: ' + error.message + '</div>';
        });
    });
    
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendButton.click();
        }
    });

    // Debug button to test route availability
    debugButton.addEventListener('click', function() {
        resultDiv.innerHTML = '<div class="alert alert-info">Testing routes...</div>';

        fetch('{{ route("seller.support.test") }}', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.innerHTML = '<div class="alert alert-success"><h6>✅ Routes Test Result:</h6><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
        })
        .catch(error => {
            console.error('Debug error:', error);
            resultDiv.innerHTML = '<div class="alert alert-danger">❌ Debug error: ' + error.message + '</div>';
        });
    });
});
</script>
@endsection

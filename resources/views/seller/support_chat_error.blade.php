@extends('seller.layouts.app')

@section('panel_content')
<div class="aiz-titlebar text-left mt-2 mb-3">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h1 class="h3">{{ translate('Support Chat') }}</h1>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0 h6">{{ translate('Support Chat Error') }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>{{ translate('Error') }}:</strong> {{ $error }}
        </div>
        
        @if($details)
        <div class="alert alert-warning">
            <strong>{{ translate('Technical Details') }}:</strong>
            <pre>{{ $details }}</pre>
        </div>
        @endif
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>{{ translate('Alternative Options') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="{{ route('seller.support.chat.simple') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-comments"></i> {{ translate('Simple Chat Interface') }}
                            </a>
                            <a href="{{ route('seller.support.test') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-cog"></i> {{ translate('Test Support System') }}
                            </a>
                            @if (get_setting('platform_customer_service_link'))
                            <a href="{{ get_setting('platform_customer_service_link') }}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-external-link-alt"></i> {{ translate('External Customer Service') }}
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>{{ translate('Contact Information') }}</h6>
                    </div>
                    <div class="card-body">
                        <p>{{ translate('If you need immediate assistance, please contact us through:') }}</p>
                        <ul>
                            <li>{{ translate('Email') }}: <EMAIL></li>
                            <li>{{ translate('Phone') }}: ******-567-8900</li>
                            <li>{{ translate('Business Hours') }}: 9:00 AM - 6:00 PM</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <button onclick="location.reload()" class="btn btn-primary">
                <i class="fas fa-redo"></i> {{ translate('Try Again') }}
            </button>
            <a href="{{ route('seller.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-home"></i> {{ translate('Back to Dashboard') }}
            </a>
        </div>
    </div>
</div>
@endsection
